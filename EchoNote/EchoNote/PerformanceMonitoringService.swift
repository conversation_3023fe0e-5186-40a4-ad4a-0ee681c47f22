//
//  PerformanceMonitoringService.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import UIKit
import os.log

// MARK: - Performance Metrics

struct PerformanceMetrics {
    let timestamp: Date
    let memoryUsage: MemoryUsage
    let cpuUsage: Double
    let batteryLevel: Float
    let thermalState: ProcessInfo.ThermalState
    let audioLatency: TimeInterval?
    let frameRate: Double?
    let appLaunchTime: TimeInterval?
    
    struct MemoryUsage {
        let used: UInt64
        let available: UInt64
        let total: UInt64
        
        var usagePercentage: Double {
            return Double(used) / Double(total) * 100
        }
    }
}

// MARK: - Performance Event

struct PerformanceEvent: Codable, Identifiable {
    let id = UUID()
    let timestamp: Date
    let type: EventType
    let duration: TimeInterval
    let metadata: [String: String]
    
    enum EventType: String, Codable, CaseIterable {
        case appLaunch = "app_launch"
        case audioRecordingStart = "audio_recording_start"
        case audioRecordingStop = "audio_recording_stop"
        case audioPlaybackStart = "audio_playback_start"
        case audioPlaybackStop = "audio_playback_stop"
        case speechRecognition = "speech_recognition"
        case keywordDetection = "keyword_detection"
        case dataLoad = "data_load"
        case dataSave = "data_save"
        case viewLoad = "view_load"
        case memoryWarning = "memory_warning"
        case thermalStateChange = "thermal_state_change"
    }
}

// MARK: - Performance Monitoring Service

@MainActor
class PerformanceMonitoringService: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentMetrics: PerformanceMetrics?
    @Published var recentEvents: [PerformanceEvent] = []
    @Published var isMonitoring = false
    
    // MARK: - Properties
    
    private var metricsTimer: Timer?
    private var eventHistory: [PerformanceEvent] = []
    private let maxEventHistory = 1000
    private var appLaunchTime: Date?
    private var frameRateMonitor: CADisplayLink?
    private var frameCount = 0
    private var lastFrameTime: CFTimeInterval = 0
    
    // MARK: - Singleton
    
    static let shared = PerformanceMonitoringService()
    
    private init() {
        setupNotifications()
    }
    
    // MARK: - Monitoring Control
    
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        appLaunchTime = Date()
        
        // Start metrics collection
        metricsTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.collectMetrics()
            }
        }
        
        // Start frame rate monitoring
        startFrameRateMonitoring()
        
        LoggingService.shared.info("Performance monitoring started", category: .performance)
    }
    
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        metricsTimer?.invalidate()
        metricsTimer = nil
        
        stopFrameRateMonitoring()
        
        LoggingService.shared.info("Performance monitoring stopped", category: .performance)
    }
    
    // MARK: - Metrics Collection
    
    private func collectMetrics() {
        let memoryUsage = getMemoryUsage()
        let cpuUsage = getCPUUsage()
        let batteryLevel = UIDevice.current.batteryLevel
        let thermalState = ProcessInfo.processInfo.thermalState
        
        let metrics = PerformanceMetrics(
            timestamp: Date(),
            memoryUsage: memoryUsage,
            cpuUsage: cpuUsage,
            batteryLevel: batteryLevel,
            thermalState: thermalState,
            audioLatency: nil, // Will be set during audio operations
            frameRate: getCurrentFrameRate(),
            appLaunchTime: appLaunchTime?.timeIntervalSinceNow
        )
        
        currentMetrics = metrics
        
        // Log performance warnings
        checkPerformanceThresholds(metrics)
    }
    
    private func getMemoryUsage() -> PerformanceMetrics.MemoryUsage {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let used = UInt64(info.resident_size)
            let total = UInt64(ProcessInfo.processInfo.physicalMemory)
            let available = total - used
            
            return PerformanceMetrics.MemoryUsage(used: used, available: available, total: total)
        } else {
            return PerformanceMetrics.MemoryUsage(used: 0, available: 0, total: 0)
        }
    }
    
    private func getCPUUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.user_time.seconds + info.system_time.seconds)
        } else {
            return 0.0
        }
    }
    
    // MARK: - Frame Rate Monitoring
    
    private func startFrameRateMonitoring() {
        frameRateMonitor = CADisplayLink(target: self, selector: #selector(frameRateUpdate))
        frameRateMonitor?.add(to: .main, forMode: .common)
    }
    
    private func stopFrameRateMonitoring() {
        frameRateMonitor?.invalidate()
        frameRateMonitor = nil
    }
    
    @objc private func frameRateUpdate(displayLink: CADisplayLink) {
        frameCount += 1
        
        if lastFrameTime == 0 {
            lastFrameTime = displayLink.timestamp
        }
        
        let elapsed = displayLink.timestamp - lastFrameTime
        if elapsed >= 1.0 {
            let fps = Double(frameCount) / elapsed
            frameCount = 0
            lastFrameTime = displayLink.timestamp
            
            // Store frame rate for metrics
            // This will be picked up by the next metrics collection
        }
    }
    
    private func getCurrentFrameRate() -> Double? {
        // Return the last calculated frame rate
        return frameRateMonitor != nil ? 60.0 : nil // Placeholder
    }
    
    // MARK: - Event Tracking
    
    func trackEvent(_ type: PerformanceEvent.EventType, duration: TimeInterval = 0, metadata: [String: String] = [:]) {
        let event = PerformanceEvent(
            timestamp: Date(),
            type: type,
            duration: duration,
            metadata: metadata
        )
        
        eventHistory.append(event)
        recentEvents.insert(event, at: 0)
        
        // Limit history size
        if eventHistory.count > maxEventHistory {
            eventHistory = Array(eventHistory.suffix(maxEventHistory))
        }
        
        if recentEvents.count > 50 {
            recentEvents = Array(recentEvents.prefix(50))
        }
        
        LoggingService.shared.info("Performance event: \(type.rawValue)", category: .performance, metadata: [
            "duration": String(duration),
            "metadata": metadata.description
        ])
    }
    
    func measurePerformance<T>(of operation: () throws -> T, eventType: PerformanceEvent.EventType, metadata: [String: String] = [:]) rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try operation()
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        
        trackEvent(eventType, duration: duration, metadata: metadata)
        return result
    }
    
    func measureAsyncPerformance<T>(of operation: () async throws -> T, eventType: PerformanceEvent.EventType, metadata: [String: String] = [:]) async rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try await operation()
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        
        trackEvent(eventType, duration: duration, metadata: metadata)
        return result
    }
    
    // MARK: - Performance Analysis
    
    private func checkPerformanceThresholds(_ metrics: PerformanceMetrics) {
        // Memory usage warning
        if metrics.memoryUsage.usagePercentage > 80 {
            LoggingService.shared.warning("High memory usage: \(String(format: "%.1f", metrics.memoryUsage.usagePercentage))%", category: .performance)
            trackEvent(.memoryWarning, metadata: ["usage": String(format: "%.1f", metrics.memoryUsage.usagePercentage)])
        }
        
        // Thermal state warning
        if metrics.thermalState != .nominal {
            LoggingService.shared.warning("Thermal state: \(metrics.thermalState)", category: .performance)
            trackEvent(.thermalStateChange, metadata: ["state": String(describing: metrics.thermalState)])
        }
        
        // Low battery warning
        if metrics.batteryLevel < 0.2 && metrics.batteryLevel > 0 {
            LoggingService.shared.warning("Low battery: \(String(format: "%.0f", metrics.batteryLevel * 100))%", category: .performance)
        }
        
        // Frame rate warning
        if let frameRate = metrics.frameRate, frameRate < 30 {
            LoggingService.shared.warning("Low frame rate: \(String(format: "%.1f", frameRate)) FPS", category: .performance)
        }
    }
    
    func getPerformanceReport() -> PerformanceReport {
        let eventsByType = Dictionary(grouping: eventHistory) { $0.type }
        let averageDurations = eventsByType.mapValues { events in
            events.map { $0.duration }.reduce(0, +) / Double(events.count)
        }
        
        return PerformanceReport(
            totalEvents: eventHistory.count,
            eventsByType: eventsByType.mapValues { $0.count },
            averageDurations: averageDurations,
            currentMetrics: currentMetrics,
            recentEvents: Array(recentEvents.prefix(20))
        )
    }
    
    // MARK: - Notifications
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(didReceiveMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(thermalStateDidChange),
            name: ProcessInfo.thermalStateDidChangeNotification,
            object: nil
        )
    }
    
    @objc private func didReceiveMemoryWarning() {
        trackEvent(.memoryWarning)
        LoggingService.shared.warning("Memory warning received", category: .performance)
    }
    
    @objc private func thermalStateDidChange() {
        let state = ProcessInfo.processInfo.thermalState
        trackEvent(.thermalStateChange, metadata: ["state": String(describing: state)])
        LoggingService.shared.warning("Thermal state changed to: \(state)", category: .performance)
    }
}

// MARK: - Performance Report

struct PerformanceReport {
    let totalEvents: Int
    let eventsByType: [PerformanceEvent.EventType: Int]
    let averageDurations: [PerformanceEvent.EventType: TimeInterval]
    let currentMetrics: PerformanceMetrics?
    let recentEvents: [PerformanceEvent]
}
