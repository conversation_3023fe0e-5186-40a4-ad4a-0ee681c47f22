import Foundation
import AVFoundation
import Speech
import UserNotifications
import UIKit

// MARK: - Permission Types

enum PermissionType: String, CaseIterable {
    case microphone = "microphone"
    case speechRecognition = "speechRecognition"
    case notifications = "notifications"
    
    var displayName: String {
        switch self {
        case .microphone:
            return "Microphone"
        case .speechRecognition:
            return "Speech Recognition"
        case .notifications:
            return "Notifications"
        }
    }
    
    var description: String {
        switch self {
        case .microphone:
            return "Required to record voice notes and capture audio"
        case .speechRecognition:
            return "Required to convert speech to text for note creation"
        case .notifications:
            return "Optional for reminders and app notifications"
        }
    }
    
    var isRequired: Bool {
        switch self {
        case .microphone, .speechRecognition:
            return true
        case .notifications:
            return false
        }
    }
}

enum PermissionStatus {
    case notDetermined
    case granted
    case denied
    case restricted
    case unknown
    
    var isGranted: Bool {
        return self == .granted
    }
    
    var displayText: String {
        switch self {
        case .notDetermined:
            return "Not Requested"
        case .granted:
            return "Granted"
        case .denied:
            return "Denied"
        case .restricted:
            return "Restricted"
        case .unknown:
            return "Unknown"
        }
    }
}

// MARK: - Permission Request Result

struct PermissionRequestResult {
    let permission: PermissionType
    let status: PermissionStatus
    let wasGranted: Bool
    let error: Error?
    
    init(permission: PermissionType, status: PermissionStatus, error: Error? = nil) {
        self.permission = permission
        self.status = status
        self.wasGranted = status.isGranted
        self.error = error
    }
}

// MARK: - Permissions Service

@MainActor
class PermissionsService: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var microphoneStatus: PermissionStatus = .notDetermined
    @Published var speechRecognitionStatus: PermissionStatus = .notDetermined
    @Published var notificationsStatus: PermissionStatus = .notDetermined
    
    @Published var allRequiredPermissionsGranted: Bool = false
    @Published var hasRequestedPermissions: Bool = false
    
    // MARK: - Private Properties
    
    private var permissionStatusCache: [PermissionType: PermissionStatus] = [:]
    private let userDefaults = UserDefaults.standard
    
    // MARK: - Initialization
    
    init() {
        loadPermissionStates()
        updateAllRequiredPermissionsStatus()
    }
    
    // MARK: - Public API
    
    func checkAllPermissions() async {
        await withTaskGroup(of: Void.self) { group in
            group.addTask { await self.checkMicrophonePermission() }
            group.addTask { await self.checkSpeechRecognitionPermission() }
            group.addTask { await self.checkNotificationsPermission() }
        }
        
        updateAllRequiredPermissionsStatus()
        savePermissionStates()
    }
    
    func requestAllRequiredPermissions() async -> [PermissionRequestResult] {
        var results: [PermissionRequestResult] = []
        
        // Request microphone permission first
        let micResult = await requestMicrophonePermission()
        results.append(micResult)
        
        // Only request speech recognition if microphone was granted
        if micResult.wasGranted {
            let speechResult = await requestSpeechRecognitionPermission()
            results.append(speechResult)
        }
        
        hasRequestedPermissions = true
        updateAllRequiredPermissionsStatus()
        savePermissionStates()
        
        return results
    }
    
    func requestPermission(_ type: PermissionType) async -> PermissionRequestResult {
        switch type {
        case .microphone:
            return await requestMicrophonePermission()
        case .speechRecognition:
            return await requestSpeechRecognitionPermission()
        case .notifications:
            return await requestNotificationsPermission()
        }
    }
    
    func getPermissionStatus(_ type: PermissionType) -> PermissionStatus {
        switch type {
        case .microphone:
            return microphoneStatus
        case .speechRecognition:
            return speechRecognitionStatus
        case .notifications:
            return notificationsStatus
        }
    }
    
    func openAppSettings() {
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else { return }
        
        if UIApplication.shared.canOpenURL(settingsUrl) {
            UIApplication.shared.open(settingsUrl)
        }
    }
    
    // MARK: - Individual Permission Requests
    
    private func requestMicrophonePermission() async -> PermissionRequestResult {
        return await withCheckedContinuation { continuation in
            DispatchQueue.main.async {
                AVAudioSession.sharedInstance().requestRecordPermission { [weak self] granted in
                    Task { @MainActor in
                        let status: PermissionStatus = granted ? .granted : .denied
                        self?.microphoneStatus = status
                        self?.permissionStatusCache[.microphone] = status

                        let result = PermissionRequestResult(permission: .microphone, status: status)
                        continuation.resume(returning: result)
                    }
                }
            }
        }
    }
    
    private func requestSpeechRecognitionPermission() async -> PermissionRequestResult {
        return await withCheckedContinuation { continuation in
            DispatchQueue.main.async {
                SFSpeechRecognizer.requestAuthorization { [weak self] authStatus in
                    Task { @MainActor in
                        let status = self?.convertSpeechAuthStatus(authStatus) ?? .unknown
                        self?.speechRecognitionStatus = status
                        self?.permissionStatusCache[.speechRecognition] = status

                        let result = PermissionRequestResult(permission: .speechRecognition, status: status)
                        continuation.resume(returning: result)
                    }
                }
            }
        }
    }
    
    private func requestNotificationsPermission() async -> PermissionRequestResult {
        do {
            let granted = try await UNUserNotificationCenter.current().requestAuthorization(
                options: [.alert, .badge, .sound]
            )
            
            let status: PermissionStatus = granted ? .granted : .denied
            notificationsStatus = status
            permissionStatusCache[.notifications] = status
            
            return PermissionRequestResult(permission: .notifications, status: status)
        } catch {
            let result = PermissionRequestResult(permission: .notifications, status: .unknown, error: error)
            return result
        }
    }
    
    // MARK: - Permission Status Checking
    
    private func checkMicrophonePermission() async {
        let authStatus = AVAudioSession.sharedInstance().recordPermission
        let status = convertAudioAuthStatus(authStatus)
        microphoneStatus = status
        permissionStatusCache[.microphone] = status
    }
    
    private func checkSpeechRecognitionPermission() async {
        let authStatus = SFSpeechRecognizer.authorizationStatus()
        let status = convertSpeechAuthStatus(authStatus)
        speechRecognitionStatus = status
        permissionStatusCache[.speechRecognition] = status
    }
    
    private func checkNotificationsPermission() async {
        let settings = await UNUserNotificationCenter.current().notificationSettings()
        let status = convertNotificationAuthStatus(settings.authorizationStatus)
        notificationsStatus = status
        permissionStatusCache[.notifications] = status
    }
    
    // MARK: - Status Conversion Helpers
    
    private func convertAudioAuthStatus(_ status: AVAudioSession.RecordPermission) -> PermissionStatus {
        switch status {
        case .undetermined:
            return .notDetermined
        case .granted:
            return .granted
        case .denied:
            return .denied
        @unknown default:
            return .unknown
        }
    }
    
    private func convertSpeechAuthStatus(_ status: SFSpeechRecognizerAuthorizationStatus) -> PermissionStatus {
        switch status {
        case .notDetermined:
            return .notDetermined
        case .authorized:
            return .granted
        case .denied:
            return .denied
        case .restricted:
            return .restricted
        @unknown default:
            return .unknown
        }
    }
    
    private func convertNotificationAuthStatus(_ status: UNAuthorizationStatus) -> PermissionStatus {
        switch status {
        case .notDetermined:
            return .notDetermined
        case .authorized:
            return .granted
        case .denied:
            return .denied
        case .provisional:
            return .granted
        case .ephemeral:
            return .granted
        @unknown default:
            return .unknown
        }
    }
    
    // MARK: - Helper Methods
    
    private func updateAllRequiredPermissionsStatus() {
        let requiredPermissions: [PermissionType] = [.microphone, .speechRecognition]
        allRequiredPermissionsGranted = requiredPermissions.allSatisfy { permission in
            getPermissionStatus(permission).isGranted
        }
    }
    
    private func savePermissionStates() {
        userDefaults.set(hasRequestedPermissions, forKey: "hasRequestedPermissions")
        
        for (permission, status) in permissionStatusCache {
            userDefaults.set(status.displayText, forKey: "permission_\(permission.rawValue)")
        }
    }
    
    private func loadPermissionStates() {
        hasRequestedPermissions = userDefaults.bool(forKey: "hasRequestedPermissions")
    }
}
