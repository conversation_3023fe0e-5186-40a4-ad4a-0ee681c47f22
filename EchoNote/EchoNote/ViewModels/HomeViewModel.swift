//
//  HomeViewModel.swift
//  EchoNote
//
//  Created by <PERSON> on 30/6/2025.
//

import SwiftUI
import SwiftData
import Combine

@MainActor
class HomeViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var selectedChartType: ChartType = .pie
    @Published var showingRecordingView = false
    @Published var isBackgroundMonitoringActive = false
    @Published var backgroundMonitoringError: String?
    
    // MARK: - Private Properties
    private let serviceContainer: ServiceContainer
    private var cancellables = Set<AnyCancellable>()
    
    private var recordingCoordinator: RecordingCoordinator {
        serviceContainer.getRecordingCoordinator()
    }
    
    // MARK: - Initialization
    init(serviceContainer: ServiceContainer = ServiceContainer.shared) {
        self.serviceContainer = serviceContainer
        setupBindings()
    }
    
    // MARK: - Public Methods
    
    /// Start manual recording when headphone button is tapped
    func startManualRecording() async {
        await recordingCoordinator.startManualRecording()
        showingRecordingView = true
    }
    
    /// Handle view appearance - check and start background monitoring if needed
    func handleViewAppear(preferences: UserPreferences?) {
        print("🏠 HomeViewModel: View appeared")
        
        guard let preferences = preferences else {
            print("ℹ️ HomeViewModel: No preferences found")
            return
        }
        
        if preferences.backgroundMonitorEnabled {
            if !recordingCoordinator.isBackgroundMonitoringEnabled {
                print("🔄 HomeViewModel: Background monitor should be active but isn't - starting it")
                Task {
                    await recordingCoordinator.startBackgroundMonitoring()
                }
            } else {
                print("✅ HomeViewModel: Background monitor is already active")
            }
        } else {
            print("ℹ️ HomeViewModel: Background monitor is disabled in preferences")
        }
    }
    
    /// Generate chart data from notes and keywords
    func generateChartData(notes: [Note], keywords: [Keyword]) -> [(keyword: String, count: Int, color: String)] {
        let keywordCounts = Dictionary(grouping: notes.flatMap { $0.keywords }) { $0.text }
            .mapValues { $0.count }
        
        return keywords.compactMap { keyword in
            let count = keywordCounts[keyword.text] ?? 0
            guard count > 0 else { return nil }
            return (keyword: keyword.text, count: count, color: keyword.color)
        }.sorted { $0.count > $1.count }
    }
    
    /// Check if notes overview should be shown
    func shouldShowNotesOverview(preferences: UserPreferences?) -> Bool {
        return preferences?.showNotesOverview ?? true
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // Observe recording coordinator state changes
        recordingCoordinator.$currentState
            .sink { [weak self] state in
                self?.updateBackgroundMonitoringStatus(state: state)
            }
            .store(in: &cancellables)
        
        recordingCoordinator.$isBackgroundMonitoringEnabled
            .assign(to: \.isBackgroundMonitoringActive, on: self)
            .store(in: &cancellables)
    }
    
    private func updateBackgroundMonitoringStatus(state: RecordingCoordinator.State) {
        switch state {
        case .listening:
            isBackgroundMonitoringActive = true
            backgroundMonitoringError = nil
            
        case .error(let message):
            isBackgroundMonitoringActive = false
            backgroundMonitoringError = message
            
        case .idle, .recording, .processing:
            // Don't change monitoring status for these states
            break
        }
    }
}


