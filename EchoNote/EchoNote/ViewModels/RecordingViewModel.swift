//
//  RecordingViewModel.swift
//  EchoNote
//
//  Created by <PERSON> on 30/6/2025.
//

import SwiftUI
import SwiftData
import Combine

@MainActor
class RecordingViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var currentState: RecordingCoordinator.State = .idle
    @Published var recordingDuration: TimeInterval = 0
    @Published var transcribedText: String = ""
    @Published var audioLevels: [Float] = []
    @Published var errorMessage: String?
    @Published var isTranscribing: Bool = false
    @Published var transcriptionWords: [String] = []
    @Published var isPaused: Bool = false
    
    // MARK: - Private Properties
    private let serviceContainer: ServiceContainer
    private var cancellables = Set<AnyCancellable>()
    
    private var recordingCoordinator: RecordingCoordinator {
        serviceContainer.getRecordingCoordinator()
    }
    
    private var voiceRecordingService: VoiceRecordingServiceProtocol {
        serviceContainer.getVoiceRecordingService()
    }
    
    // MARK: - Computed Properties
    
    var isRecording: Bool {
        if case .recording = currentState {
            return true
        }
        return false
    }
    
    var isProcessing: Bool {
        if case .processing = currentState {
            return true
        }
        return false
    }
    
    var canStop: Bool {
        return currentState.isActive
    }
    
    var formattedDuration: String {
        let minutes = Int(recordingDuration) / 60
        let seconds = Int(recordingDuration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    // MARK: - Initialization
    init(serviceContainer: ServiceContainer = ServiceContainer.shared) {
        self.serviceContainer = serviceContainer
        setupBindings()
    }
    
    // MARK: - Public Methods
    
    /// Stop the current recording
    func stopRecording() async {
        await recordingCoordinator.stopRecording()
    }
    
    /// Cancel the current recording without saving
    func cancelRecording() async {
        await recordingCoordinator.cancelRecording()
    }
    
    /// Pause the current recording (if supported)
    func pauseRecording() async {
        // For now, we'll simulate pause by stopping
        // In a full implementation, this would pause the audio recording
        isPaused = true
        await stopRecording()
    }

    /// Resume recording (if supported)
    func resumeRecording() async {
        // For now, this would start a new recording
        // In a full implementation, this would resume the paused recording
        isPaused = false
        await recordingCoordinator.startManualRecording()
    }
    
    /// Handle view appearance
    func handleViewAppear() {
        print("🎙️ RecordingViewModel: View appeared")
        // The recording should already be started by the coordinator
        // Just sync the current state
        syncWithCoordinator()
    }
    
    /// Handle view disappearance
    func handleViewDisappear() {
        print("🎙️ RecordingViewModel: View disappeared")
        // Clean up any resources if needed
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // Observe recording coordinator state
        recordingCoordinator.$currentState
            .assign(to: \.currentState, on: self)
            .store(in: &cancellables)
        
        // Observe voice recording service properties
        voiceRecordingService.$recordingDuration
            .assign(to: \.recordingDuration, on: self)
            .store(in: &cancellables)
        
        voiceRecordingService.$transcribedText
            .sink { [weak self] text in
                self?.transcribedText = text
                self?.updateTranscriptionWords(text)
                self?.isTranscribing = !text.isEmpty
            }
            .store(in: &cancellables)
        
        voiceRecordingService.$audioLevels
            .assign(to: \.audioLevels, on: self)
            .store(in: &cancellables)
        
        // Handle state changes
        recordingCoordinator.$currentState
            .sink { [weak self] state in
                self?.handleStateChange(state)
            }
            .store(in: &cancellables)
    }
    
    private func syncWithCoordinator() {
        currentState = recordingCoordinator.currentState
        recordingDuration = voiceRecordingService.recordingDuration
        transcribedText = voiceRecordingService.transcribedText
        audioLevels = voiceRecordingService.audioLevels
    }
    
    private func handleStateChange(_ state: RecordingCoordinator.State) {
        switch state {
        case .error(let message):
            errorMessage = message
            isTranscribing = false

        case .idle:
            errorMessage = nil
            isTranscribing = false
            transcribedText = ""
            transcriptionWords = []

        case .listening, .recording, .processing:
            errorMessage = nil
        }
    }

    private func updateTranscriptionWords(_ text: String) {
        let words = text.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }
        transcriptionWords = words
    }
}

// MARK: - Recording State Extensions

extension RecordingCoordinator.State {
    
    var displayTitle: String {
        switch self {
        case .idle:
            return "Ready"
        case .listening(let keywords):
            return "Listening for: \(keywords.joined(separator: ", "))"
        case .recording:
            return "Recording..."
        case .processing:
            return "Processing..."
        case .error:
            return "Error"
        }
    }
    
    var displayDescription: String {
        switch self {
        case .idle:
            return "Ready to start recording"
        case .listening:
            return "Waiting for keyword detection"
        case .recording:
            return "Recording your voice"
        case .processing:
            return "Creating your note"
        case .error(let message):
            return message
        }
    }
    
    var iconName: String {
        switch self {
        case .idle:
            return "mic.slash"
        case .listening:
            return "ear"
        case .recording:
            return "mic.fill"
        case .processing:
            return "gearshape.2"
        case .error:
            return "exclamationmark.triangle"
        }
    }
    
    var iconColor: Color {
        switch self {
        case .idle:
            return .gray
        case .listening:
            return .blue
        case .recording:
            return .red
        case .processing:
            return .orange
        case .error:
            return .red
        }
    }
}
