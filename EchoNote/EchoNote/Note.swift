//
//  Note.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import SwiftData

@Model
final class Note {
    var id: UUID
    var title: String
    var content: String
    var audioURL: String?
    var createdAt: Date
    var updatedAt: Date
    var isFavorite: Bool
    var duration: TimeInterval

    @Relationship(deleteRule: .nullify, inverse: \Keyword.notes)
    var keywords: [Keyword]

    init(title: String, content: String, audioURL: String? = nil, keywords: [Keyword] = []) {
        self.id = UUID()
        self.title = title
        self.content = content
        self.audioURL = audioURL
        self.createdAt = Date()
        self.updatedAt = Date()
        self.isFavorite = false
        self.duration = 0
        self.keywords = keywords
    }

    func updateModifiedDate() {
        self.updatedAt = Date()
    }
}
