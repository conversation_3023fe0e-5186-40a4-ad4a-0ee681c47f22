//
//  SecureDataManager.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import CryptoKit

class SecureDataManager {
    
    // MARK: - Error Types
    
    enum SecureDataError: Error {
        case encryptionFailed
        case decryptionFailed
        case keyGenerationFailed
        case invalidData
        case fileProtectionFailed
        
        var localizedDescription: String {
            switch self {
            case .encryptionFailed:
                return "Failed to encrypt data"
            case .decryptionFailed:
                return "Failed to decrypt data"
            case .keyGenerationFailed:
                return "Failed to generate encryption key"
            case .invalidData:
                return "Invalid data format"
            case .fileProtectionFailed:
                return "Failed to set file protection"
            }
        }
    }
    
    // MARK: - Properties
    
    private let encryptionKey: SymmetricKey
    
    // MARK: - Initialization
    
    init() throws {
        // Get or generate encryption key from keychain
        let keyData = try KeychainManager.getEncryptionKey()
        self.encryptionKey = SymmetricKey(data: keyData)
    }
    
    // MARK: - Encryption/Decryption
    
    /// Encrypt data using AES-GCM
    func encrypt(_ data: Data) throws -> Data {
        do {
            let sealedBox = try AES.GCM.seal(data, using: encryptionKey)
            return sealedBox.combined!
        } catch {
            throw SecureDataError.encryptionFailed
        }
    }
    
    /// Decrypt data using AES-GCM
    func decrypt(_ encryptedData: Data) throws -> Data {
        do {
            let sealedBox = try AES.GCM.SealedBox(combined: encryptedData)
            return try AES.GCM.open(sealedBox, using: encryptionKey)
        } catch {
            throw SecureDataError.decryptionFailed
        }
    }
    
    /// Encrypt string
    func encrypt(_ string: String) throws -> Data {
        guard let data = string.data(using: .utf8) else {
            throw SecureDataError.invalidData
        }
        return try encrypt(data)
    }
    
    /// Decrypt to string
    func decryptToString(_ encryptedData: Data) throws -> String {
        let decryptedData = try decrypt(encryptedData)
        guard let string = String(data: decryptedData, encoding: .utf8) else {
            throw SecureDataError.invalidData
        }
        return string
    }
    
    // MARK: - File Operations
    
    /// Save encrypted data to file with protection
    func saveEncryptedFile(_ data: Data, to url: URL) throws {
        let encryptedData = try encrypt(data)
        
        // Create directory if needed
        let directory = url.deletingLastPathComponent()
        try FileManager.default.createDirectory(at: directory, withIntermediateDirectories: true)
        
        // Write encrypted data
        try encryptedData.write(to: url)
        
        // Set file protection
        try setFileProtection(for: url)
    }
    
    /// Load and decrypt file
    func loadEncryptedFile(from url: URL) throws -> Data {
        let encryptedData = try Data(contentsOf: url)
        return try decrypt(encryptedData)
    }
    
    /// Save encrypted string to file
    func saveEncryptedString(_ string: String, to url: URL) throws {
        guard let data = string.data(using: .utf8) else {
            throw SecureDataError.invalidData
        }
        try saveEncryptedFile(data, to: url)
    }
    
    /// Load and decrypt string from file
    func loadEncryptedString(from url: URL) throws -> String {
        let data = try loadEncryptedFile(from: url)
        guard let string = String(data: data, encoding: .utf8) else {
            throw SecureDataError.invalidData
        }
        return string
    }
    
    /// Set file protection level
    private func setFileProtection(for url: URL) throws {
        do {
            try FileManager.default.setAttributes(
                [.protectionKey: FileProtectionType.complete],
                ofItemAtPath: url.path
            )
        } catch {
            throw SecureDataError.fileProtectionFailed
        }
    }
    
    // MARK: - Audio File Security
    
    /// Encrypt audio file and save with protection
    func secureAudioFile(at sourceURL: URL, to destinationURL: URL) throws {
        let audioData = try Data(contentsOf: sourceURL)
        try saveEncryptedFile(audioData, to: destinationURL)
        
        // Remove original unencrypted file
        try FileManager.default.removeItem(at: sourceURL)
    }
    
    /// Decrypt audio file for playback
    func decryptAudioFile(at url: URL) throws -> Data {
        return try loadEncryptedFile(from: url)
    }
    
    // MARK: - Secure Temporary Files
    
    /// Create secure temporary file URL
    func createSecureTempFileURL(withExtension ext: String = "tmp") -> URL {
        let tempDir = FileManager.default.temporaryDirectory
        let fileName = UUID().uuidString + "." + ext
        return tempDir.appendingPathComponent(fileName)
    }
    
    /// Write data to secure temporary file
    func writeToSecureTempFile(_ data: Data, extension ext: String = "tmp") throws -> URL {
        let tempURL = createSecureTempFileURL(withExtension: ext)
        try saveEncryptedFile(data, to: tempURL)
        return tempURL
    }
    
    /// Clean up temporary files
    func cleanupTempFiles() {
        let tempDir = FileManager.default.temporaryDirectory
        
        do {
            let tempFiles = try FileManager.default.contentsOfDirectory(at: tempDir, includingPropertiesForKeys: nil)
            
            for file in tempFiles {
                if file.pathExtension == "tmp" || file.lastPathComponent.contains("EchoNote") {
                    try? FileManager.default.removeItem(at: file)
                }
            }
        } catch {
            print("Failed to cleanup temp files: \(error)")
        }
    }
}

// MARK: - Codable Support

extension SecureDataManager {
    
    /// Encrypt and save Codable object
    func saveEncrypted<T: Codable>(_ object: T, to url: URL) throws {
        let data = try JSONEncoder().encode(object)
        try saveEncryptedFile(data, to: url)
    }
    
    /// Load and decrypt Codable object
    func loadEncrypted<T: Codable>(_ type: T.Type, from url: URL) throws -> T {
        let data = try loadEncryptedFile(from: url)
        return try JSONDecoder().decode(type, from: data)
    }
}

// MARK: - Memory Security

extension SecureDataManager {
    
    /// Securely process data with automatic cleanup
    func secureProcess<T>(_ data: inout Data, operation: (Data) throws -> T) rethrows -> T {
        defer {
            data.secureClear()
        }
        return try operation(data)
    }
    
    /// Create secure data container that auto-clears
    func createSecureContainer(for data: Data) -> SecureDataContainer {
        return SecureDataContainer(data: data)
    }
}

// MARK: - Secure Data Container

class SecureDataContainer {
    private var _data: Data
    
    init(data: Data) {
        self._data = data
    }
    
    deinit {
        _data.secureClear()
    }
    
    func withData<T>(_ operation: (Data) throws -> T) rethrows -> T {
        return try operation(_data)
    }
    
    func clear() {
        _data.secureClear()
    }
}
