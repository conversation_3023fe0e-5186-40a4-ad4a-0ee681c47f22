//
//  ServiceContainer.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import SwiftData
import SwiftUI

// MARK: - Service Container

@MainActor
class ServiceContainer: ObservableObject {
    
    // MARK: - Singleton
    static let shared = ServiceContainer()
    
    // MARK: - Core Services
    private(set) lazy var audioSessionManager: AudioSessionManagerProtocol = AudioSessionManager.shared
    private(set) lazy var permissionsService: PermissionsServiceProtocol = PermissionsService.shared
    
    // MARK: - Business Services
    private(set) lazy var voiceRecordingService: any VoiceRecordingServiceProtocol = VoiceRecordingService(
        audioSessionManager: audioSessionManager,
        permissionsService: permissionsService
    )
    
    private(set) lazy var keywordDetectionService: any KeywordDetectionServiceProtocol = KeywordDetectionService(
        audioSessionManager: audioSessionManager,
        permissionsService: permissionsService
    )
    
    private(set) lazy var noteCreationService: NoteCreationServiceProtocol = NoteCreationService(
        dataManager: dataManager
    )
    
    // MARK: - Coordinators
    private(set) lazy var recordingCoordinator: RecordingCoordinator = RecordingCoordinator(
        voiceRecordingService: voiceRecordingService,
        keywordDetectionService: keywordDetectionService,
        noteCreationService: noteCreationService,
        dataManager: dataManager
    )
    
    // MARK: - Data Manager
    private var _dataManager: DataManager?
    var dataManager: DataManager {
        guard let dataManager = _dataManager else {
            fatalError("DataManager not initialized. Call configure(with:) first.")
        }
        return dataManager
    }
    
    // MARK: - Initialization
    private init() {}
    
    // MARK: - Configuration
    func configure(with modelContext: ModelContext) {
        _dataManager = DataManager(modelContext: modelContext)
        print("✅ ServiceContainer: Configured with ModelContext")
    }
    
    // MARK: - Service Access Methods
    func getRecordingCoordinator() -> RecordingCoordinator {
        return recordingCoordinator
    }
    
    func getVoiceRecordingService() -> any VoiceRecordingServiceProtocol {
        return voiceRecordingService
    }

    func getKeywordDetectionService() -> any KeywordDetectionServiceProtocol {
        return keywordDetectionService
    }
    
    func getNoteCreationService() -> NoteCreationServiceProtocol {
        return noteCreationService
    }
    
    func getAudioSessionManager() -> AudioSessionManagerProtocol {
        return audioSessionManager
    }
    
    func getPermissionsService() -> PermissionsServiceProtocol {
        return permissionsService
    }
    
    func getDataManager() -> DataManager {
        return dataManager
    }
}

// MARK: - SwiftUI Environment

struct ServiceContainerKey: EnvironmentKey {
    static let defaultValue = ServiceContainer.shared
}

extension EnvironmentValues {
    var serviceContainer: ServiceContainer {
        get { self[ServiceContainerKey.self] }
        set { self[ServiceContainerKey.self] = newValue }
    }
}

// MARK: - View Extension

extension View {
    func withServiceContainer(_ container: ServiceContainer = ServiceContainer.shared) -> some View {
        environment(\.serviceContainer, container)
    }
}

// MARK: - Mock Container for Testing

#if DEBUG
class MockServiceContainer: ServiceContainer {
    
    public init() {
        super.init()
    }
    
    // Override services with mock implementations for testing
    func configureMocks() {
        // This would be implemented with mock services for unit testing
        print("🧪 MockServiceContainer: Configured with mock services")
    }
}
#endif
