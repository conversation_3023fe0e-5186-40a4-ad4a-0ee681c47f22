//
//  KeywordDetectionService.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import Speech
import AVFoundation
import Combine

// MARK: - Detection Models

struct KeywordDetection {
    let keyword: String
    let confidence: Float
    let timestamp: Date
    let context: String
}

// MARK: - Service Protocol

protocol KeywordDetectionServiceProtocol: ObservableObject {
    var isListening: Bool { get }
    var activeKeywords: [String] { get }
    var detectionStream: AnyPublisher<KeywordDetection, Never> { get }
    
    func startListening(for keywords: [String]) async throws
    func stopListening() async
    func updateKeywords(_ keywords: [String]) async
    func setDetectionSensitivity(_ sensitivity: Float) async
}

// MARK: - Implementation

@MainActor
class KeywordDetectionService: NSObject, KeywordDetectionServiceProtocol {
    
    // MARK: - Published Properties
    @Published private(set) var isListening = false
    @Published private(set) var activeKeywords: [String] = []
    
    // MARK: - Private Properties
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioEngine: AVAudioEngine?
    
    private let detectionSubject = PassthroughSubject<KeywordDetection, Never>()
    private var accumulatedText = ""
    private var detectionSensitivity: Float = 0.8
    private var lastDetectionTime: Date?
    private let cooldownPeriod: TimeInterval = 2.0
    
    private let audioSessionManager: AudioSessionManagerProtocol
    private let permissionsService: PermissionsServiceProtocol
    
    // MARK: - Computed Properties
    var detectionStream: AnyPublisher<KeywordDetection, Never> {
        detectionSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    init(
        audioSessionManager: AudioSessionManagerProtocol = AudioSessionManager.shared,
        permissionsService: PermissionsServiceProtocol = PermissionsService.shared
    ) {
        self.audioSessionManager = audioSessionManager
        self.permissionsService = permissionsService
        super.init()
        setupSpeechRecognizer()
    }
    
    // MARK: - Public Methods
    func startListening(for keywords: [String]) async throws {
        guard !isListening else {
            throw KeywordDetectionError.alreadyListening
        }
        
        guard !keywords.isEmpty else {
            throw KeywordDetectionError.noKeywords
        }
        
        // Request permissions
        let hasPermissions = await permissionsService.requestAllPermissions()
        guard hasPermissions else {
            throw KeywordDetectionError.permissionDenied
        }
        
        // Setup audio session for background listening
        try await audioSessionManager.configureForBackgroundListening()
        
        activeKeywords = keywords
        accumulatedText = ""
        lastDetectionTime = nil
        
        try startSpeechRecognition()
        isListening = true
        
        print("🎯 KeywordDetectionService: Started listening for keywords: \(keywords)")
    }
    
    func stopListening() async {
        guard isListening else { return }
        
        stopSpeechRecognition()
        isListening = false
        activeKeywords = []
        accumulatedText = ""
        
        print("🛑 KeywordDetectionService: Stopped listening")
    }
    
    func updateKeywords(_ keywords: [String]) async {
        activeKeywords = keywords
        print("🔄 KeywordDetectionService: Updated keywords to: \(keywords)")
    }
    
    func setDetectionSensitivity(_ sensitivity: Float) async {
        detectionSensitivity = max(0.0, min(1.0, sensitivity))
        print("🎚️ KeywordDetectionService: Set sensitivity to: \(detectionSensitivity)")
    }
}

// MARK: - Private Methods
private extension KeywordDetectionService {
    
    func setupSpeechRecognizer() {
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "en-US"))
        speechRecognizer?.delegate = self
    }
    
    func startSpeechRecognition() throws {
        guard let speechRecognizer = speechRecognizer,
              speechRecognizer.isAvailable else {
            throw KeywordDetectionError.speechRecognitionUnavailable
        }
        
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            throw KeywordDetectionError.speechRecognitionSetupFailed
        }
        
        recognitionRequest.shouldReportPartialResults = true
        recognitionRequest.requiresOnDeviceRecognition = false // Allow cloud for better accuracy
        
        audioEngine = AVAudioEngine()
        let inputNode = audioEngine!.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }
        
        audioEngine!.prepare()
        try audioEngine!.start()
        
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            DispatchQueue.main.async {
                self?.handleRecognitionResult(result, error: error)
            }
        }
    }
    
    func stopSpeechRecognition() {
        audioEngine?.stop()
        audioEngine?.inputNode.removeTap(onBus: 0)
        recognitionRequest?.endAudio()
        recognitionTask?.cancel()
        
        audioEngine = nil
        recognitionRequest = nil
        recognitionTask = nil
    }
    
    func handleRecognitionResult(_ result: SFSpeechRecognitionResult?, error: Error?) {
        if let error = error {
            print("❌ KeywordDetectionService: Recognition error: \(error)")
            Task {
                await stopListening()
            }
            return
        }
        
        guard let result = result else { return }
        
        let newText = result.bestTranscription.formattedString
        accumulatedText = newText
        
        // Check for keywords in the new text
        checkForKeywords(in: newText)
        
        // Limit accumulated text to prevent memory issues
        if accumulatedText.count > 1000 {
            let startIndex = accumulatedText.index(accumulatedText.startIndex, offsetBy: 500)
            accumulatedText = String(accumulatedText[startIndex...])
        }
    }
    
    func checkForKeywords(in text: String) {
        let lowercaseText = text.lowercased()
        let currentTime = Date()
        
        // Apply cooldown to prevent rapid repeated detections
        if let lastTime = lastDetectionTime,
           currentTime.timeIntervalSince(lastTime) < cooldownPeriod {
            return
        }
        
        for keyword in activeKeywords {
            let lowercaseKeyword = keyword.lowercased()
            
            if lowercaseText.contains(lowercaseKeyword) {
                // Calculate confidence based on context and clarity
                let confidence = calculateConfidence(for: keyword, in: text)
                
                if confidence >= detectionSensitivity {
                    let detection = KeywordDetection(
                        keyword: keyword,
                        confidence: confidence,
                        timestamp: currentTime,
                        context: extractContext(for: keyword, in: text)
                    )
                    
                    lastDetectionTime = currentTime
                    detectionSubject.send(detection)
                    
                    print("✅ KeywordDetectionService: Detected '\(keyword)' with confidence \(confidence)")
                    break // Only detect one keyword at a time
                }
            }
        }
    }
    
    func calculateConfidence(for keyword: String, in text: String) -> Float {
        let lowercaseText = text.lowercased()
        let lowercaseKeyword = keyword.lowercased()
        
        // Base confidence if keyword is found
        var confidence: Float = 0.7
        
        // Boost confidence if keyword appears as a separate word
        let words = lowercaseText.components(separatedBy: .whitespacesAndNewlines)
        if words.contains(lowercaseKeyword) {
            confidence += 0.2
        }
        
        // Boost confidence if keyword appears at the beginning
        if lowercaseText.hasPrefix(lowercaseKeyword) {
            confidence += 0.1
        }
        
        // Reduce confidence if text is very long (might be noise)
        if text.count > 200 {
            confidence -= 0.1
        }
        
        return min(1.0, max(0.0, confidence))
    }
    
    func extractContext(for keyword: String, in text: String) -> String {
        let words = text.components(separatedBy: .whitespacesAndNewlines)
        guard let keywordIndex = words.firstIndex(where: { $0.lowercased().contains(keyword.lowercased()) }) else {
            return text
        }
        
        // Extract context around the keyword (3 words before and after)
        let startIndex = max(0, keywordIndex - 3)
        let endIndex = min(words.count, keywordIndex + 4)
        
        return words[startIndex..<endIndex].joined(separator: " ")
    }
}

// MARK: - SFSpeechRecognizerDelegate
extension KeywordDetectionService: SFSpeechRecognizerDelegate {
    func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        if !available && isListening {
            Task {
                await stopListening()
            }
        }
    }
}

// MARK: - Errors
enum KeywordDetectionError: LocalizedError {
    case alreadyListening
    case noKeywords
    case permissionDenied
    case speechRecognitionUnavailable
    case speechRecognitionSetupFailed
    case audioSessionSetupFailed
    
    var errorDescription: String? {
        switch self {
        case .alreadyListening:
            return "Keyword detection is already active"
        case .noKeywords:
            return "No keywords provided for detection"
        case .permissionDenied:
            return "Microphone or speech recognition permission denied"
        case .speechRecognitionUnavailable:
            return "Speech recognition is not available"
        case .speechRecognitionSetupFailed:
            return "Failed to setup speech recognition"
        case .audioSessionSetupFailed:
            return "Failed to setup audio session"
        }
    }
}
