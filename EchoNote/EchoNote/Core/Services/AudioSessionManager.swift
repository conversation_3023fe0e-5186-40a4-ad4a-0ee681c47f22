//
//  AudioSessionManager.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import AVFoundation

// MARK: - Service Protocol

protocol AudioSessionManagerProtocol {
    var isActive: Bool { get }
    var currentCategory: AVAudioSession.Category { get }

    func configureForRecording() async throws
    func configureForBackgroundListening() async throws
    func configureForPlayback() async throws
    func deactivate() async throws
}

// MARK: - Permissions Service Protocol

protocol PermissionsServiceProtocol {
    var hasMicrophonePermission: Bool { get }
    var hasSpeechRecognitionPermission: Bool { get }

    func requestMicrophonePermission() async -> Bool
    func requestSpeechRecognitionPermission() async -> Bool
    func requestAllPermissions() async -> Bool
}

// MARK: - Implementation

class AudioSessionManager: AudioSessionManagerProtocol {
    
    // MARK: - Singleton
    static let shared = AudioSessionManager()
    
    // MARK: - Private Properties
    private let audioSession = AVAudioSession.sharedInstance()
    private var isConfigured = false
    
    // MARK: - Computed Properties
    var isActive: Bool {
        audioSession.isOtherAudioPlaying == false
    }
    
    var currentCategory: AVAudioSession.Category {
        audioSession.category
    }
    
    // MARK: - Initialization
    private init() {
        setupNotifications()
    }
    
    // MARK: - Public Methods
    func configureForRecording() async throws {
        try await withCheckedThrowingContinuation { continuation in
            do {
                // Set category for recording with playback capability
                try audioSession.setCategory(
                    .playAndRecord,
                    mode: .measurement,
                    options: [.defaultToSpeaker, .allowBluetooth, .allowBluetoothA2DP]
                )
                
                // Set preferred sample rate for high quality
                try audioSession.setPreferredSampleRate(44100.0)
                
                // Set preferred buffer duration for low latency
                try audioSession.setPreferredIOBufferDuration(0.01) // 10ms
                
                // Activate the session
                try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
                
                isConfigured = true
                print("🎧 AudioSessionManager: Configured for recording")
                continuation.resume()
                
            } catch {
                print("❌ AudioSessionManager: Failed to configure for recording: \(error)")
                continuation.resume(throwing: AudioSessionError.configurationFailed(underlying: error))
            }
        }
    }
    
    func configureForBackgroundListening() async throws {
        try await withCheckedThrowingContinuation { continuation in
            do {
                // Set category for background recording
                try audioSession.setCategory(
                    .record,
                    mode: .measurement,
                    options: [.allowBluetooth, .duckOthers]
                )
                
                // Use lower sample rate for background efficiency
                try audioSession.setPreferredSampleRate(16000.0)
                
                // Use longer buffer for background stability
                try audioSession.setPreferredIOBufferDuration(0.02) // 20ms
                
                // Activate the session
                try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
                
                isConfigured = true
                print("👂 AudioSessionManager: Configured for background listening")
                continuation.resume()
                
            } catch {
                print("❌ AudioSessionManager: Failed to configure for background listening: \(error)")
                continuation.resume(throwing: AudioSessionError.configurationFailed(underlying: error))
            }
        }
    }
    
    func configureForPlayback() async throws {
        try await withCheckedThrowingContinuation { continuation in
            do {
                // Set category for playback
                try audioSession.setCategory(
                    .playback,
                    mode: .default,
                    options: [.defaultToSpeaker]
                )
                
                // Activate the session
                try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
                
                isConfigured = true
                print("🔊 AudioSessionManager: Configured for playback")
                continuation.resume()
                
            } catch {
                print("❌ AudioSessionManager: Failed to configure for playback: \(error)")
                continuation.resume(throwing: AudioSessionError.configurationFailed(underlying: error))
            }
        }
    }
    
    func deactivate() async throws {
        try await withCheckedThrowingContinuation { continuation in
            do {
                try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
                isConfigured = false
                print("🔇 AudioSessionManager: Deactivated")
                continuation.resume()
                
            } catch {
                print("❌ AudioSessionManager: Failed to deactivate: \(error)")
                continuation.resume(throwing: AudioSessionError.deactivationFailed(underlying: error))
            }
        }
    }
}

// MARK: - Private Methods
private extension AudioSessionManager {
    
    func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleInterruption),
            name: AVAudioSession.interruptionNotification,
            object: audioSession
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleRouteChange),
            name: AVAudioSession.routeChangeNotification,
            object: audioSession
        )
    }
    
    @objc func handleInterruption(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }
        
        switch type {
        case .began:
            print("🔇 AudioSessionManager: Interruption began")
            // Post notification for services to handle interruption
            NotificationCenter.default.post(
                name: .audioSessionInterrupted,
                object: nil
            )
            
        case .ended:
            print("🔊 AudioSessionManager: Interruption ended")
            
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) {
                    // Post notification for services to resume
                    NotificationCenter.default.post(
                        name: .audioSessionResumed,
                        object: nil
                    )
                }
            }
            
        @unknown default:
            break
        }
    }
    
    @objc func handleRouteChange(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
            return
        }
        
        switch reason {
        case .newDeviceAvailable:
            print("🎧 AudioSessionManager: New audio device available")
            
        case .oldDeviceUnavailable:
            print("🎧 AudioSessionManager: Audio device unavailable")
            // Post notification for services to handle device change
            NotificationCenter.default.post(
                name: .audioDeviceChanged,
                object: nil
            )
            
        case .categoryChange:
            print("🎧 AudioSessionManager: Category changed")
            
        default:
            break
        }
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let audioSessionInterrupted = Notification.Name("audioSessionInterrupted")
    static let audioSessionResumed = Notification.Name("audioSessionResumed")
    static let audioDeviceChanged = Notification.Name("audioDeviceChanged")
}

// MARK: - Errors
enum AudioSessionError: LocalizedError {
    case configurationFailed(underlying: Error)
    case deactivationFailed(underlying: Error)
    case permissionDenied
    case hardwareUnavailable
    
    var errorDescription: String? {
        switch self {
        case .configurationFailed(let error):
            return "Failed to configure audio session: \(error.localizedDescription)"
        case .deactivationFailed(let error):
            return "Failed to deactivate audio session: \(error.localizedDescription)"
        case .permissionDenied:
            return "Microphone permission denied"
        case .hardwareUnavailable:
            return "Audio hardware unavailable"
        }
    }
}
