//
//  VoiceRecordingService.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import AVFoundation
import Speech
import Combine

// MARK: - Recording Models

struct RecordingSession: Equatable {
    let id: UUID
    let startTime: Date
    let audioURL: URL
    let mode: RecordingMode
    
    enum RecordingMode {
        case manual
        case background
        case siri
    }
}

struct RecordingResult {
    let session: RecordingSession
    let duration: TimeInterval
    let transcription: String
    let audioURL: URL
    let audioLevels: [Float]
}

// MARK: - Service Protocol

protocol VoiceRecordingServiceProtocol: ObservableObject {
    var isRecording: Bool { get }
    var currentSession: RecordingSession? { get }
    var recordingDuration: TimeInterval { get }
    var transcribedText: String { get }
    var audioLevels: [Float] { get }
    var recordingProgress: AnyPublisher<RecordingProgress, Never> { get }
    
    func startRecording(mode: RecordingSession.RecordingMode) async throws -> RecordingSession
    func stopRecording() async throws -> RecordingResult
    func pauseRecording() async throws
    func resumeRecording() async throws
    func cancelRecording() async
}

struct RecordingProgress {
    let duration: TimeInterval
    let transcription: String
    let audioLevels: [Float]
    let isActive: Bool
}

// MARK: - Implementation

@MainActor
class VoiceRecordingService: NSObject, VoiceRecordingServiceProtocol {
    
    // MARK: - Published Properties
    @Published private(set) var isRecording = false
    @Published private(set) var currentSession: RecordingSession?
    @Published private(set) var recordingDuration: TimeInterval = 0
    @Published private(set) var transcribedText = ""
    @Published private(set) var audioLevels: [Float] = []
    
    // MARK: - Private Properties
    private var audioRecorder: AVAudioRecorder?
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioEngine: AVAudioEngine?
    
    private var recordingTimer: Timer?
    private var levelTimer: Timer?
    private let progressSubject = PassthroughSubject<RecordingProgress, Never>()
    
    private let audioSessionManager: AudioSessionManagerProtocol
    private let permissionsService: PermissionsServiceProtocol
    
    // MARK: - Computed Properties
    var recordingProgress: AnyPublisher<RecordingProgress, Never> {
        progressSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    init(
        audioSessionManager: AudioSessionManagerProtocol = AudioSessionManager.shared,
        permissionsService: PermissionsServiceProtocol = PermissionsService.shared
    ) {
        self.audioSessionManager = audioSessionManager
        self.permissionsService = permissionsService
        super.init()
        setupSpeechRecognizer()
    }
    
    // MARK: - Public Methods
    func startRecording(mode: RecordingSession.RecordingMode) async throws -> RecordingSession {
        guard !isRecording else {
            throw RecordingError.alreadyRecording
        }
        
        // Request permissions
        let hasPermissions = await permissionsService.requestAllPermissions()
        guard hasPermissions else {
            throw RecordingError.permissionDenied
        }
        
        // Setup audio session
        try await audioSessionManager.configureForRecording()
        
        // Create session
        let session = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: generateAudioURL(),
            mode: mode
        )
        
        // Start recording
        try setupAudioRecorder(url: session.audioURL)
        try startSpeechRecognition()
        
        currentSession = session
        isRecording = true
        recordingDuration = 0
        transcribedText = ""
        audioLevels = []
        
        startTimers()
        
        return session
    }
    
    func stopRecording() async throws -> RecordingResult {
        guard let session = currentSession, isRecording else {
            throw RecordingError.notRecording
        }
        
        stopTimers()
        stopSpeechRecognition()
        audioRecorder?.stop()
        audioRecorder = nil // Release audio recorder to free memory

        // Audio session will be deactivated automatically when recording stops

        let result = RecordingResult(
            session: session,
            duration: recordingDuration,
            transcription: transcribedText,
            audioURL: session.audioURL,
            audioLevels: audioLevels
        )

        // Reset state and clear memory
        isRecording = false
        currentSession = nil
        recordingDuration = 0
        transcribedText = ""
        audioLevels.removeAll() // Clear array to free memory
        
        return result
    }
    
    func pauseRecording() async throws {
        guard isRecording else {
            throw RecordingError.notRecording
        }
        
        audioRecorder?.pause()
        stopTimers()
        
        // Note: Speech recognition continues during pause
    }
    
    func resumeRecording() async throws {
        guard let session = currentSession, !isRecording else {
            throw RecordingError.invalidState
        }
        
        audioRecorder?.record()
        startTimers()
    }
    
    func cancelRecording() async {
        stopTimers()
        stopSpeechRecognition()
        audioRecorder?.stop()
        
        // Clean up audio file
        if let session = currentSession {
            try? FileManager.default.removeItem(at: session.audioURL)
        }
        
        // Reset state
        isRecording = false
        currentSession = nil
        recordingDuration = 0
        transcribedText = ""
        audioLevels = []
    }
}

// MARK: - Private Methods
private extension VoiceRecordingService {
    
    func setupSpeechRecognizer() {
        // Only create if not already exists to avoid memory overhead
        guard speechRecognizer == nil else { return }

        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "en-US"))
        speechRecognizer?.delegate = self

        // Configure for better performance
        speechRecognizer?.defaultTaskHint = .dictation
    }
    
    func setupAudioRecorder(url: URL) throws {
        let settings: [String: Any] = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 44100,
            AVNumberOfChannelsKey: 1,
            AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
        ]
        
        audioRecorder = try AVAudioRecorder(url: url, settings: settings)
        audioRecorder?.isMeteringEnabled = true
        audioRecorder?.prepareToRecord()
        audioRecorder?.record()
    }
    
    func startSpeechRecognition() throws {
        guard let speechRecognizer = speechRecognizer,
              speechRecognizer.isAvailable else {
            throw RecordingError.speechRecognitionUnavailable
        }
        
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            throw RecordingError.speechRecognitionSetupFailed
        }

        // Optimize for performance and memory usage
        recognitionRequest.shouldReportPartialResults = true
        recognitionRequest.requiresOnDeviceRecognition = false // Use cloud for better accuracy

        audioEngine = AVAudioEngine()
        let inputNode = audioEngine!.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)

        // Use smaller buffer size for better responsiveness and lower memory usage
        inputNode.installTap(onBus: 0, bufferSize: 512, format: recordingFormat) { [weak self] buffer, _ in
            recognitionRequest.append(buffer)
            // Update audio levels for UI feedback
            self?.updateAudioLevels(from: buffer)
        }
        
        audioEngine!.prepare()
        try audioEngine!.start()
        
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            DispatchQueue.main.async {
                if let result = result {
                    self?.transcribedText = result.bestTranscription.formattedString
                }
                
                if error != nil || result?.isFinal == true {
                    self?.stopSpeechRecognition()
                }
            }
        }
    }
    
    func stopSpeechRecognition() {
        audioEngine?.stop()
        audioEngine?.inputNode.removeTap(onBus: 0)
        recognitionRequest?.endAudio()
        recognitionTask?.cancel()
        
        audioEngine = nil
        recognitionRequest = nil
        recognitionTask = nil
    }
    
    func startTimers() {
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            self?.updateRecordingDuration()
        }
        
        levelTimer = Timer.scheduledTimer(withTimeInterval: 0.05, repeats: true) { [weak self] _ in
            self?.updateAudioLevels()
        }
    }
    
    func stopTimers() {
        recordingTimer?.invalidate()
        levelTimer?.invalidate()
        recordingTimer = nil
        levelTimer = nil
    }
    
    func updateRecordingDuration() {
        guard let session = currentSession else { return }
        recordingDuration = Date().timeIntervalSince(session.startTime)
        publishProgress()
    }
    
    func updateAudioLevels() {
        audioRecorder?.updateMeters()
        let level = audioRecorder?.averagePower(forChannel: 0) ?? -160
        let normalizedLevel = max(0, (level + 160) / 160)
        
        audioLevels.append(normalizedLevel)
        if audioLevels.count > 100 {
            audioLevels.removeFirst()
        }
        
        publishProgress()
    }
    
    func publishProgress() {
        let progress = RecordingProgress(
            duration: recordingDuration,
            transcription: transcribedText,
            audioLevels: audioLevels,
            isActive: isRecording
        )
        progressSubject.send(progress)
    }
    
    func generateAudioURL() -> URL {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioFilename = "recording_\(Date().timeIntervalSince1970).m4a"
        return documentsPath.appendingPathComponent(audioFilename)
    }

    func updateAudioLevels(from buffer: AVAudioPCMBuffer) {
        guard let channelData = buffer.floatChannelData?[0] else { return }

        let frameLength = Int(buffer.frameLength)
        let samples = Array(UnsafeBufferPointer(start: channelData, count: frameLength))

        // Calculate RMS (Root Mean Square) for audio level
        let rms = sqrt(samples.map { $0 * $0 }.reduce(0, +) / Float(samples.count))
        let level = min(max(rms * 100, 0), 100) // Normalize to 0-100

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // Keep only recent levels to prevent memory growth
            if self.audioLevels.count > 100 {
                self.audioLevels.removeFirst(50) // Remove older half
            }
            self.audioLevels.append(level)
        }
    }
}

// MARK: - SFSpeechRecognizerDelegate
extension VoiceRecordingService: SFSpeechRecognizerDelegate {
    func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        if !available && isRecording {
            Task {
                await cancelRecording()
            }
        }
    }
}

// MARK: - Errors
enum RecordingError: LocalizedError {
    case alreadyRecording
    case notRecording
    case permissionDenied
    case speechRecognitionUnavailable
    case speechRecognitionSetupFailed
    case invalidState
    case audioSessionSetupFailed
    
    var errorDescription: String? {
        switch self {
        case .alreadyRecording:
            return "Recording is already in progress"
        case .notRecording:
            return "No recording in progress"
        case .permissionDenied:
            return "Microphone or speech recognition permission denied"
        case .speechRecognitionUnavailable:
            return "Speech recognition is not available"
        case .speechRecognitionSetupFailed:
            return "Failed to setup speech recognition"
        case .invalidState:
            return "Invalid recording state"
        case .audioSessionSetupFailed:
            return "Failed to setup audio session"
        }
    }
}
