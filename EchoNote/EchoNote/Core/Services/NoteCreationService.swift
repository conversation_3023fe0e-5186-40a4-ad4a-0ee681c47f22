//
//  NoteCreationService.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import SwiftData

// MARK: - Creation Models

struct NoteCreationRequest {
    let title: String?
    let content: String
    let keywords: [String]
    let audioURL: String?
    let duration: TimeInterval
    let source: NoteSource
    
    enum NoteSource {
        case manual
        case background
        case siri
    }
}

struct NoteCreationResult {
    let note: Note
    let success: Bool
    let error: Error?
}

// MARK: - Service Protocol

protocol NoteCreationServiceProtocol {
    func createNote(from recordingResult: RecordingResult, keywords: [String]) async throws -> Note
    func createNote(from request: NoteCreationRequest) async throws -> Note
    func createTextNote(title: String, content: String, keywords: [String]) async throws -> Note
    func updateNote(_ note: Note, with content: String) async throws
    func deleteNote(_ note: Note) async throws
}

// MARK: - Implementation

@MainActor
class NoteCreationService: NoteCreationServiceProtocol {
    
    // MARK: - Private Properties
    private let dataManager: DataManager
    private let keywordManager: KeywordManagerProtocol
    private let userPreferences: UserPreferencesService
    
    // MARK: - Initialization
    init(
        dataManager: DataManager,
        keywordManager: KeywordManagerProtocol = KeywordManager.shared,
        userPreferences: UserPreferencesService = UserPreferencesServiceImpl.shared
    ) {
        self.dataManager = dataManager
        self.keywordManager = keywordManager
        self.userPreferences = userPreferences
    }
    
    // MARK: - Public Methods
    func createNote(from recordingResult: RecordingResult, keywords: [String]) async throws -> Note {
        let preferences = await userPreferences.getCurrentPreferences()
        
        // Determine if audio should be included
        let shouldIncludeAudio = preferences?.recordAudioWhenCreatingNote ?? true
        let finalAudioURL = shouldIncludeAudio ? recordingResult.audioURL.path : nil
        
        // Generate title if not provided
        let title = generateTitle(
            from: recordingResult.transcription,
            keywords: keywords,
            source: mapRecordingModeToSource(recordingResult.session.mode)
        )
        
        let request = NoteCreationRequest(
            title: title,
            content: recordingResult.transcription,
            keywords: keywords,
            audioURL: finalAudioURL,
            duration: shouldIncludeAudio ? recordingResult.duration : 0,
            source: mapRecordingModeToSource(recordingResult.session.mode)
        )
        
        return try await createNote(from: request)
    }
    
    func createNote(from request: NoteCreationRequest) async throws -> Note {
        // Validate content
        guard !request.content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw NoteCreationError.emptyContent
        }
        
        // Find or create keyword objects
        let keywordObjects = try await findOrCreateKeywords(request.keywords)
        
        // Generate final title
        let finalTitle = request.title ?? generateTitle(
            from: request.content,
            keywords: request.keywords,
            source: request.source
        )
        
        // Create note using DataManager
        let note = dataManager.createNote(
            title: finalTitle,
            content: request.content,
            audioURL: request.audioURL,
            keywords: keywordObjects
        )
        
        // Set duration if provided
        if request.duration > 0 {
            note.duration = request.duration
        }
        
        // Update keyword usage statistics
        for keyword in keywordObjects {
            keyword.incrementUsage()
        }
        
        // Log creation for analytics
        logNoteCreation(note: note, source: request.source)
        
        return note
    }
    
    func createTextNote(title: String, content: String, keywords: [String]) async throws -> Note {
        let request = NoteCreationRequest(
            title: title,
            content: content,
            keywords: keywords,
            audioURL: nil,
            duration: 0,
            source: .siri
        )
        
        return try await createNote(from: request)
    }
    
    func updateNote(_ note: Note, with content: String) async throws {
        guard !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw NoteCreationError.emptyContent
        }
        
        note.content = content
        note.updateModifiedDate()
        
        // Save changes
        do {
            try dataManager.modelContext.save()
        } catch {
            throw NoteCreationError.saveFailed(underlying: error)
        }
    }
    
    func deleteNote(_ note: Note) async throws {
        // Clean up audio file if exists
        if let audioURL = note.audioURL {
            try? FileManager.default.removeItem(atPath: audioURL)
        }
        
        // Remove note
        dataManager.modelContext.delete(note)
        
        // Save changes
        do {
            try dataManager.modelContext.save()
        } catch {
            throw NoteCreationError.deleteFailed(underlying: error)
        }
    }
}

// MARK: - Private Methods
private extension NoteCreationService {
    
    func findOrCreateKeywords(_ keywordTexts: [String]) async throws -> [Keyword] {
        var keywords: [Keyword] = []
        
        for keywordText in keywordTexts {
            let keyword = dataManager.findOrCreateKeyword(text: keywordText)
            keywords.append(keyword)
        }
        
        return keywords
    }
    
    func generateTitle(from content: String, keywords: [String], source: NoteCreationRequest.NoteSource) -> String {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .short)
        
        // Use first keyword if available
        if let firstKeyword = keywords.first {
            return "\(firstKeyword.capitalized) - \(timestamp)"
        }
        
        // Extract first few words from content
        let words = content.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }
        
        if words.count >= 3 {
            let firstWords = Array(words.prefix(3)).joined(separator: " ")
            return "\(firstWords) - \(timestamp)"
        } else if !words.isEmpty {
            return "\(words.first!) - \(timestamp)"
        }
        
        // Fallback based on source
        switch source {
        case .manual:
            return "Voice Note - \(timestamp)"
        case .background:
            return "Auto Note - \(timestamp)"
        case .siri:
            return "Siri Note - \(timestamp)"
        }
    }
    
    func mapRecordingModeToSource(_ mode: RecordingSession.RecordingMode) -> NoteCreationRequest.NoteSource {
        switch mode {
        case .manual:
            return .manual
        case .background:
            return .background
        case .siri:
            return .siri
        }
    }
    
    func logNoteCreation(note: Note, source: NoteCreationRequest.NoteSource) {
        let metadata: [String: Any] = [
            "noteId": note.id.uuidString,
            "source": String(describing: source),
            "hasAudio": note.audioURL != nil,
            "duration": note.duration,
            "keywordCount": note.keywords.count,
            "contentLength": note.content.count
        ]
        
        LoggingService.shared.info(
            "Note created",
            category: .userAction,
            metadata: metadata
        )
        
        // Track performance metrics
        PerformanceMonitoringService.shared.recordEvent(
            .noteCreation,
            metadata: metadata
        )
    }
}

// MARK: - Supporting Services

protocol UserPreferencesService {
    func getCurrentPreferences() async -> UserPreferences?
}

class UserPreferencesServiceImpl: UserPreferencesService {
    static let shared: UserPreferencesService = UserPreferencesServiceImpl()

    func getCurrentPreferences() async -> UserPreferences? {
        // This would be implemented to fetch current user preferences
        // For now, return nil to use defaults
        return nil
    }
}

protocol KeywordManagerProtocol {
    func findOrCreateKeyword(text: String) async throws -> Keyword
    func getAllKeywords() async -> [Keyword]
}

extension KeywordManagerProtocol {
    static var shared: KeywordManagerProtocol {
        // This would return the actual KeywordManager instance
        fatalError("KeywordManager.shared not implemented")
    }
}

// MARK: - Errors
enum NoteCreationError: LocalizedError {
    case emptyContent
    case keywordNotFound(String)
    case saveFailed(underlying: Error)
    case deleteFailed(underlying: Error)
    case invalidData
    
    var errorDescription: String? {
        switch self {
        case .emptyContent:
            return "Note content cannot be empty"
        case .keywordNotFound(let keyword):
            return "Keyword '\(keyword)' not found"
        case .saveFailed(let error):
            return "Failed to save note: \(error.localizedDescription)"
        case .deleteFailed(let error):
            return "Failed to delete note: \(error.localizedDescription)"
        case .invalidData:
            return "Invalid note data provided"
        }
    }
}
