//
//  MemoryManagementService.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import UIKit
import AVFoundation

// MARK: - Memory Management Service

@MainActor
class MemoryManagementService: ObservableObject {
    
    // MARK: - Properties
    
    static let shared = MemoryManagementService()
    
    private var audioCache: NSCache<NSString, AVAudioPlayer> = NSCache()
    private var imageCache: NSCache<NSString, UIImage> = NSCache()
    private var dataCache: NSCache<NSString, NSData> = NSCache()
    
    private var memoryWarningObserver: NSObjectProtocol?
    
    // MARK: - Initialization
    
    private init() {
        setupCaches()
        setupMemoryWarningObserver()
    }
    
    deinit {
        if let observer = memoryWarningObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }
    
    // MARK: - Cache Setup
    
    private func setupCaches() {
        // Audio cache configuration
        audioCache.countLimit = 10 // Limit to 10 audio players
        audioCache.totalCostLimit = 50 * 1024 * 1024 // 50MB limit
        
        // Image cache configuration
        imageCache.countLimit = 50 // Limit to 50 images
        imageCache.totalCostLimit = 20 * 1024 * 1024 // 20MB limit
        
        // Data cache configuration
        dataCache.countLimit = 100 // Limit to 100 data objects
        dataCache.totalCostLimit = 30 * 1024 * 1024 // 30MB limit
        
        LoggingService.shared.info("Memory caches configured", category: .performance, metadata: [
            "audioCacheLimit": "50MB",
            "imageCacheLimit": "20MB",
            "dataCacheLimit": "30MB"
        ])
    }
    
    // MARK: - Memory Warning Handling
    
    private func setupMemoryWarningObserver() {
        memoryWarningObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleMemoryWarning()
            }
        }
    }
    
    private func handleMemoryWarning() {
        LoggingService.shared.warning("Memory warning received - clearing caches", category: .performance)
        
        // Clear all caches
        clearAllCaches()
        
        // Force garbage collection
        autoreleasepool {
            // Perform any additional cleanup
            cleanupAudioResources()
            cleanupImageResources()
        }
        
        // Track memory warning event
        PerformanceMonitoringService.shared.trackEvent(.memoryWarning)
    }
    
    // MARK: - Audio Cache Management
    
    func cacheAudioPlayer(_ player: AVAudioPlayer, forKey key: String) {
        let cost = Int(player.duration * 1000) // Approximate cost based on duration
        audioCache.setObject(player, forKey: key as NSString, cost: cost)
    }
    
    func getCachedAudioPlayer(forKey key: String) -> AVAudioPlayer? {
        return audioCache.object(forKey: key as NSString)
    }
    
    func removeCachedAudioPlayer(forKey key: String) {
        audioCache.removeObject(forKey: key as NSString)
    }
    
    private func cleanupAudioResources() {
        // Stop all cached audio players
        audioCache.removeAllObjects()
        
        // Additional audio cleanup
        try? AVAudioSession.sharedInstance().setActive(false)
        
        LoggingService.shared.info("Audio resources cleaned up", category: .performance)
    }
    
    // MARK: - Image Cache Management
    
    func cacheImage(_ image: UIImage, forKey key: String) {
        let cost = Int(image.size.width * image.size.height * 4) // Approximate memory cost
        imageCache.setObject(image, forKey: key as NSString, cost: cost)
    }
    
    func getCachedImage(forKey key: String) -> UIImage? {
        return imageCache.object(forKey: key as NSString)
    }
    
    func removeCachedImage(forKey key: String) {
        imageCache.removeObject(forKey: key as NSString)
    }
    
    private func cleanupImageResources() {
        imageCache.removeAllObjects()
        LoggingService.shared.info("Image resources cleaned up", category: .performance)
    }
    
    // MARK: - Data Cache Management
    
    func cacheData(_ data: Data, forKey key: String) {
        let nsData = NSData(data: data)
        dataCache.setObject(nsData, forKey: key as NSString, cost: data.count)
    }
    
    func getCachedData(forKey key: String) -> Data? {
        guard let nsData = dataCache.object(forKey: key as NSString) else { return nil }
        return Data(referencing: nsData)
    }
    
    func removeCachedData(forKey key: String) {
        dataCache.removeObject(forKey: key as NSString)
    }
    
    // MARK: - Cache Management
    
    func clearAllCaches() {
        audioCache.removeAllObjects()
        imageCache.removeAllObjects()
        dataCache.removeAllObjects()
        
        LoggingService.shared.info("All caches cleared", category: .performance)
    }
    
    func clearAudioCache() {
        audioCache.removeAllObjects()
        LoggingService.shared.info("Audio cache cleared", category: .performance)
    }
    
    func clearImageCache() {
        imageCache.removeAllObjects()
        LoggingService.shared.info("Image cache cleared", category: .performance)
    }
    
    func clearDataCache() {
        dataCache.removeAllObjects()
        LoggingService.shared.info("Data cache cleared", category: .performance)
    }
    
    // MARK: - Memory Usage Monitoring
    
    func getCurrentMemoryUsage() -> MemoryUsage {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let usedMemory = UInt64(info.resident_size)
            let totalMemory = UInt64(ProcessInfo.processInfo.physicalMemory)
            
            return MemoryUsage(
                used: usedMemory,
                total: totalMemory,
                available: totalMemory - usedMemory,
                audioCacheSize: getAudioCacheSize(),
                imageCacheSize: getImageCacheSize(),
                dataCacheSize: getDataCacheSize()
            )
        } else {
            return MemoryUsage(used: 0, total: 0, available: 0, audioCacheSize: 0, imageCacheSize: 0, dataCacheSize: 0)
        }
    }
    
    private func getAudioCacheSize() -> UInt64 {
        // Approximate cache size calculation
        return UInt64(audioCache.totalCostLimit)
    }
    
    private func getImageCacheSize() -> UInt64 {
        return UInt64(imageCache.totalCostLimit)
    }
    
    private func getDataCacheSize() -> UInt64 {
        return UInt64(dataCache.totalCostLimit)
    }
    
    // MARK: - Automatic Memory Management
    
    func performMemoryOptimization() {
        let memoryUsage = getCurrentMemoryUsage()
        let usagePercentage = Double(memoryUsage.used) / Double(memoryUsage.total) * 100
        
        LoggingService.shared.info("Memory optimization check", category: .performance, metadata: [
            "usagePercentage": String(format: "%.1f", usagePercentage),
            "usedMemory": String(memoryUsage.used),
            "totalMemory": String(memoryUsage.total)
        ])
        
        if usagePercentage > 80 {
            // High memory usage - aggressive cleanup
            LoggingService.shared.warning("High memory usage detected - performing aggressive cleanup", category: .performance)
            clearAllCaches()
            
            // Force garbage collection
            autoreleasepool {
                cleanupAudioResources()
                cleanupImageResources()
            }
        } else if usagePercentage > 60 {
            // Moderate memory usage - selective cleanup
            LoggingService.shared.info("Moderate memory usage - performing selective cleanup", category: .performance)
            
            // Clear least recently used items from caches
            audioCache.removeAllObjects()
            
            // Keep image and data caches but reduce their limits
            imageCache.totalCostLimit = imageCache.totalCostLimit / 2
            dataCache.totalCostLimit = dataCache.totalCostLimit / 2
        }
    }
    
    // MARK: - Scheduled Memory Management
    
    func startPeriodicMemoryOptimization() {
        Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.performMemoryOptimization()
            }
        }
        
        LoggingService.shared.info("Periodic memory optimization started", category: .performance)
    }
}

// MARK: - Memory Usage Structure

struct MemoryUsage {
    let used: UInt64
    let total: UInt64
    let available: UInt64
    let audioCacheSize: UInt64
    let imageCacheSize: UInt64
    let dataCacheSize: UInt64
    
    var usagePercentage: Double {
        guard total > 0 else { return 0 }
        return Double(used) / Double(total) * 100
    }
    
    var formattedUsed: String {
        return ByteCountFormatter.string(fromByteCount: Int64(used), countStyle: .memory)
    }
    
    var formattedTotal: String {
        return ByteCountFormatter.string(fromByteCount: Int64(total), countStyle: .memory)
    }
    
    var formattedAvailable: String {
        return ByteCountFormatter.string(fromByteCount: Int64(available), countStyle: .memory)
    }
}
