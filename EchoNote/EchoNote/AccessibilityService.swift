//
//  AccessibilityService.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import SwiftUI
import UIKit

// MARK: - Accessibility Service

@MainActor
class AccessibilityService: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var isVoiceOverEnabled = UIAccessibility.isVoiceOverRunning
    @Published var isReduceMotionEnabled = UIAccessibility.isReduceMotionEnabled
    @Published var isReduceTransparencyEnabled = UIAccessibility.isReduceTransparencyEnabled
    @Published var isBoldTextEnabled = UIAccessibility.isBoldTextEnabled
    @Published var isButtonShapesEnabled = false // UIAccessibility.isButtonShapesEnabled not available in iOS 18
    @Published var isDarkerSystemColorsEnabled = UIAccessibility.isDarkerSystemColorsEnabled
    @Published var preferredContentSizeCategory = UIApplication.shared.preferredContentSizeCategory
    
    // MARK: - Properties
    
    static let shared = AccessibilityService()
    
    private var observers: [NSObjectProtocol] = []
    
    // MARK: - Initialization
    
    private init() {
        setupAccessibilityObservers()
    }
    
    deinit {
        Task { @MainActor in
            removeObservers()
        }
    }
    
    // MARK: - Accessibility Observers
    
    private func setupAccessibilityObservers() {
        // VoiceOver status observer
        let voiceOverObserver = NotificationCenter.default.addObserver(
            forName: UIAccessibility.voiceOverStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.isVoiceOverEnabled = UIAccessibility.isVoiceOverRunning
                LoggingService.shared.info("VoiceOver status changed: \(UIAccessibility.isVoiceOverRunning)", category: .ui)
            }
        }
        observers.append(voiceOverObserver)
        
        // Reduce motion observer
        let reduceMotionObserver = NotificationCenter.default.addObserver(
            forName: UIAccessibility.reduceMotionStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.isReduceMotionEnabled = UIAccessibility.isReduceMotionEnabled
                LoggingService.shared.info("Reduce motion status changed: \(UIAccessibility.isReduceMotionEnabled)", category: .ui)
            }
        }
        observers.append(reduceMotionObserver)
        
        // Reduce transparency observer
        let reduceTransparencyObserver = NotificationCenter.default.addObserver(
            forName: UIAccessibility.reduceTransparencyStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.isReduceTransparencyEnabled = UIAccessibility.isReduceTransparencyEnabled
                LoggingService.shared.info("Reduce transparency status changed: \(UIAccessibility.isReduceTransparencyEnabled)", category: .ui)
            }
        }
        observers.append(reduceTransparencyObserver)

        // Bold text observer
        let boldTextObserver = NotificationCenter.default.addObserver(
            forName: UIAccessibility.boldTextStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.isBoldTextEnabled = UIAccessibility.isBoldTextEnabled
                LoggingService.shared.info("Bold text status changed: \(UIAccessibility.isBoldTextEnabled)", category: .ui)
            }
        }
        observers.append(boldTextObserver)
        
        // Button shapes observer - commented out as API not available in iOS 18
        // let buttonShapesObserver = NotificationCenter.default.addObserver(
        //     forName: UIAccessibility.buttonShapesEnabledStatusDidChangeNotification,
        //     object: nil,
        //     queue: .main
        // ) { [weak self] _ in
        //     self?.isButtonShapesEnabled = UIAccessibility.isButtonShapesEnabled
        //     LoggingService.shared.info("Button shapes status changed: \(UIAccessibility.isButtonShapesEnabled)", category: .ui)
        // }
        // observers.append(buttonShapesObserver)
        
        // Darker system colors observer
        let darkerColorsObserver = NotificationCenter.default.addObserver(
            forName: UIAccessibility.darkerSystemColorsStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.isDarkerSystemColorsEnabled = UIAccessibility.isDarkerSystemColorsEnabled
                LoggingService.shared.info("Darker system colors status changed: \(UIAccessibility.isDarkerSystemColorsEnabled)", category: .ui)
            }
        }
        observers.append(darkerColorsObserver)

        // Content size category observer
        let contentSizeObserver = NotificationCenter.default.addObserver(
            forName: UIContentSizeCategory.didChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.preferredContentSizeCategory = UIApplication.shared.preferredContentSizeCategory
                LoggingService.shared.info("Content size category changed: \(UIApplication.shared.preferredContentSizeCategory.rawValue)", category: .ui)
            }
        }
        observers.append(contentSizeObserver)
    }
    
    private func removeObservers() {
        observers.forEach { NotificationCenter.default.removeObserver($0) }
        observers.removeAll()
    }
    
    // MARK: - Accessibility Helpers
    
    /// Check if large text is enabled
    var isLargeTextEnabled: Bool {
        return preferredContentSizeCategory.isAccessibilityCategory
    }
    
    /// Get appropriate animation duration based on reduce motion setting
    func animationDuration(_ defaultDuration: Double) -> Double {
        return isReduceMotionEnabled ? 0.0 : defaultDuration
    }
    
    /// Get appropriate spring animation based on reduce motion setting
    func springAnimation(response: Double = 0.5, dampingFraction: Double = 0.8) -> Animation {
        if isReduceMotionEnabled {
            return .linear(duration: 0.0)
        } else {
            return .spring(response: response, dampingFraction: dampingFraction)
        }
    }
    
    /// Get appropriate opacity for backgrounds based on reduce transparency setting
    func backgroundOpacity(_ defaultOpacity: Double) -> Double {
        return isReduceTransparencyEnabled ? 1.0 : defaultOpacity
    }
    
    /// Get appropriate font weight based on bold text setting
    func fontWeight(_ defaultWeight: Font.Weight) -> Font.Weight {
        return isBoldTextEnabled ? .bold : defaultWeight
    }
    
    /// Get appropriate button style based on button shapes setting
    func buttonBorderWidth() -> CGFloat {
        return isButtonShapesEnabled ? 2.0 : 0.0
    }
    
    /// Post accessibility announcement
    func announce(_ message: String) {
        UIAccessibility.post(notification: .announcement, argument: message)
        LoggingService.shared.info("Accessibility announcement: \(message)", category: .ui)
    }
    
    /// Post layout change notification
    func announceLayoutChange(focusElement: Any? = nil) {
        UIAccessibility.post(notification: .layoutChanged, argument: focusElement)
        LoggingService.shared.info("Accessibility layout change announced", category: .ui)
    }
    
    /// Post screen change notification
    func announceScreenChange(focusElement: Any? = nil) {
        UIAccessibility.post(notification: .screenChanged, argument: focusElement)
        LoggingService.shared.info("Accessibility screen change announced", category: .ui)
    }
    
    // MARK: - VoiceOver Helpers
    
    /// Create accessibility label for recording duration
    func recordingDurationLabel(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        
        if minutes > 0 {
            return "Recording duration: \(minutes) minutes and \(seconds) seconds"
        } else {
            return "Recording duration: \(seconds) seconds"
        }
    }
    
    /// Create accessibility label for audio level
    func audioLevelLabel(_ level: Float) -> String {
        let percentage = Int(level * 100)
        return "Audio level: \(percentage) percent"
    }
    
    /// Create accessibility label for keyword
    func keywordLabel(_ keyword: Keyword) -> String {
        let status = keyword.isEnabled ? "enabled" : "disabled"
        return "Keyword: \(keyword.text), \(status)"
    }
    
    /// Create accessibility label for note
    func noteLabel(_ note: Note) -> String {
        let favoriteStatus = note.isFavorite ? "favorite" : ""
        let audioStatus = note.audioURL != nil ? "with audio" : "text only"
        let keywordCount = note.keywords.count
        let keywordText = keywordCount > 0 ? "\(keywordCount) keywords" : "no keywords"
        
        return "\(note.title), \(audioStatus), \(keywordText), \(favoriteStatus)"
    }
    
    /// Create accessibility hint for button
    func buttonHint(_ action: String) -> String {
        return "Double tap to \(action)"
    }
    
    // MARK: - Dynamic Type Support
    
    /// Get scaled font size
    func scaledFont(_ style: Font.TextStyle, size: CGFloat? = nil) -> Font {
        if let size = size {
            return Font.custom("", size: size).weight(fontWeight(.regular))
        } else {
            return Font.system(style).weight(fontWeight(.regular))
        }
    }
    
    /// Get minimum touch target size
    var minimumTouchTargetSize: CGFloat {
        return isLargeTextEnabled ? 60.0 : 44.0
    }
    
    // MARK: - Color Accessibility
    
    /// Get high contrast color if needed
    func accessibleColor(_ defaultColor: Color, highContrastColor: Color) -> Color {
        return isDarkerSystemColorsEnabled ? highContrastColor : defaultColor
    }
    
    /// Get accessible background color
    func accessibleBackgroundColor(_ defaultColor: Color) -> Color {
        if isReduceTransparencyEnabled {
            return defaultColor.opacity(1.0)
        } else {
            return defaultColor
        }
    }
    
    // MARK: - Accessibility Testing
    
    func getAccessibilityStatus() -> AccessibilityStatus {
        return AccessibilityStatus(
            isVoiceOverEnabled: isVoiceOverEnabled,
            isReduceMotionEnabled: isReduceMotionEnabled,
            isReduceTransparencyEnabled: isReduceTransparencyEnabled,
            isBoldTextEnabled: isBoldTextEnabled,
            isButtonShapesEnabled: isButtonShapesEnabled,
            isDarkerSystemColorsEnabled: isDarkerSystemColorsEnabled,
            preferredContentSizeCategory: preferredContentSizeCategory,
            isLargeTextEnabled: isLargeTextEnabled
        )
    }
}

// MARK: - Accessibility Status

struct AccessibilityStatus {
    let isVoiceOverEnabled: Bool
    let isReduceMotionEnabled: Bool
    let isReduceTransparencyEnabled: Bool
    let isBoldTextEnabled: Bool
    let isButtonShapesEnabled: Bool
    let isDarkerSystemColorsEnabled: Bool
    let preferredContentSizeCategory: UIContentSizeCategory
    let isLargeTextEnabled: Bool
}

// MARK: - SwiftUI Extensions

extension View {
    /// Apply accessibility-aware animation
    func accessibleAnimation(_ animation: Animation) -> some View {
        let accessibilityService = AccessibilityService.shared
        return self.animation(accessibilityService.isReduceMotionEnabled ? .linear(duration: 0) : animation, value: UUID())
    }
    
    /// Apply accessibility-aware spring animation
    func accessibleSpringAnimation(response: Double = 0.5, dampingFraction: Double = 0.8) -> some View {
        let accessibilityService = AccessibilityService.shared
        return self.animation(accessibilityService.springAnimation(response: response, dampingFraction: dampingFraction), value: UUID())
    }
    
    /// Apply minimum touch target size
    func accessibleTouchTarget() -> some View {
        let accessibilityService = AccessibilityService.shared
        return self.frame(minWidth: accessibilityService.minimumTouchTargetSize, minHeight: accessibilityService.minimumTouchTargetSize)
    }
}
