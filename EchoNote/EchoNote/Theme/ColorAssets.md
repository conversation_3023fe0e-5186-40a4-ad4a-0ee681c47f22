# Color Assets Configuration

This document describes the color assets that need to be created in the Xcode Asset Catalog for the theme system.

## Required Color Assets

### Background Colors
1. **BackgroundDefault** - Default black background
   - Light: #000000 (Black)
   - Dark: #000000 (Black)

2. **BackgroundMidnight** - Deep blue-black
   - Light: #0D0D26 (Dark blue)
   - Dark: #0D0D26 (Dark blue)

3. **BackgroundOcean** - Deep ocean blue
   - Light: #003366 (Navy blue)
   - Dark: #003366 (Navy blue)

4. **BackgroundForest** - Deep forest green
   - Light: #1A331A (Dark green)
   - Dark: #1A331A (Dark green)

5. **BackgroundSunset** - Deep purple-red
   - Light: #4D1A33 (Dark purple-red)
   - Dark: #4D1A33 (Dark purple-red)

6. **BackgroundCosmic** - Deep space purple
   - Light: #1A0D4D (Dark purple)
   - Dark: #1A0D4D (Dark purple)

### Core Colors
7. **PrimaryColor** - Main brand color
   - Light: #00FF00 (Bright green)
   - Dark: #00FF00 (Bright green)

8. **SecondaryColor** - Secondary brand color
   - Light: #666666 (Medium gray)
   - Dark: #999999 (Light gray)

9. **AccentColor** - Accent/CTA color
   - Light: #007AFF (Blue)
   - Dark: #0A84FF (Light blue)

### Text Colors
10. **TextPrimary** - Primary text
    - Light: #000000 (Black)
    - Dark: #FFFFFF (White)

11. **TextSecondary** - Secondary text
    - Light: #666666 (Medium gray)
    - Dark: #CCCCCC (Light gray)

12. **TextTertiary** - Tertiary text
    - Light: #999999 (Light gray)
    - Dark: #666666 (Medium gray)

### Surface Colors
13. **SurfaceColor** - Cards and containers
    - Light: #F5F5F5 (Light gray)
    - Dark: #1C1C1E (Dark gray)

14. **BorderColor** - Borders and dividers
    - Light: #E5E5E5 (Very light gray)
    - Dark: #333333 (Dark gray)

### State Colors
15. **SuccessColor** - Success states
    - Light: #34C759 (Green)
    - Dark: #30D158 (Light green)

16. **WarningColor** - Warning states
    - Light: #FF9500 (Orange)
    - Dark: #FF9F0A (Light orange)

17. **ErrorColor** - Error states
    - Light: #FF3B30 (Red)
    - Dark: #FF453A (Light red)

### Semantic Colors
18. **RecordingColor** - Recording state
    - Light: #00FF00 (Bright green)
    - Dark: #00FF00 (Bright green)

19. **PlaybackColor** - Playback state
    - Light: #007AFF (Blue)
    - Dark: #0A84FF (Light blue)

20. **FavoriteColor** - Favorite/heart color
    - Light: #FF3B30 (Red)
    - Dark: #FF453A (Light red)

### Keyword Colors
21. **KeywordColor1** - First keyword color
    - Light: #007AFF (Blue)
    - Dark: #0A84FF (Light blue)

22. **KeywordColor2** - Second keyword color
    - Light: #34C759 (Green)
    - Dark: #30D158 (Light green)

23. **KeywordColor3** - Third keyword color
    - Light: #FF9500 (Orange)
    - Dark: #FF9F0A (Light orange)

24. **KeywordColor4** - Fourth keyword color
    - Light: #AF52DE (Purple)
    - Dark: #BF5AF2 (Light purple)

25. **KeywordColor5** - Fifth keyword color
    - Light: #FF2D92 (Pink)
    - Dark: #FF375F (Light pink)

26. **KeywordColor6** - Sixth keyword color
    - Light: #5AC8FA (Cyan)
    - Dark: #64D2FF (Light cyan)

## Instructions for Adding to Asset Catalog

1. Open `Assets.xcassets` in Xcode
2. Right-click and select "New Color Set"
3. Name the color set exactly as listed above (case-sensitive)
4. Set the "Appearances" to "Any, Dark"
5. Configure the light and dark mode colors as specified
6. Ensure "Provide Namespace" is unchecked for global access

## Usage in Code

```swift
// Access colors through AppTheme
let backgroundColor = AppTheme.shared.applyBackground()
let primaryColor = AppTheme.shared.primaryColor
let textColor = AppTheme.shared.textPrimary

// Or directly through Color assets
let directColor = Color("PrimaryColor")
```
