//
//  AppTheme.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI

// MARK: - App Theme Manager

/// Centralized theme management system for the entire app
/// Handles color schemes, background customization, and theme persistence
@MainActor
class ThemeManager: ObservableObject {

    // MARK: - Singleton

    static let shared = ThemeManager()
    
    // MARK: - Published Properties
    
    /// Current background theme selected by user
    @Published var currentBackground: BackgroundTheme {
        didSet {
            saveBackgroundSetting()
        }
    }
    
    /// Current appearance mode setting
    @Published var appearanceMode: AppearanceMode {
        didSet {
            saveAppearanceSetting()
            applyAppearanceMode()
        }
    }
    
    // MARK: - Initialization
    
    private init() {
        self.currentBackground = Self.loadBackgroundSetting()
        self.appearanceMode = Self.loadAppearanceSetting()
        applyAppearanceMode()
    }
    
    // MARK: - Background Theme Management
    
    /// Apply the current background theme to the environment
    func applyBackground() -> Color {
        // For solid colors, return the color directly
        // For gradients, we'll return the primary color (gradients will be handled in the view modifier)
        switch currentBackground {
        // Solid Colors
        case .default:
            return .black
        case .midnight:
            return Color(red: 0.05, green: 0.05, blue: 0.15)
        case .ocean:
            return Color(red: 0.0, green: 0.2, blue: 0.4)
        case .forest:
            return Color(red: 0.1, green: 0.2, blue: 0.1)
        case .sunset:
            return Color(red: 0.3, green: 0.1, blue: 0.2)
        case .cosmic:
            return Color(red: 0.1, green: 0.0, blue: 0.3)

        // Rich Solid Colors
        case .deepPurple:
            return Color(red: 0.2, green: 0.0, blue: 0.4)
        case .emerald:
            return Color(red: 0.0, green: 0.3, blue: 0.2)
        case .crimson:
            return Color(red: 0.4, green: 0.0, blue: 0.1)
        case .sapphire:
            return Color(red: 0.0, green: 0.1, blue: 0.4)
        case .amber:
            return Color(red: 0.4, green: 0.2, blue: 0.0)
        case .rose:
            return Color(red: 0.3, green: 0.1, blue: 0.2)
        case .charcoal:
            return Color(red: 0.1, green: 0.1, blue: 0.1)
        case .navy:
            return Color(red: 0.0, green: 0.05, blue: 0.2)

        // Gradient Themes (return primary color, actual gradient handled in modifier)
        case .oceanWave:
            return Color(red: 0.0, green: 0.3, blue: 0.6)
        case .sunsetGlow:
            return Color(red: 0.6, green: 0.2, blue: 0.4)
        case .cosmicNebula:
            return Color(red: 0.3, green: 0.0, blue: 0.6)
        case .forestMist:
            return Color(red: 0.1, green: 0.4, blue: 0.2)
        case .purpleDream:
            return Color(red: 0.4, green: 0.1, blue: 0.6)
        case .fireGlow:
            return Color(red: 0.6, green: 0.1, blue: 0.0)
        case .arcticAurora:
            return Color(red: 0.0, green: 0.4, blue: 0.6)
        case .tropicalSunset:
            return Color(red: 0.6, green: 0.3, blue: 0.1)
        case .deepSpace:
            return Color(red: 0.05, green: 0.0, blue: 0.2)
        case .enchantedForest:
            return Color(red: 0.2, green: 0.4, blue: 0.1)
        }
    }
    
    /// Update the background theme and persist the change
    func setBackground(_ theme: BackgroundTheme) {
        currentBackground = theme
        LoggingService.shared.info("Background theme changed to: \(theme.displayName)", category: .ui)
    }

    /// Check if the current theme is a gradient
    var isGradientTheme: Bool {
        currentBackground.isGradient
    }

    /// Get the gradient for the current theme (if applicable)
    func applyGradient() -> LinearGradient? {
        currentBackground.gradient
    }

    /// Update the appearance mode and apply it
    func setAppearanceMode(_ mode: AppearanceMode) {
        appearanceMode = mode
        LoggingService.shared.info("Appearance mode changed to: \(mode.displayName)", category: .ui)
    }

    /// Apply the current appearance mode to the app
    private func applyAppearanceMode() {
        DispatchQueue.main.async {
            guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let window = windowScene.windows.first else { return }

            switch self.appearanceMode {
            case .light:
                window.overrideUserInterfaceStyle = .light
            case .dark:
                window.overrideUserInterfaceStyle = .dark
            case .system:
                window.overrideUserInterfaceStyle = .unspecified
            }
        }
    }
    
    // MARK: - Core Colors (Asset Catalog Based)
    
    /// Primary brand color - adapts to light/dark mode
    var primaryColor: Color {
        .green
    }

    /// Secondary brand color - adapts to light/dark mode
    var secondaryColor: Color {
        .gray
    }

    /// Accent color for highlights and CTAs
    var accentColor: Color {
        .blue
    }

    /// Primary text color - adapts to light/dark mode
    var textPrimary: Color {
        .white
    }

    /// Secondary text color - adapts to light/dark mode
    var textSecondary: Color {
        .gray
    }

    /// Tertiary text color for subtle elements
    var textTertiary: Color {
        Color.gray.opacity(0.6)
    }

    /// Surface color for cards and containers
    var surfaceColor: Color {
        Color.gray.opacity(0.2)
    }

    /// Border color for dividers and outlines
    var borderColor: Color {
        Color.gray.opacity(0.3)
    }
    
    /// Success state color
    var successColor: Color {
        .green
    }

    /// Warning state color
    var warningColor: Color {
        .orange
    }

    /// Error state color
    var errorColor: Color {
        .red
    }

    // MARK: - Semantic Colors

    /// Recording state color (green)
    var recordingColor: Color {
        .green
    }

    /// Playback state color (blue)
    var playbackColor: Color {
        .blue
    }

    /// Favorite/heart color (red)
    var favoriteColor: Color {
        .red
    }

    /// Keyword tag colors
    var keywordColors: [Color] {
        [
            .blue,
            .green,
            .orange,
            .purple,
            .pink,
            .cyan
        ]
    }
    
    // MARK: - Persistence

    private static let backgroundKey = "AppTheme.BackgroundTheme"
    private static let appearanceKey = "AppTheme.AppearanceMode"

    /// Save the current background setting to UserDefaults
    private func saveBackgroundSetting() {
        UserDefaults.standard.set(currentBackground.rawValue, forKey: Self.backgroundKey)
        UserDefaults.standard.synchronize()
    }

    /// Load the saved background setting from UserDefaults
    private static func loadBackgroundSetting() -> BackgroundTheme {
        let savedValue = UserDefaults.standard.string(forKey: backgroundKey)
        return BackgroundTheme(rawValue: savedValue ?? "") ?? .default
    }

    /// Save the current appearance setting to UserDefaults
    private func saveAppearanceSetting() {
        UserDefaults.standard.set(appearanceMode.rawValue, forKey: Self.appearanceKey)
        UserDefaults.standard.synchronize()
    }

    /// Load the saved appearance setting from UserDefaults
    private static func loadAppearanceSetting() -> AppearanceMode {
        let savedValue = UserDefaults.standard.string(forKey: appearanceKey)
        return AppearanceMode(rawValue: savedValue ?? "") ?? .system
    }
}

// MARK: - Appearance Mode Enum

/// Available appearance modes for the app
enum AppearanceMode: String, CaseIterable, Identifiable {
    case light = "light"
    case dark = "dark"
    case system = "system"

    var id: String { rawValue }

    /// Human-readable display name
    var displayName: String {
        switch self {
        case .light:
            return "Light"
        case .dark:
            return "Dark"
        case .system:
            return "System"
        }
    }

    /// Icon representation for the appearance mode
    var iconName: String {
        switch self {
        case .light:
            return "sun.max.fill"
        case .dark:
            return "moon.fill"
        case .system:
            return "gear"
        }
    }

    /// Description of the appearance mode
    var description: String {
        switch self {
        case .light:
            return "Always light"
        case .dark:
            return "Always dark"
        case .system:
            return "Follow device"
        }
    }
}

// MARK: - Background Theme Enum

/// Available background themes for user customization
enum BackgroundTheme: String, CaseIterable, Identifiable {
    // Solid Colors
    case `default` = "default"
    case midnight = "midnight"
    case ocean = "ocean"
    case forest = "forest"
    case sunset = "sunset"
    case cosmic = "cosmic"

    // Rich Solid Colors
    case deepPurple = "deepPurple"
    case emerald = "emerald"
    case crimson = "crimson"
    case sapphire = "sapphire"
    case amber = "amber"
    case rose = "rose"
    case charcoal = "charcoal"
    case navy = "navy"

    // Gradient Themes
    case oceanWave = "oceanWave"
    case sunsetGlow = "sunsetGlow"
    case cosmicNebula = "cosmicNebula"
    case forestMist = "forestMist"
    case purpleDream = "purpleDream"
    case fireGlow = "fireGlow"
    case arcticAurora = "arcticAurora"
    case tropicalSunset = "tropicalSunset"
    case deepSpace = "deepSpace"
    case enchantedForest = "enchantedForest"

    var id: String { rawValue }
    
    /// Human-readable display name
    var displayName: String {
        switch self {
        // Solid Colors
        case .default:
            return "Default"
        case .midnight:
            return "Midnight"
        case .ocean:
            return "Ocean"
        case .forest:
            return "Forest"
        case .sunset:
            return "Sunset"
        case .cosmic:
            return "Cosmic"

        // Rich Solid Colors
        case .deepPurple:
            return "Deep Purple"
        case .emerald:
            return "Emerald"
        case .crimson:
            return "Crimson"
        case .sapphire:
            return "Sapphire"
        case .amber:
            return "Amber"
        case .rose:
            return "Rose"
        case .charcoal:
            return "Charcoal"
        case .navy:
            return "Navy"

        // Gradient Themes
        case .oceanWave:
            return "Ocean Wave"
        case .sunsetGlow:
            return "Sunset Glow"
        case .cosmicNebula:
            return "Cosmic Nebula"
        case .forestMist:
            return "Forest Mist"
        case .purpleDream:
            return "Purple Dream"
        case .fireGlow:
            return "Fire Glow"
        case .arcticAurora:
            return "Arctic Aurora"
        case .tropicalSunset:
            return "Tropical Sunset"
        case .deepSpace:
            return "Deep Space"
        case .enchantedForest:
            return "Enchanted Forest"
        }
    }
    
    /// Icon representation for the theme
    var iconName: String {
        switch self {
        // Solid Colors
        case .default:
            return "circle.fill"
        case .midnight:
            return "moon.fill"
        case .ocean:
            return "drop.fill"
        case .forest:
            return "leaf.fill"
        case .sunset:
            return "sun.max.fill"
        case .cosmic:
            return "sparkles"

        // Rich Solid Colors
        case .deepPurple:
            return "diamond.fill"
        case .emerald:
            return "gem"
        case .crimson:
            return "heart.fill"
        case .sapphire:
            return "star.fill"
        case .amber:
            return "sun.min.fill"
        case .rose:
            return "rosette"
        case .charcoal:
            return "square.fill"
        case .navy:
            return "anchor.fill"

        // Gradient Themes
        case .oceanWave:
            return "water.waves"
        case .sunsetGlow:
            return "sun.and.horizon.fill"
        case .cosmicNebula:
            return "cloud.fill"
        case .forestMist:
            return "tree.fill"
        case .purpleDream:
            return "moon.stars.fill"
        case .fireGlow:
            return "flame.fill"
        case .arcticAurora:
            return "snowflake"
        case .tropicalSunset:
            return "beach.umbrella.fill"
        case .deepSpace:
            return "globe.americas.fill"
        case .enchantedForest:
            return "tree.circle.fill"
        }
    }
    
    /// Preview color for theme selection
    var previewColor: Color {
        switch self {
        // Solid Colors
        case .default:
            return .black
        case .midnight:
            return Color(red: 0.05, green: 0.05, blue: 0.15)
        case .ocean:
            return Color(red: 0.0, green: 0.2, blue: 0.4)
        case .forest:
            return Color(red: 0.1, green: 0.2, blue: 0.1)
        case .sunset:
            return Color(red: 0.3, green: 0.1, blue: 0.2)
        case .cosmic:
            return Color(red: 0.1, green: 0.0, blue: 0.3)

        // Rich Solid Colors
        case .deepPurple:
            return Color(red: 0.2, green: 0.0, blue: 0.4)
        case .emerald:
            return Color(red: 0.0, green: 0.3, blue: 0.2)
        case .crimson:
            return Color(red: 0.4, green: 0.0, blue: 0.1)
        case .sapphire:
            return Color(red: 0.0, green: 0.1, blue: 0.4)
        case .amber:
            return Color(red: 0.4, green: 0.2, blue: 0.0)
        case .rose:
            return Color(red: 0.3, green: 0.1, blue: 0.2)
        case .charcoal:
            return Color(red: 0.1, green: 0.1, blue: 0.1)
        case .navy:
            return Color(red: 0.0, green: 0.05, blue: 0.2)

        // Gradient Themes (showing primary color)
        case .oceanWave:
            return Color(red: 0.0, green: 0.3, blue: 0.6)
        case .sunsetGlow:
            return Color(red: 0.6, green: 0.2, blue: 0.4)
        case .cosmicNebula:
            return Color(red: 0.3, green: 0.0, blue: 0.6)
        case .forestMist:
            return Color(red: 0.1, green: 0.4, blue: 0.2)
        case .purpleDream:
            return Color(red: 0.4, green: 0.1, blue: 0.6)
        case .fireGlow:
            return Color(red: 0.6, green: 0.1, blue: 0.0)
        case .arcticAurora:
            return Color(red: 0.0, green: 0.4, blue: 0.6)
        case .tropicalSunset:
            return Color(red: 0.6, green: 0.3, blue: 0.1)
        case .deepSpace:
            return Color(red: 0.05, green: 0.0, blue: 0.2)
        case .enchantedForest:
            return Color(red: 0.2, green: 0.4, blue: 0.1)
        }
    }

    /// Check if this theme is a gradient
    var isGradient: Bool {
        switch self {
        case .oceanWave, .sunsetGlow, .cosmicNebula, .forestMist, .purpleDream,
             .fireGlow, .arcticAurora, .tropicalSunset, .deepSpace, .enchantedForest:
            return true
        default:
            return false
        }
    }

    /// Get the gradient for this theme (if applicable)
    var gradient: LinearGradient? {
        guard isGradient else { return nil }

        switch self {
        case .oceanWave:
            return LinearGradient(
                colors: [
                    Color(red: 0.0, green: 0.2, blue: 0.4),
                    Color(red: 0.0, green: 0.4, blue: 0.6),
                    Color(red: 0.1, green: 0.3, blue: 0.5)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

        case .sunsetGlow:
            return LinearGradient(
                colors: [
                    Color(red: 0.6, green: 0.1, blue: 0.2),
                    Color(red: 0.8, green: 0.3, blue: 0.1),
                    Color(red: 0.4, green: 0.1, blue: 0.4)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

        case .cosmicNebula:
            return LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.0, blue: 0.3),
                    Color(red: 0.3, green: 0.0, blue: 0.6),
                    Color(red: 0.5, green: 0.1, blue: 0.4)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

        case .forestMist:
            return LinearGradient(
                colors: [
                    Color(red: 0.05, green: 0.2, blue: 0.1),
                    Color(red: 0.1, green: 0.4, blue: 0.2),
                    Color(red: 0.2, green: 0.3, blue: 0.15)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

        case .purpleDream:
            return LinearGradient(
                colors: [
                    Color(red: 0.2, green: 0.0, blue: 0.4),
                    Color(red: 0.4, green: 0.1, blue: 0.6),
                    Color(red: 0.3, green: 0.0, blue: 0.5)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

        case .fireGlow:
            return LinearGradient(
                colors: [
                    Color(red: 0.4, green: 0.0, blue: 0.0),
                    Color(red: 0.6, green: 0.1, blue: 0.0),
                    Color(red: 0.8, green: 0.2, blue: 0.0)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

        case .arcticAurora:
            return LinearGradient(
                colors: [
                    Color(red: 0.0, green: 0.2, blue: 0.4),
                    Color(red: 0.0, green: 0.4, blue: 0.6),
                    Color(red: 0.1, green: 0.5, blue: 0.3)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

        case .tropicalSunset:
            return LinearGradient(
                colors: [
                    Color(red: 0.6, green: 0.2, blue: 0.0),
                    Color(red: 0.8, green: 0.4, blue: 0.1),
                    Color(red: 0.4, green: 0.3, blue: 0.2)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

        case .deepSpace:
            return LinearGradient(
                colors: [
                    Color(red: 0.0, green: 0.0, blue: 0.1),
                    Color(red: 0.05, green: 0.0, blue: 0.2),
                    Color(red: 0.1, green: 0.0, blue: 0.3)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

        case .enchantedForest:
            return LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.3, blue: 0.1),
                    Color(red: 0.2, green: 0.4, blue: 0.1),
                    Color(red: 0.15, green: 0.5, blue: 0.2)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

        default:
            return nil
        }
    }
}

// MARK: - Theme Environment Key

/// Environment key for accessing the app theme throughout the view hierarchy
struct AppThemeKey: EnvironmentKey {
    @MainActor static let defaultValue = ThemeManager.shared
}

extension EnvironmentValues {
    var appTheme: ThemeManager {
        get { self[AppThemeKey.self] }
        set { self[AppThemeKey.self] = newValue }
    }
}

// MARK: - View Extensions

extension View {
    /// Apply the app theme to the view hierarchy
    func withAppTheme() -> some View {
        self.environmentObject(ThemeManager.shared)
            .environment(\.appTheme, ThemeManager.shared)
    }

    /// Apply the global background color with safe area coverage
    func withGlobalBackground() -> some View {
        self.modifier(GlobalBackgroundModifier())
    }

    /// Apply theme background (solid or gradient) with safe area coverage
    func withThemeBackground(_ themeManager: ThemeManager) -> some View {
        self.background(
            Group {
                if themeManager.isGradientTheme,
                   let gradient = themeManager.applyGradient() {
                    gradient.ignoresSafeArea()
                } else {
                    themeManager.applyBackground().ignoresSafeArea()
                }
            }
        )
    }
}

// MARK: - Global Background Modifier

struct GlobalBackgroundModifier: ViewModifier {
    @StateObject private var themeManager = ThemeManager.shared

    func body(content: Content) -> some View {
        content
            .background(
                Group {
                    if themeManager.isGradientTheme,
                       let gradient = themeManager.applyGradient() {
                        gradient
                            .ignoresSafeArea(.all)
                    } else {
                        themeManager.applyBackground()
                            .ignoresSafeArea(.all)
                    }
                }
            )
    }
}
