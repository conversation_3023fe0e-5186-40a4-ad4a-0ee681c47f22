//
//  AppearanceModeSelector.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI

// MARK: - Appearance Mode Selector

/// A view component for selecting appearance mode (Light/Dark/System)
struct AppearanceModeSelector: View {
    
    // MARK: - Properties
    
    @EnvironmentObject private var appTheme: ThemeManager
    @StateObject private var accessibilityService = AccessibilityService.shared
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedMode: AppearanceMode
    
    // MARK: - Initialization
    
    init(currentMode: AppearanceMode? = nil) {
        self._selectedMode = State(initialValue: currentMode ?? .system)
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Mode Options
                VStack(spacing: 20) {
                    ForEach(AppearanceMode.allCases) { mode in
                        AppearanceModeCard(
                            mode: mode,
                            isSelected: selectedMode == mode,
                            onSelect: {
                                selectMode(mode)
                            }
                        )
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 30)
                
                Spacer()
                
                // Apply <PERSON><PERSON>
                applyButton
            }
            .withGlobalBackground()
            .navigationBarHidden(true)
        }
        .onAppear {
            selectedMode = appTheme.appearanceMode
        }
    }
    
    // MARK: - View Components
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            HStack {
                Button("Cancel") {
                    dismiss()
                }
                .foregroundColor(appTheme.textSecondary)
                
                Spacer()
                
                Text("Appearance")
                    .font(accessibilityService.scaledFont(.headline))
                    .fontWeight(accessibilityService.fontWeight(.semibold))
                    .foregroundColor(appTheme.textPrimary)
                
                Spacer()
                
                // Empty space for symmetry
                Text("Cancel")
                    .foregroundColor(.clear)
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            Divider()
                .background(appTheme.borderColor)
        }
    }
    
    private var applyButton: some View {
        VStack(spacing: 0) {
            Divider()
                .background(appTheme.borderColor)
            
            Button(action: applySelectedMode) {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20))
                    
                    Text("Apply")
                        .font(accessibilityService.scaledFont(.headline))
                        .fontWeight(accessibilityService.fontWeight(.semibold))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(appTheme.accentColor)
                )
            }
            .disabled(selectedMode == appTheme.appearanceMode)
            .opacity(selectedMode == appTheme.appearanceMode ? 0.6 : 1.0)
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .accessibilityLabel("Apply selected appearance mode")
            .accessibilityHint(accessibilityService.buttonHint("apply the selected appearance mode"))
        }
    }
    
    // MARK: - Actions
    
    private func selectMode(_ mode: AppearanceMode) {
        withAnimation(.easeInOut(duration: 0.2)) {
            selectedMode = mode
        }
        
        // Provide haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        LoggingService.shared.info("Appearance mode selected: \(mode.displayName)", category: .ui)
    }
    
    private func applySelectedMode() {
        withAnimation(.easeInOut(duration: 0.3)) {
            appTheme.setAppearanceMode(selectedMode)
        }
        
        // Provide success haptic feedback
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
        
        // Dismiss after a brief delay to show the change
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            dismiss()
        }
    }
}

// MARK: - Appearance Mode Card

struct AppearanceModeCard: View {
    
    // MARK: - Properties
    
    let mode: AppearanceMode
    let isSelected: Bool
    let onSelect: () -> Void
    
    @EnvironmentObject private var appTheme: ThemeManager
    @StateObject private var accessibilityService = AccessibilityService.shared
    
    // MARK: - Body
    
    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 16) {
                // Icon
                Image(systemName: mode.iconName)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(isSelected ? appTheme.accentColor : appTheme.textSecondary)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(isSelected ? appTheme.accentColor.opacity(0.2) : appTheme.surfaceColor)
                    )
                
                // Text Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(mode.displayName)
                        .font(accessibilityService.scaledFont(.headline))
                        .fontWeight(accessibilityService.fontWeight(.semibold))
                        .foregroundColor(isSelected ? appTheme.accentColor : appTheme.textPrimary)
                    
                    Text(mode.description)
                        .font(accessibilityService.scaledFont(.subheadline))
                        .foregroundColor(appTheme.textSecondary)
                }
                
                Spacer()
                
                // Selection Indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(appTheme.accentColor)
                        .transition(.scale.combined(with: .opacity))
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(appTheme.surfaceColor.opacity(0.5))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                isSelected ? appTheme.accentColor : appTheme.borderColor,
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
        .accessibilityLabel("\(mode.displayName) appearance mode")
        .accessibilityHint(accessibilityService.buttonHint("select \(mode.displayName) appearance mode"))
        .accessibilityAddTraits(isSelected ? [.isSelected] : [])
    }
}

// MARK: - Preview

#Preview {
    AppearanceModeSelector()
        .withAppTheme()
}
