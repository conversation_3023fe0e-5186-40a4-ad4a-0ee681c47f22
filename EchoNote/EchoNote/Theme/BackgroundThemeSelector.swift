//
//  BackgroundThemeSelector.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI

// MARK: - Background Theme Selector

/// A view component for selecting and previewing background themes
struct BackgroundThemeSelector: View {
    
    // MARK: - Properties
    
    @EnvironmentObject private var appTheme: ThemeManager
    @StateObject private var accessibilityService = AccessibilityService.shared
    @Environment(\.dismiss) private var dismiss

    @State private var selectedTheme: BackgroundTheme
    @State private var showingPreview = false
    @State private var originalTheme: BackgroundTheme

    // MARK: - Initialization

    init(currentTheme: BackgroundTheme? = nil) {
        let theme = currentTheme ?? .default
        self._selectedTheme = State(initialValue: theme)
        self._originalTheme = State(initialValue: theme)
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Theme Grid
                ScrollView {
                    VStack(spacing: 30) {
                        // Solid Colors Section
                        themeSection(
                            title: "Solid Colors",
                            themes: [.default, .midnight, .ocean, .forest, .sunset, .cosmic]
                        )

                        // Rich Colors Section
                        themeSection(
                            title: "Rich Colors",
                            themes: [.deepPurple, .emerald, .crimson, .sapphire, .amber, .rose, .charcoal, .navy]
                        )

                        // Gradient Themes Section
                        themeSection(
                            title: "Gradient Themes",
                            themes: [.oceanWave, .sunsetGlow, .cosmicNebula, .forestMist, .purpleDream, .fireGlow, .arcticAurora, .tropicalSunset, .deepSpace, .enchantedForest]
                        )
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                }
                
                // Apply Button
                applyButton
            }
            .withGlobalBackground()
            .navigationBarHidden(true)
        }
        .onAppear {
            selectedTheme = appTheme.currentBackground
            originalTheme = appTheme.currentBackground
        }
        .onDisappear {
            // Restore original theme if preview was active but not applied
            if showingPreview && appTheme.currentBackground != originalTheme {
                appTheme.setBackground(originalTheme)
            }
        }
    }
    
    // MARK: - View Components
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            HStack {
                Button("Cancel") {
                    dismiss()
                }
                .foregroundColor(appTheme.textSecondary)
                
                Spacer()
                
                VStack(spacing: 4) {
                    Text("Background Theme")
                        .font(accessibilityService.scaledFont(.headline))
                        .fontWeight(accessibilityService.fontWeight(.semibold))
                        .foregroundColor(appTheme.textPrimary)

                    if showingPreview {
                        Text("Preview Mode")
                            .font(accessibilityService.scaledFont(.caption))
                            .foregroundColor(appTheme.warningColor)
                            .transition(.opacity.combined(with: .scale))
                    }
                }
                
                Spacer()
                
                Button(showingPreview ? "Stop Preview" : "Preview") {
                    togglePreview()
                }
                .foregroundColor(showingPreview ? appTheme.warningColor : appTheme.accentColor)
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            Divider()
                .background(appTheme.borderColor)
        }
    }
    
    private var applyButton: some View {
        VStack(spacing: 0) {
            Divider()
                .background(appTheme.borderColor)
            
            Button(action: applySelectedTheme) {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20))
                    
                    Text(showingPreview ? "Apply & Save" : "Apply Theme")
                        .font(accessibilityService.scaledFont(.headline))
                        .fontWeight(accessibilityService.fontWeight(.semibold))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(appTheme.accentColor)
                )
            }
            .disabled(!showingPreview && selectedTheme == originalTheme)
            .opacity((!showingPreview && selectedTheme == originalTheme) ? 0.6 : 1.0)
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .accessibilityLabel("Apply selected background theme")
            .accessibilityHint(accessibilityService.buttonHint("apply the selected background theme"))
        }
    }
    
    private var gridColumns: [GridItem] {
        [
            GridItem(.flexible(), spacing: 12),
            GridItem(.flexible(), spacing: 12),
            GridItem(.flexible(), spacing: 12)
        ]
    }

    private func themeSection(title: String, themes: [BackgroundTheme]) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(title)
                .font(accessibilityService.scaledFont(.title3))
                .fontWeight(accessibilityService.fontWeight(.semibold))
                .foregroundColor(appTheme.textPrimary)
                .frame(maxWidth: .infinity, alignment: .leading)

            LazyVGrid(columns: gridColumns, spacing: 16) {
                ForEach(themes) { theme in
                    ThemePreviewCard(
                        theme: theme,
                        isSelected: selectedTheme == theme,
                        onSelect: {
                            selectTheme(theme)
                        }
                    )
                }
            }
        }
    }
    
    // MARK: - Actions
    
    private func selectTheme(_ theme: BackgroundTheme) {
        withAnimation(.easeInOut(duration: 0.2)) {
            selectedTheme = theme

            // If preview is active, immediately apply the new selection
            if showingPreview {
                appTheme.setBackground(theme)
            }
        }

        // Provide haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        LoggingService.shared.info("Theme selected: \(theme.displayName)", category: .ui)
    }
    
    private func togglePreview() {
        withAnimation(.easeInOut(duration: 0.3)) {
            if showingPreview {
                // Stop preview - restore original theme
                appTheme.setBackground(originalTheme)
                showingPreview = false
            } else {
                // Start preview - apply selected theme temporarily
                appTheme.setBackground(selectedTheme)
                showingPreview = true
            }
        }

        // Provide haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        LoggingService.shared.info("Theme preview \(showingPreview ? "started" : "stopped"): \(selectedTheme.displayName)", category: .ui)
    }

    private func applySelectedTheme() {
        withAnimation(.easeInOut(duration: 0.3)) {
            appTheme.setBackground(selectedTheme)
            originalTheme = selectedTheme // Update original theme to the new selection
            showingPreview = false // Reset preview state
        }

        // Provide success haptic feedback
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)

        // Dismiss after a brief delay to show the change
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            dismiss()
        }
    }
}

// MARK: - Theme Preview Card

struct ThemePreviewCard: View {
    
    // MARK: - Properties
    
    let theme: BackgroundTheme
    let isSelected: Bool
    let onSelect: () -> Void
    
    @EnvironmentObject private var appTheme: ThemeManager
    @StateObject private var accessibilityService = AccessibilityService.shared
    
    // MARK: - Body
    
    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 12) {
                // Preview Circle
                ZStack {
                    if theme.isGradient, let gradient = theme.gradient {
                        Circle()
                            .fill(gradient)
                            .frame(width: 70, height: 70)
                            .overlay(
                                Circle()
                                    .stroke(
                                        isSelected ? appTheme.accentColor : appTheme.borderColor,
                                        lineWidth: isSelected ? 3 : 1
                                    )
                            )
                    } else {
                        Circle()
                            .fill(theme.previewColor)
                            .frame(width: 70, height: 70)
                            .overlay(
                                Circle()
                                    .stroke(
                                        isSelected ? appTheme.accentColor : appTheme.borderColor,
                                        lineWidth: isSelected ? 3 : 1
                                    )
                            )
                    }

                    Image(systemName: theme.iconName)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.5), radius: 1, x: 0, y: 1)
                }
                .scaleEffect(isSelected ? 1.1 : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
                
                // Theme Name
                Text(theme.displayName)
                    .font(accessibilityService.scaledFont(.caption))
                    .fontWeight(accessibilityService.fontWeight(isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? appTheme.accentColor : appTheme.textPrimary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
                    .animation(.easeInOut(duration: 0.2), value: isSelected)

                // Selection Indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 12))
                        .foregroundColor(appTheme.accentColor)
                        .transition(.scale.combined(with: .opacity))
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 120)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(appTheme.surfaceColor.opacity(0.5))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                isSelected ? appTheme.accentColor : appTheme.borderColor,
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("\(theme.displayName) background theme")
        .accessibilityHint(accessibilityService.buttonHint("select this background theme"))
        .accessibilityAddTraits(isSelected ? [.isSelected] : [])
    }
}

// MARK: - Preview

#Preview {
    BackgroundThemeSelector()
        .withAppTheme()
}
