//
//  EchoNoteApp.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData

@main
struct EchoNoteApp: App {

    init() {
        // Initialize crash reporting as early as possible
        CrashReportingService.shared.setup()

        // Initialize logging service
        LoggingService.shared.info("EchoNote app starting up", category: .system)

        // Start performance monitoring
        Task { @MainActor in
            PerformanceMonitoringService.shared.startMonitoring()
            PerformanceMonitoringService.shared.trackEvent(.appLaunch)

            // Start memory management
            MemoryManagementService.shared.startPeriodicMemoryOptimization()
        }
    }
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            Note.self,
            Keyword.self,
            UserPreferences.self,
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            // If migration fails, try to delete the existing store and create a new one
            print("Initial ModelContainer creation failed: \(error)")

            do {
                // Get the default store URL
                let storeURL = modelConfiguration.url

                // Remove the existing store files
                let fileManager = FileManager.default
                if fileManager.fileExists(atPath: storeURL.path) {
                    try fileManager.removeItem(at: storeURL)
                    print("Removed existing store at: \(storeURL)")
                }

                // Also remove related files (WAL, SHM)
                let walURL = storeURL.appendingPathExtension("wal")
                let shmURL = storeURL.appendingPathExtension("shm")

                if fileManager.fileExists(atPath: walURL.path) {
                    try fileManager.removeItem(at: walURL)
                }
                if fileManager.fileExists(atPath: shmURL.path) {
                    try fileManager.removeItem(at: shmURL)
                }

                // Try creating the container again
                return try ModelContainer(for: schema, configurations: [modelConfiguration])
            } catch {
                print("Failed to recover from migration error: \(error)")
                fatalError("Could not create ModelContainer after cleanup: \(error)")
            }
        }
    }()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .withAppTheme()
                .withGlobalBackground()
                .onContinueUserActivity("com.clevorie.EchoNote.startRecording") { userActivity in
                    _ = SiriShortcutsService.shared.handleUserActivity(userActivity)
                }
                .onContinueUserActivity("com.clevorie.EchoNote.quickNote") { userActivity in
                    _ = SiriShortcutsService.shared.handleUserActivity(userActivity)
                }
                .onContinueUserActivity("com.clevorie.EchoNote.playLastRecording") { userActivity in
                    _ = SiriShortcutsService.shared.handleUserActivity(userActivity)
                }
                .onContinueUserActivity("com.clevorie.EchoNote.searchNotes") { userActivity in
                    _ = SiriShortcutsService.shared.handleUserActivity(userActivity)
                }
                .onReceive(NotificationCenter.default.publisher(for: .siriCreateNoteWithKeyword)) { notification in
                    handleCreateNoteWithKeyword(notification)
                }
                .onReceive(NotificationCenter.default.publisher(for: .siriStartRecordingWithKeyword)) { notification in
                    handleStartRecordingWithKeyword(notification)
                }
        }
        .modelContainer(sharedModelContainer)
    }

    // MARK: - Siri Intent Handlers

    private func handleCreateNoteWithKeyword(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let keyword = userInfo["keyword"] as? String,
              let content = userInfo["content"] as? String else {
            print("❌ Invalid create note notification")
            return
        }

        print("🎯 Creating note via Siri: keyword='\(keyword)', content='\(content)'")

        // Create note using DataManager
        Task { @MainActor in
            let context = sharedModelContainer.mainContext
            let dataManager = DataManager(modelContext: context)

            // Find or create keyword
            let keywordObject = dataManager.findOrCreateKeyword(text: keyword)

            // Generate title
            let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .short)
            let title = "\(keyword) - \(timestamp)"

            // Create note
            let note = dataManager.createNote(
                title: title,
                content: content,
                keywords: [keywordObject]
            )

            print("✅ Created note via Siri: '\(note.title)'")

            // Donate the intent for future suggestions
            SiriShortcutsService.shared.donateStartRecordingIntent(with: keyword)
        }
    }

    private func handleStartRecordingWithKeyword(_ notification: Notification) {
        let keyword = notification.userInfo?["keyword"] as? String

        print("🎯 Starting recording via Siri with keyword: '\(keyword ?? "none")'")

        // Start background monitoring
        let backgroundMonitor = BackgroundMonitorService.shared
        if !backgroundMonitor.isMonitoring {
            backgroundMonitor.startMonitoring()
        }

        // If keyword provided, set it as active
        if let keyword = keyword {
            // Post notification to set active keyword
            NotificationCenter.default.post(
                name: .siriStartRecording,
                object: nil,
                userInfo: ["keyword": keyword]
            )
        }

        // Donate the intent for future suggestions
        SiriShortcutsService.shared.donateStartRecordingIntent(with: keyword)
    }
}
