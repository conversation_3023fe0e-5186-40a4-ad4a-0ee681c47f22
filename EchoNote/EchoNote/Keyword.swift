//
//  Keyword.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import SwiftData

@Model
final class Keyword {
    var id: UUID
    var text: String
    var createdAt: Date
    var usageCount: Int
    var color: String // Hex color string
    var icon: String // SF Symbol name
    var isEnabled: Bo<PERSON>

    @Relationship(deleteRule: .nullify)
    var notes: [Note]

    init(text: String, color: String = "#34C759", icon: String = "lightbulb.fill") {
        self.id = UUID()
        self.text = text
        self.createdAt = Date()
        self.usageCount = 0
        self.color = color
        self.icon = icon
        self.isEnabled = true
        self.notes = []
    }
    
    func incrementUsage() {
        self.usageCount += 1
    }
    
    func toggle() {
        self.isEnabled.toggle()
    }
}
