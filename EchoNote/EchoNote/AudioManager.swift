//
//  AudioManager.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import AVFoundation
import Speech
import SwiftUI

// MARK: - Speech Recognition Errors

enum SpeechRecognitionError: Error {
    case recognizerUnavailable
    case requestCreationFailed
    case audioEngineStartFailed(Error)
    case recognitionFailed(Error)
    case permissionDenied
    case networkUnavailable

    var localizedDescription: String {
        switch self {
        case .recognizerUnavailable:
            return "Speech recognizer is not available"
        case .requestCreationFailed:
            return "Failed to create recognition request"
        case .audioEngineStartFailed(let error):
            return "Audio engine failed to start: \(error.localizedDescription)"
        case .recognitionFailed(let error):
            return "Speech recognition failed: \(error.localizedDescription)"
        case .permissionDenied:
            return "Speech recognition permission denied"
        case .networkUnavailable:
            return "Network unavailable for speech recognition"
        }
    }
}

@MainActor
class AudioManager: NSObject, ObservableObject {
    private var audioEngine = AVAudioEngine()
    private var speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "en-US"))
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioRecorder: AVAudioRecorder?
    private var audioPlayer: AVAudioPlayer?

    // State
    @Published var isRecording = false
    @Published var isRecordingPaused = false
    @Published var isListening = false
    @Published var isPlaying = false
    @Published var transcribedText = ""
    @Published var detectedKeyword = ""
    @Published var recordingDuration: TimeInterval = 0
    @Published var playbackDuration: TimeInterval = 0
    @Published var playbackCurrentTime: TimeInterval = 0
    @Published var audioLevels: [Float] = []
    @Published var currentAudioURL: URL?

    // Audio metering
    @Published var recordingAudioLevel: Float = 0.0
    @Published var playbackAudioLevel: Float = 0.0
    
    // Permissions service
    private let permissionsService = PermissionsService()

    // Secure data manager
    private let secureDataManager: SecureDataManager?

    // Legacy permission properties for backward compatibility
    var hasMicrophonePermission: Bool {
        return permissionsService.microphoneStatus.isGranted
    }

    var hasSpeechRecognitionPermission: Bool {
        return permissionsService.speechRecognitionStatus.isGranted
    }

    // Speech recognition settings
    var recognitionConfidenceThreshold: Float = 0.1 // Very low threshold for maximum sensitivity
    var useOnDeviceRecognition = true
    var recognitionLanguage = "en-US"

    // Simplified keyword storage
    private var registeredKeywords: [String] = []

    // Keywords property
    var keywords: [String] {
        return registeredKeywords
    }
    
    private var recordingTimer: Timer?
    private var levelTimer: Timer?
    
    override init() {
        // Initialize secure data manager
        do {
            self.secureDataManager = try SecureDataManager()
        } catch {
            self.secureDataManager = nil
        }

        super.init()
        setupAudio()
        configureSpeechRecognizer()
    }

    private func configureSpeechRecognizer() {
        // Configure speech recognizer with locale
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: recognitionLanguage))

        guard let speechRecognizer = speechRecognizer else {
            return
        }

        // Enable on-device recognition if available and requested
        if useOnDeviceRecognition && speechRecognizer.supportsOnDeviceRecognition {
            speechRecognizer.defaultTaskHint = .dictation
        }

        // Set delegate for monitoring availability
        speechRecognizer.delegate = self
    }
    
    // MARK: - Setup
    
    private func setupAudio() {
        requestPermissions()
        setupAudioSession()
    }
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)

            // Set preferred sample rate and buffer duration for better performance
            try audioSession.setPreferredSampleRate(44100.0)
            try audioSession.setPreferredIOBufferDuration(0.005)

        } catch {
            handleAudioError(error)
        }
    }

    private func ensureAudioSessionActive() {
        let audioSession = AVAudioSession.sharedInstance()

        do {
            // Force deactivate first, then reactivate
            try audioSession.setActive(false, options: .notifyOthersOnDeactivation)

            // Wait a moment
            Thread.sleep(forTimeInterval: 0.1)

            // Reactivate audio session for speech recognition
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true, options: [])

        } catch {
            // Try alternative approach
            do {
                try audioSession.setCategory(.record, mode: .measurement, options: [])
                try audioSession.setActive(true, options: [])
            } catch {
                handleAudioError(error)
            }
        }
    }

    func configureAudioSessionForRecording() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.record, mode: .measurement, options: [])
            try audioSession.setActive(true)
        } catch {
            handleAudioError(error)
        }
    }

    func configureAudioSessionForPlayback() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .default, options: [])
            try audioSession.setActive(true)
        } catch {
            handleAudioError(error)
        }
    }

    func configureAudioSessionForPlayAndRecord() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            handleAudioError(error)
        }
    }
    
    private func requestPermissions() {
        // Use the new permissions service for initial check
        Task {
            await permissionsService.checkAllPermissions()
        }
    }

    func requestPermissionsAsync() async {
        // Public async method for requesting permissions
        await permissionsService.checkAllPermissions()
        let _ = await permissionsService.requestAllRequiredPermissions()
    }

    func getPermissionsService() -> PermissionsService {
        return permissionsService
    }
    


    // MARK: - Keyword Detection
    
    func startListening() {
        // Request permissions if not granted
        if !hasMicrophonePermission || !hasSpeechRecognitionPermission {
            Task {
                await requestPermissionsAsync()

                // Try again after requesting permissions
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self.startListeningAfterPermissions()
                }
            }
            return
        }

        // Ensure audio session is active before starting
        ensureAudioSessionActive()
        startListeningAfterPermissions()
    }

    private func startListeningAfterPermissions() {
        guard hasMicrophonePermission && hasSpeechRecognitionPermission else {
            return
        }

        // Double-check audio session is active
        ensureAudioSessionActive()

        guard !isListening else {
            return
        }

        isListening = true
        detectedKeyword = ""
        transcribedText = ""

        startSpeechRecognition()
    }
    
    func stopListening() {
        isListening = false
        stopSpeechRecognition()
    }
    
    private func startSpeechRecognition() {
        // Cancel any previous task
        recognitionTask?.cancel()
        recognitionTask = nil

        // Check if speech recognizer is available
        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            handleSpeechRecognitionError(SpeechRecognitionError.recognizerUnavailable)
            return
        }

        // Create recognition request
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            handleSpeechRecognitionError(SpeechRecognitionError.requestCreationFailed)
            return
        }

        // Configure recognition request
        recognitionRequest.shouldReportPartialResults = true
        recognitionRequest.taskHint = .dictation

        // Enable on-device recognition if available and requested
        if useOnDeviceRecognition && speechRecognizer.supportsOnDeviceRecognition {
            recognitionRequest.requiresOnDeviceRecognition = true
        }
        
        // Check if audio engine is already running
        if audioEngine.isRunning {
            audioEngine.stop()
            audioEngine.inputNode.removeTap(onBus: 0)
        }

        // Start audio engine
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)

        // Remove any existing tap
        inputNode.removeTap(onBus: 0)

        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }

        audioEngine.prepare()

        do {
            try audioEngine.start()
        } catch {
            handleSpeechRecognitionError(SpeechRecognitionError.audioEngineStartFailed(error))
            return
        }

        // Start recognition task
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            guard let self = self else { return }
            
            if let result = result {
                let transcription = result.bestTranscription.formattedString.lowercased()
                
                // Get confidence score
                let confidence = result.bestTranscription.segments.first?.confidence ?? 0.0

                // Process speech with confidence threshold
                if confidence >= self.recognitionConfidenceThreshold {
                    DispatchQueue.main.async {
                        self.transcribedText = transcription

                        // Only detect keywords if we're in listening mode (not recording)
                        if self.isListening && !self.isRecording {
                            // Simple keyword detection
                            let lowercaseText = transcription.lowercased()
                            for keyword in self.registeredKeywords {
                                if lowercaseText.contains(keyword.lowercased()) {
                                    self.detectedKeyword = keyword

                                    // Stop listening
                                    self.stopListening()

                                    // Small delay to ensure state change is processed
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                        self.startRecording()
                                    }
                                    break // Only detect first keyword
                                }
                            }
                        }
                    }
                }
            }

            if let error = error {
                // Check if this is a "No speech detected" error and we're already recording
                let nsError = error as NSError
                if nsError.domain == "kAFAssistantErrorDomain" && nsError.code == 1110 && self.isRecording {
                    return
                }

                self.handleSpeechRecognitionError(SpeechRecognitionError.recognitionFailed(error))
            }
        }
    }
    
    private func stopSpeechRecognition() {
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        recognitionRequest?.endAudio()
        recognitionRequest = nil
        recognitionTask?.cancel()
        recognitionTask = nil
    }

    // MARK: - Speech Recognition Utilities

    func updateRecognitionLanguage(_ languageCode: String) {
        recognitionLanguage = languageCode
        configureSpeechRecognizer()
    }

    func updateConfidenceThreshold(_ threshold: Float) {
        recognitionConfidenceThreshold = max(0.0, min(1.0, threshold))
    }

    func toggleOnDeviceRecognition(_ enabled: Bool) {
        useOnDeviceRecognition = enabled
        configureSpeechRecognizer()
    }

    func isSpeechRecognitionAvailable() -> Bool {
        return speechRecognizer?.isAvailable == true
    }

    func supportsOnDeviceRecognition() -> Bool {
        return speechRecognizer?.supportsOnDeviceRecognition == true
    }

    // MARK: - Keyword Management

    func updateKeywords(from databaseKeywords: [Keyword]) {
        // Update keywords from database
        registeredKeywords = databaseKeywords
            .filter { $0.isEnabled }
            .map { $0.text }

        print("AudioManager: Updated keywords: \(registeredKeywords)")
    }

    // MARK: - Diagnostics



    func addKeyword(_ text: String) {
        if !registeredKeywords.contains(text) {
            registeredKeywords.append(text)
        }
    }

    func removeKeyword(_ text: String) {
        registeredKeywords.removeAll { $0 == text }
    }

    func getRegisteredKeywords() -> [String] {
        return registeredKeywords
    }

    // MARK: - Secure Audio File Management

    /// Securely save audio file with encryption
    func secureAudioFile(at url: URL) -> URL? {
        guard let secureManager = secureDataManager else {
            return url // Return original URL as fallback
        }

        do {
            let secureURL = secureManager.createSecureTempFileURL(withExtension: "m4a")
            try secureManager.secureAudioFile(at: url, to: secureURL)
            return secureURL
        } catch {
            return url // Return original URL as fallback
        }
    }

    /// Get decrypted audio data for playback
    func getDecryptedAudioData(from url: URL) -> Data? {
        guard let secureManager = secureDataManager else {
            // Fallback to regular file reading
            return try? Data(contentsOf: url)
        }

        do {
            return try secureManager.decryptAudioFile(at: url)
        } catch {
            // Fallback to regular file reading
            return try? Data(contentsOf: url)
        }
    }

    /// Clean up temporary secure files
    func cleanupSecureFiles() {
        secureDataManager?.cleanupTempFiles()
    }
    
    // MARK: - Recording
    
    func startRecording() {
        PerformanceMonitoringService.shared.measurePerformance(of: {
            guard hasMicrophonePermission else { return }
            guard !isRecording else { return }

            // Stop listening for keywords when we start recording
            if isListening {
                stopListening()
            }

            isRecording = true
            recordingDuration = 0
            // Don't clear transcribedText - we'll accumulate recording content
            // Don't clear detectedKeyword - we need it for tagging the note
            audioLevels = []

            setupRecorder()
            startRecordingTimer()
            startLevelMonitoring()
            startTranscription()
        }, eventType: .audioRecordingStart)
    }
    
    func pauseRecording() {
        guard isRecording && !isRecordingPaused else { return }

        isRecordingPaused = true
        audioRecorder?.pause()
        recordingTimer?.invalidate()
        stopLevelMonitoring()

        // Pause speech recognition
        stopSpeechRecognition()
    }

    func resumeRecording() {
        guard isRecording && isRecordingPaused else { return }
        guard let recorder = audioRecorder else { return }

        isRecordingPaused = false
        recorder.record()
        startRecordingTimer()
        startLevelMonitoring()
        startTranscription()
    }

    func stopRecording() -> URL? {
        return PerformanceMonitoringService.shared.measurePerformance(of: {
            guard isRecording else { return nil }

            isRecording = false
            isRecordingPaused = false
            recordingTimer?.invalidate()
            stopLevelMonitoring()

            audioRecorder?.stop()
            stopSpeechRecognition()

            return currentAudioURL
        }, eventType: .audioRecordingStop)
    }
    
    private func setupRecorder() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioFilename = documentsPath.appendingPathComponent("recording_\(Date().timeIntervalSince1970).m4a")
        currentAudioURL = audioFilename
        
        // Optimized settings for better performance
        let settings = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 44100, // Standard sample rate
            AVNumberOfChannelsKey: 1, // Mono for efficiency
            AVEncoderAudioQualityKey: AVAudioQuality.medium.rawValue, // Balanced quality/performance
            AVEncoderBitRateKey: 64000 // Optimized bit rate for voice
        ]
        
        do {
            audioRecorder = try AVAudioRecorder(url: audioFilename, settings: settings)
            audioRecorder?.isMeteringEnabled = true
            audioRecorder?.record()
        } catch {
            // Handle recorder setup error silently
        }
    }
    
    private func startRecordingTimer() {
        // Optimized timer with lower frequency for better performance
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 0.2, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            Task { @MainActor in
                self.recordingDuration += 0.2
            }
        }
    }
    
    private func startLevelMonitoring() {
        levelTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            guard let self = self, let recorder = self.audioRecorder else { return }
            recorder.updateMeters()
            let level = recorder.averagePower(forChannel: 0)
            let normalizedLevel = max(0, (level + 80) / 80) // Normalize -80 to 0 dB to 0-1

            // Update current recording level on main actor
            Task { @MainActor in
                self.recordingAudioLevel = normalizedLevel

                // Add to levels array for visualization
                self.audioLevels.append(normalizedLevel)

                // Keep only last 50 levels for visualization
                if self.audioLevels.count > 50 {
                    self.audioLevels.removeFirst()
                }
            }
        }
    }

    private func stopLevelMonitoring() {
        levelTimer?.invalidate()
        levelTimer = nil
        recordingAudioLevel = 0.0
    }
    
    private func startTranscription() {
        // Start speech recognition for transcription during recording
        startSpeechRecognition()
        
        // Update recognition task to capture transcription
        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest!) { [weak self] result, error in
            guard let self = self else { return }
            
            if let result = result {
                DispatchQueue.main.async {
                    self.transcribedText = result.bestTranscription.formattedString
                }
            }
        }
    }
    
    // MARK: - Playback

    func playRecording(url: URL) {
        PerformanceMonitoringService.shared.measurePerformance(of: {
            do {
                // Stop any current playback
                stopPlayback()

                // Check cache first for better memory management
                let cacheKey = url.absoluteString
                if let cachedPlayer = MemoryManagementService.shared.getCachedAudioPlayer(forKey: cacheKey) {
                    audioPlayer = cachedPlayer
                } else {
                    // Create audio player with optimized settings
                    audioPlayer = try AVAudioPlayer(contentsOf: url)

                    // Cache the player for reuse
                    if let player = audioPlayer {
                        MemoryManagementService.shared.cacheAudioPlayer(player, forKey: cacheKey)
                    }
                }

                audioPlayer?.delegate = self
                audioPlayer?.isMeteringEnabled = true

                // Prepare to play asynchronously for better performance
                audioPlayer?.prepareToPlay()

                // Update state
                isPlaying = true
                playbackDuration = audioPlayer?.duration ?? 0
                playbackCurrentTime = 0

                // Start playback
                audioPlayer?.play()

                // Start metering timer with optimized frequency
                startPlaybackMetering()

            } catch {
                handleAudioError(error)
            }
        }, eventType: .audioPlaybackStart)
    }

    func pausePlayback() {
        audioPlayer?.pause()
        isPlaying = false
        stopPlaybackMetering()
    }

    func resumePlayback() {
        audioPlayer?.play()
        isPlaying = true
        startPlaybackMetering()
    }

    func stopPlayback() {
        audioPlayer?.stop()
        audioPlayer = nil
        isPlaying = false
        playbackCurrentTime = 0
        playbackAudioLevel = 0.0
        stopPlaybackMetering()
    }

    func seekToTime(_ time: TimeInterval) {
        guard let player = audioPlayer else { return }
        player.currentTime = min(max(time, 0), player.duration)
        playbackCurrentTime = player.currentTime
    }

    private func startPlaybackMetering() {
        levelTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updatePlaybackMetering()
            }
        }
    }

    private func stopPlaybackMetering() {
        levelTimer?.invalidate()
        levelTimer = nil
        playbackAudioLevel = 0.0
    }

    private func updatePlaybackMetering() {
        guard let player = audioPlayer, player.isPlaying else { return }

        player.updateMeters()
        playbackCurrentTime = player.currentTime

        // Get average power level
        let averagePower = player.averagePower(forChannel: 0)
        // Convert to linear scale (0.0 to 1.0)
        playbackAudioLevel = pow(10.0, averagePower / 20.0)
    }
    
    // MARK: - Error Handling

    private func handleAudioError(_ error: Error) {
        DispatchQueue.main.async {
            // Reset states on error
            self.isRecording = false
            self.isListening = false
            self.isPlaying = false

            // Handle audio error silently
        }
    }

    private func handleSpeechRecognitionError(_ error: SpeechRecognitionError) {
        DispatchQueue.main.async {
            // Don't reset state if we're already recording
            // This prevents "No speech detected" errors from interfering with successful keyword detection
            if self.isRecording {
                self.stopSpeechRecognition()
                return
            }

            // Check if we have a detected keyword that triggered recording
            let hasDetectedKeyword = !self.detectedKeyword.isEmpty

            // Only reset state if we're not recording
            self.isListening = false

            // Don't clear detectedKeyword if we have one - we need it for tagging the note
            if !hasDetectedKeyword {
                self.transcribedText = ""
                self.detectedKeyword = ""
            }

            // Stop speech recognition
            self.stopSpeechRecognition()
        }
    }

    // MARK: - Utility

    func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    func getRecordingURL() -> URL {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioFilename = "recording_\(Date().timeIntervalSince1970).m4a"
        return documentsPath.appendingPathComponent(audioFilename)
    }

    deinit {
        // Clean up timers (these are not MainActor isolated)
        levelTimer?.invalidate()
        recordingTimer?.invalidate()

        // Clean up audio session and players directly
        audioRecorder?.stop()
        audioPlayer?.stop()

        // Reset audio session
        try? AVAudioSession.sharedInstance().setActive(false)
    }
}

// MARK: - AVAudioPlayerDelegate

extension AudioManager: AVAudioPlayerDelegate {
    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        DispatchQueue.main.async {
            self.isPlaying = false
            self.playbackCurrentTime = 0
            self.playbackAudioLevel = 0.0
            self.stopPlaybackMetering()
        }
    }

    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        if let error = error {
            Task { @MainActor in
                handleAudioError(error)
            }
        }
    }
}

// MARK: - SFSpeechRecognizerDelegate

extension AudioManager: SFSpeechRecognizerDelegate {
    nonisolated func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        DispatchQueue.main.async {
            if !available {
                if self.isListening {
                    self.stopListening()
                }
            }
        }
    }
}
