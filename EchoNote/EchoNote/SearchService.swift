//
//  SearchService.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import SwiftData

// MARK: - Search Result

struct SearchResult: Identifiable {
    let id = UUID()
    let note: Note
    let relevanceScore: Double
    let matchedFields: [MatchedField]
    let highlightedTitle: String
    let highlightedContent: String
    let highlightedKeywords: [String]
}

struct MatchedField {
    let field: SearchField
    let matches: [TextMatch]
}

struct TextMatch {
    let range: NSRange
    let matchedText: String
}

enum SearchField {
    case title
    case content
    case keywords
}

// MARK: - Search Filter

struct SearchFilter {
    var dateRange: DateRange?
    var keywords: [Keyword]
    var isFavoriteOnly: Bool
    var hasAudioOnly: Bool
    var sortBy: SearchSortOption
    
    init() {
        self.dateRange = nil
        self.keywords = []
        self.isFavoriteOnly = false
        self.hasAudioOnly = false
        self.sortBy = .relevance
    }
}

struct DateRange {
    let startDate: Date
    let endDate: Date
}

enum SearchSortOption: String, CaseIterable {
    case relevance = "Relevance"
    case dateCreated = "Date Created"
    case dateModified = "Date Modified"
    case title = "Title"
}

// MARK: - Search Service

@MainActor
class SearchService: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var searchResults: [SearchResult] = []
    @Published var isSearching = false
    @Published var recentSearches: [String] = []
    @Published var searchSuggestions: [String] = []
    
    // MARK: - Properties
    
    private let modelContext: ModelContext
    private let maxRecentSearches = 10
    private let maxSuggestions = 5
    
    // MARK: - Initialization
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
        loadRecentSearches()
    }
    
    // MARK: - Search Methods
    
    /// Perform a search with the given query and filters
    func search(query: String, filter: SearchFilter = SearchFilter()) async {
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            searchResults = []
            return
        }
        
        isSearching = true
        
        let results = await PerformanceMonitoringService.shared.measureAsyncPerformance(of: {
            await performSearch(query: query, filter: filter)
        }, eventType: .dataLoad, metadata: ["operation": "search", "query": query])
        
        searchResults = results
        isSearching = false
        
        // Add to recent searches
        addToRecentSearches(query)
        
        LoggingService.shared.info("Search completed", category: .system, metadata: [
            "query": query,
            "resultCount": String(results.count)
        ])
    }
    
    private func performSearch(query: String, filter: SearchFilter) async -> [SearchResult] {
        do {
            // Get all notes
            let descriptor = FetchDescriptor<Note>()
            let allNotes = try modelContext.fetch(descriptor)
            
            // Filter notes based on search criteria
            let filteredNotes = filterNotes(allNotes, with: filter)
            
            // Search and score results
            let searchResults = await searchAndScore(notes: filteredNotes, query: query)
            
            // Sort results
            let sortedResults = sortResults(searchResults, by: filter.sortBy)
            
            return sortedResults
            
        } catch {
            LoggingService.shared.error("Search failed: \(error)", category: .system)
            ErrorHandlingService.shared.handle(StorageError.loadFailed(underlying: error), context: "Searching notes")
            return []
        }
    }
    
    private func filterNotes(_ notes: [Note], with filter: SearchFilter) -> [Note] {
        var filtered = notes
        
        // Date range filter
        if let dateRange = filter.dateRange {
            filtered = filtered.filter { note in
                note.createdAt >= dateRange.startDate && note.createdAt <= dateRange.endDate
            }
        }
        
        // Keyword filter
        if !filter.keywords.isEmpty {
            filtered = filtered.filter { note in
                filter.keywords.allSatisfy { filterKeyword in
                    note.keywords.contains { noteKeyword in
                        noteKeyword.id == filterKeyword.id
                    }
                }
            }
        }
        
        // Favorite filter
        if filter.isFavoriteOnly {
            filtered = filtered.filter { $0.isFavorite }
        }
        
        // Audio filter
        if filter.hasAudioOnly {
            filtered = filtered.filter { $0.audioURL != nil && !$0.audioURL!.isEmpty }
        }
        
        return filtered
    }
    
    private func searchAndScore(notes: [Note], query: String) async -> [SearchResult] {
        let searchTerms = query.lowercased().components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }
        
        var results: [SearchResult] = []
        
        for note in notes {
            let (score, matchedFields) = calculateRelevanceScore(for: note, searchTerms: searchTerms)
            
            if score > 0 {
                let highlightedTitle = highlightText(note.title, searchTerms: searchTerms)
                let highlightedContent = highlightText(note.content, searchTerms: searchTerms)
                let highlightedKeywords = note.keywords.map { keyword in
                    highlightText(keyword.text, searchTerms: searchTerms)
                }
                
                let result = SearchResult(
                    note: note,
                    relevanceScore: score,
                    matchedFields: matchedFields,
                    highlightedTitle: highlightedTitle,
                    highlightedContent: highlightedContent,
                    highlightedKeywords: highlightedKeywords
                )
                
                results.append(result)
            }
        }
        
        return results
    }
    
    private func calculateRelevanceScore(for note: Note, searchTerms: [String]) -> (Double, [MatchedField]) {
        var totalScore: Double = 0
        var matchedFields: [MatchedField] = []
        
        // Title matches (highest weight)
        let titleMatches = findMatches(in: note.title, searchTerms: searchTerms)
        if !titleMatches.isEmpty {
            totalScore += Double(titleMatches.count) * 3.0
            matchedFields.append(MatchedField(field: .title, matches: titleMatches))
        }
        
        // Content matches (medium weight)
        let contentMatches = findMatches(in: note.content, searchTerms: searchTerms)
        if !contentMatches.isEmpty {
            totalScore += Double(contentMatches.count) * 1.0
            matchedFields.append(MatchedField(field: .content, matches: contentMatches))
        }
        
        // Keyword matches (high weight)
        let keywordText = note.keywords.map { $0.text }.joined(separator: " ")
        let keywordMatches = findMatches(in: keywordText, searchTerms: searchTerms)
        if !keywordMatches.isEmpty {
            totalScore += Double(keywordMatches.count) * 2.0
            matchedFields.append(MatchedField(field: .keywords, matches: keywordMatches))
        }
        
        // Boost score for exact phrase matches
        let fullQuery = searchTerms.joined(separator: " ")
        if note.title.lowercased().contains(fullQuery) {
            totalScore += 5.0
        }
        if note.content.lowercased().contains(fullQuery) {
            totalScore += 3.0
        }
        
        // Boost score for recent notes
        let daysSinceCreation = Date().timeIntervalSince(note.createdAt) / (24 * 60 * 60)
        if daysSinceCreation < 7 {
            totalScore *= 1.2
        }
        
        // Boost score for favorite notes
        if note.isFavorite {
            totalScore *= 1.1
        }
        
        return (totalScore, matchedFields)
    }
    
    private func findMatches(in text: String, searchTerms: [String]) -> [TextMatch] {
        var matches: [TextMatch] = []
        let lowercaseText = text.lowercased()
        
        for term in searchTerms {
            let ranges = lowercaseText.ranges(of: term)
            for range in ranges {
                let nsRange = NSRange(range, in: text)
                let matchedText = String(text[range])
                matches.append(TextMatch(range: nsRange, matchedText: matchedText))
            }
        }
        
        return matches
    }
    
    private func highlightText(_ text: String, searchTerms: [String]) -> String {
        var highlightedText = text
        
        for term in searchTerms {
            let pattern = "(?i)\(NSRegularExpression.escapedPattern(for: term))"
            highlightedText = highlightedText.replacingOccurrences(
                of: pattern,
                with: "**$0**",
                options: .regularExpression
            )
        }
        
        return highlightedText
    }
    
    private func sortResults(_ results: [SearchResult], by sortOption: SearchSortOption) -> [SearchResult] {
        switch sortOption {
        case .relevance:
            return results.sorted { $0.relevanceScore > $1.relevanceScore }
        case .dateCreated:
            return results.sorted { $0.note.createdAt > $1.note.createdAt }
        case .dateModified:
            return results.sorted { $0.note.updatedAt > $1.note.updatedAt }
        case .title:
            return results.sorted { $0.note.title.localizedCaseInsensitiveCompare($1.note.title) == .orderedAscending }
        }
    }
    
    // MARK: - Recent Searches
    
    private func addToRecentSearches(_ query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty else { return }
        
        // Remove if already exists
        recentSearches.removeAll { $0.lowercased() == trimmedQuery.lowercased() }
        
        // Add to beginning
        recentSearches.insert(trimmedQuery, at: 0)
        
        // Limit size
        if recentSearches.count > maxRecentSearches {
            recentSearches = Array(recentSearches.prefix(maxRecentSearches))
        }
        
        saveRecentSearches()
    }
    
    func clearRecentSearches() {
        recentSearches.removeAll()
        saveRecentSearches()
    }
    
    private func saveRecentSearches() {
        UserDefaults.standard.set(recentSearches, forKey: "recentSearches")
    }
    
    private func loadRecentSearches() {
        recentSearches = UserDefaults.standard.stringArray(forKey: "recentSearches") ?? []
    }
    
    // MARK: - Search Suggestions
    
    func generateSuggestions(for query: String) async {
        guard !query.isEmpty else {
            searchSuggestions = []
            return
        }
        
        var suggestions: [String] = []
        
        // Add recent searches that match
        let matchingRecent = recentSearches.filter { 
            $0.lowercased().contains(query.lowercased()) 
        }.prefix(3)
        suggestions.append(contentsOf: matchingRecent)
        
        // Add keyword suggestions
        do {
            let descriptor = FetchDescriptor<Keyword>()
            let keywords = try modelContext.fetch(descriptor)
            
            let matchingKeywords = keywords
                .filter { $0.text.lowercased().contains(query.lowercased()) }
                .sorted { $0.usageCount > $1.usageCount }
                .prefix(2)
                .map { $0.text }
            
            suggestions.append(contentsOf: matchingKeywords)
        } catch {
            LoggingService.shared.error("Failed to load keywords for suggestions: \(error)", category: .system)
        }
        
        // Limit suggestions
        searchSuggestions = Array(suggestions.prefix(maxSuggestions))
    }
}

// MARK: - String Extension

extension String {
    func ranges(of searchString: String) -> [Range<String.Index>] {
        var ranges: [Range<String.Index>] = []
        var searchStartIndex = self.startIndex
        
        while searchStartIndex < self.endIndex,
              let range = self.range(of: searchString, options: .caseInsensitive, range: searchStartIndex..<self.endIndex) {
            ranges.append(range)
            searchStartIndex = range.upperBound
        }
        
        return ranges
    }
}
