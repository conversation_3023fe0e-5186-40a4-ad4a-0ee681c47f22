//
//  CrashReportingService.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import UIKit

// MARK: - Crash Report

struct CrashReport: Codable {
    let id = UUID()
    let timestamp: Date
    let appVersion: String
    let osVersion: String
    let deviceModel: String
    let crashType: CrashType
    let stackTrace: String
    let userInfo: [String: String]
    let logs: [String]
    
    enum CrashType: String, Codable {
        case exception = "exception"
        case signal = "signal"
        case manual = "manual"
    }
    
    init(crashType: CrashType, stackTrace: String, userInfo: [String: String] = [:]) {
        self.timestamp = Date()
        self.appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
        self.osVersion = UIDevice.current.systemVersion
        self.deviceModel = UIDevice.current.model
        self.crashType = crashType
        self.stackTrace = stackTrace
        self.userInfo = userInfo
        // Initialize logs as empty for now to avoid actor isolation issues
        self.logs = []
    }
}

// MARK: - Crash Reporting Service

class CrashReportingService {
    
    // MARK: - Properties
    
    static let shared = CrashReportingService()
    private let crashReportsDirectory: URL
    private var isSetup = false
    
    // MARK: - Initialization
    
    private init() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        crashReportsDirectory = documentsPath.appendingPathComponent("CrashReports")
        
        // Create crash reports directory
        try? FileManager.default.createDirectory(at: crashReportsDirectory, withIntermediateDirectories: true)
    }
    
    // MARK: - Setup
    
    func setup() {
        guard !isSetup else { return }
        
        setupExceptionHandler()
        setupSignalHandlers()
        
        isSetup = true
        
        // Process any pending crash reports
        processPendingCrashReports()
    }
    
    // MARK: - Exception Handling
    
    private func setupExceptionHandler() {
        NSSetUncaughtExceptionHandler { exception in
            CrashReportingService.shared.handleException(exception)
        }
    }
    
    private func handleException(_ exception: NSException) {
        let stackTrace = exception.callStackSymbols.joined(separator: "\n")
        let userInfo = [
            "name": exception.name.rawValue,
            "reason": exception.reason ?? "Unknown"
        ]
        
        let crashReport = CrashReport(
            crashType: .exception,
            stackTrace: stackTrace,
            userInfo: userInfo
        )
        
        saveCrashReport(crashReport)
        
        // Log to system (safely)
        DispatchQueue.main.async {
            LoggingService.shared.fault("Uncaught exception: \(exception.name.rawValue) - \(exception.reason ?? "Unknown")", category: .system)
        }
    }
    
    // MARK: - Signal Handling
    
    private func setupSignalHandlers() {
        let signals = [SIGABRT, SIGILL, SIGSEGV, SIGFPE, SIGBUS]
        
        for sig in signals {
            Darwin.signal(sig) { signal in
                CrashReportingService.shared.handleSignal(signal)
            }
        }
    }
    
    private func handleSignal(_ signal: Int32) {
        let stackTrace = Thread.callStackSymbols.joined(separator: "\n")
        let userInfo = [
            "signal": String(signal),
            "signalName": signalName(for: signal)
        ]
        
        let crashReport = CrashReport(
            crashType: .signal,
            stackTrace: stackTrace,
            userInfo: userInfo
        )
        
        saveCrashReport(crashReport)
        
        // Log to system (safely)
        let signalNameString = CrashReportingService.shared.signalName(for: signal)
        DispatchQueue.main.async {
            LoggingService.shared.fault("Signal received: \(signalNameString) (\(signal))", category: .system)
        }

        // Restore default handler and re-raise
        Darwin.signal(signal, SIG_DFL)
        raise(signal)
    }
    
    private func signalName(for signal: Int32) -> String {
        switch signal {
        case SIGABRT: return "SIGABRT"
        case SIGILL: return "SIGILL"
        case SIGSEGV: return "SIGSEGV"
        case SIGFPE: return "SIGFPE"
        case SIGBUS: return "SIGBUS"
        default: return "UNKNOWN"
        }
    }
    
    // MARK: - Manual Crash Reporting
    
    func reportCrash(description: String, userInfo: [String: String] = [:]) {
        let stackTrace = Thread.callStackSymbols.joined(separator: "\n")
        var fullUserInfo = userInfo
        fullUserInfo["description"] = description
        
        let crashReport = CrashReport(
            crashType: .manual,
            stackTrace: stackTrace,
            userInfo: fullUserInfo
        )
        
        saveCrashReport(crashReport)
        
        // Log to system (safely)
        DispatchQueue.main.async {
            LoggingService.shared.error("Manual crash report: \(description)", category: .system, metadata: userInfo)
        }
    }
    
    // MARK: - Crash Report Storage
    
    private func saveCrashReport(_ crashReport: CrashReport) {
        do {
            let data = try JSONEncoder().encode(crashReport)
            let filename = "crash_\(crashReport.id.uuidString).json"
            let fileURL = crashReportsDirectory.appendingPathComponent(filename)
            
            try data.write(to: fileURL)
            
            print("Crash report saved: \(filename)")
        } catch {
            print("Failed to save crash report: \(error)")
        }
    }
    
    // MARK: - Crash Report Processing
    
    func processPendingCrashReports() {
        do {
            let crashFiles = try FileManager.default.contentsOfDirectory(at: crashReportsDirectory, includingPropertiesForKeys: nil)
            
            for file in crashFiles where file.pathExtension == "json" {
                if let data = try? Data(contentsOf: file),
                   let crashReport = try? JSONDecoder().decode(CrashReport.self, from: data) {
                    
                    // Process the crash report (could send to analytics service)
                    processCrashReport(crashReport)
                    
                    // Move to processed folder or delete
                    try? FileManager.default.removeItem(at: file)
                }
            }
        } catch {
            print("Failed to process pending crash reports: \(error)")
        }
    }
    
    private func processCrashReport(_ crashReport: CrashReport) {
        // Log the crash report (safely)
        DispatchQueue.main.async {
            LoggingService.shared.fault("Processing crash report: \(crashReport.crashType.rawValue)", category: .system, metadata: [
                "crashId": crashReport.id.uuidString,
                "timestamp": ISO8601DateFormatter().string(from: crashReport.timestamp),
                "appVersion": crashReport.appVersion,
                "osVersion": crashReport.osVersion
            ])
        }
        
        // Here you could send to analytics service, email, etc.
        // For now, we'll just log it
        print("Processed crash report: \(crashReport.id)")
    }
    
    // MARK: - Crash Report Retrieval
    
    func getAllCrashReports() -> [CrashReport] {
        do {
            let crashFiles = try FileManager.default.contentsOfDirectory(at: crashReportsDirectory, includingPropertiesForKeys: nil)
            
            return crashFiles.compactMap { file in
                guard file.pathExtension == "json",
                      let data = try? Data(contentsOf: file),
                      let crashReport = try? JSONDecoder().decode(CrashReport.self, from: data) else {
                    return nil
                }
                return crashReport
            }
        } catch {
            print("Failed to retrieve crash reports: \(error)")
            return []
        }
    }
    
    func getCrashReportsSummary() -> CrashReportsSummary {
        let reports = getAllCrashReports()
        let totalCrashes = reports.count
        let crashesByType = Dictionary(grouping: reports) { $0.crashType }
        let recentCrashes = reports.sorted { $0.timestamp > $1.timestamp }.prefix(10)
        
        return CrashReportsSummary(
            totalCrashes: totalCrashes,
            crashesByType: crashesByType.mapValues { $0.count },
            recentCrashes: Array(recentCrashes)
        )
    }
    
    // MARK: - Cleanup
    
    func clearOldCrashReports(olderThan days: Int = 30) {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        
        do {
            let crashFiles = try FileManager.default.contentsOfDirectory(at: crashReportsDirectory, includingPropertiesForKeys: [.creationDateKey])
            
            for file in crashFiles {
                if let creationDate = try file.resourceValues(forKeys: [.creationDateKey]).creationDate,
                   creationDate < cutoffDate {
                    try FileManager.default.removeItem(at: file)
                }
            }
        } catch {
            print("Failed to clear old crash reports: \(error)")
        }
    }
    
    func clearAllCrashReports() {
        do {
            let crashFiles = try FileManager.default.contentsOfDirectory(at: crashReportsDirectory, includingPropertiesForKeys: nil)
            
            for file in crashFiles {
                try FileManager.default.removeItem(at: file)
            }
        } catch {
            print("Failed to clear all crash reports: \(error)")
        }
    }
}

// MARK: - Crash Reports Summary

struct CrashReportsSummary {
    let totalCrashes: Int
    let crashesByType: [CrashReport.CrashType: Int]
    let recentCrashes: [CrashReport]
}

// MARK: - Integration with Error Handling

extension ErrorHandlingService {
    
    func reportCriticalError(_ error: EchoNoteError, context: String? = nil) {
        var userInfo: [String: String] = [
            "errorCode": error.errorCode,
            "category": error.category.rawValue,
            "severity": error.severity.rawValue,
            "userMessage": error.userMessage,
            "technicalMessage": error.technicalMessage
        ]
        
        if let context = context {
            userInfo["context"] = context
        }
        
        CrashReportingService.shared.reportCrash(
            description: "Critical error: \(error.technicalMessage)",
            userInfo: userInfo
        )
    }
}
