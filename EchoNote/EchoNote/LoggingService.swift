//
//  LoggingService.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import os.log

// MARK: - Log Categories

enum LogCategory: String, CaseIterable, Codable {
    case audio = "audio"
    case permissions = "permissions"
    case storage = "storage"
    case network = "network"
    case speech = "speech"
    case keyword = "keyword"
    case ui = "ui"
    case system = "system"
    case security = "security"
    case performance = "performance"
    case user = "user"
    case debug = "debug"
    
    var subsystem: String {
        return "com.clevorie.EchoNote"
    }
    
    var category: String {
        return self.rawValue
    }
    
    var osLog: OSLog {
        return OSLog(subsystem: subsystem, category: category)
    }
}

// MARK: - Log Levels

enum LogLevel: String, CaseIterable, Comparable, Codable {
    case debug = "debug"
    case info = "info"
    case notice = "notice"
    case warning = "warning"
    case error = "error"
    case fault = "fault"
    
    static func < (lhs: LogLevel, rhs: LogLevel) -> <PERSON><PERSON> {
        let order: [LogLevel] = [.debug, .info, .notice, .warning, .error, .fault]
        guard let lhsIndex = order.firstIndex(of: lhs),
              let rhsIndex = order.firstIndex(of: rhs) else {
            return false
        }
        return lhsIndex < rhsIndex
    }
    
    var osLogType: OSLogType {
        switch self {
        case .debug: return .debug
        case .info: return .info
        case .notice: return .default
        case .warning: return .default
        case .error: return .error
        case .fault: return .fault
        }
    }
    
    var emoji: String {
        switch self {
        case .debug: return "🔍"
        case .info: return "ℹ️"
        case .notice: return "📝"
        case .warning: return "⚠️"
        case .error: return "❌"
        case .fault: return "🚨"
        }
    }
}

// MARK: - Log Entry

struct LogEntry: Identifiable, Codable {
    let id = UUID()
    let timestamp: Date
    let level: LogLevel
    let category: LogCategory
    let message: String
    let metadata: [String: String]?
    let file: String
    let function: String
    let line: Int
    
    init(level: LogLevel, category: LogCategory, message: String, metadata: [String: String]? = nil, file: String = #file, function: String = #function, line: Int = #line) {
        self.timestamp = Date()
        self.level = level
        self.category = category
        self.message = message
        self.metadata = metadata
        self.file = URL(fileURLWithPath: file).lastPathComponent
        self.function = function
        self.line = line
    }
    
    var formattedMessage: String {
        let timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "HH:mm:ss.SSS"
        let timeString = timeFormatter.string(from: timestamp)
        
        let metadataString = metadata?.map { "\($0.key)=\($0.value)" }.joined(separator: ", ") ?? ""
        let metadataSuffix = metadataString.isEmpty ? "" : " [\(metadataString)]"
        
        return "\(level.emoji) \(timeString) [\(category.rawValue.uppercased())] \(message)\(metadataSuffix) (\(file):\(line))"
    }
}

// MARK: - Logging Service

@MainActor
class LoggingService: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var logEntries: [LogEntry] = []
    @Published var isLoggingEnabled = true
    @Published var minimumLogLevel: LogLevel = .info
    @Published var enabledCategories: Set<LogCategory> = Set(LogCategory.allCases)
    
    // MARK: - Properties
    
    private let maxLogEntries = 1000
    private let logQueue = DispatchQueue(label: "com.clevorie.EchoNote.logging", qos: .utility)
    private var logFileURL: URL?
    
    // MARK: - Singleton
    
    static let shared = LoggingService()
    
    private init() {
        setupLogFile()
        
        #if DEBUG
        minimumLogLevel = .debug
        #else
        minimumLogLevel = .info
        #endif
    }
    
    // MARK: - Logging Methods
    
    /// Log a debug message
    func debug(_ message: String, category: LogCategory = .debug, metadata: [String: String]? = nil, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .debug, category: category, message: message, metadata: metadata, file: file, function: function, line: line)
    }
    
    /// Log an info message
    func info(_ message: String, category: LogCategory = .system, metadata: [String: String]? = nil, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .info, category: category, message: message, metadata: metadata, file: file, function: function, line: line)
    }
    
    /// Log a notice message
    func notice(_ message: String, category: LogCategory = .system, metadata: [String: String]? = nil, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .notice, category: category, message: message, metadata: metadata, file: file, function: function, line: line)
    }
    
    /// Log a warning message
    func warning(_ message: String, category: LogCategory = .system, metadata: [String: String]? = nil, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .warning, category: category, message: message, metadata: metadata, file: file, function: function, line: line)
    }
    
    /// Log an error message
    func error(_ message: String, category: LogCategory = .system, metadata: [String: String]? = nil, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .error, category: category, message: message, metadata: metadata, file: file, function: function, line: line)
    }
    
    /// Log a fault message
    func fault(_ message: String, category: LogCategory = .system, metadata: [String: String]? = nil, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .fault, category: category, message: message, metadata: metadata, file: file, function: function, line: line)
    }
    
    /// Core logging method
    private func log(level: LogLevel, category: LogCategory, message: String, metadata: [String: String]?, file: String, function: String, line: Int) {
        guard isLoggingEnabled,
              level >= minimumLogLevel,
              enabledCategories.contains(category) else {
            return
        }
        
        let entry = LogEntry(level: level, category: category, message: redactSensitiveInfo(message), metadata: metadata, file: file, function: function, line: line)
        
        // Log to system
        logToSystem(entry)
        
        // Add to in-memory log
        Task { @MainActor in
            addToMemoryLog(entry)
        }
        
        // Log to file
        logToFile(entry)
    }
    
    // MARK: - System Logging
    
    private func logToSystem(_ entry: LogEntry) {
        let osLog = entry.category.osLog
        let metadataString = entry.metadata?.map { "\($0.key)=\($0.value)" }.joined(separator: ", ") ?? ""
        let fullMessage = metadataString.isEmpty ? entry.message : "\(entry.message) [\(metadataString)]"
        
        os_log("%{public}@", log: osLog, type: entry.level.osLogType, fullMessage)
    }
    
    // MARK: - Memory Logging
    
    private func addToMemoryLog(_ entry: LogEntry) {
        logEntries.insert(entry, at: 0)
        
        // Limit memory usage
        if logEntries.count > maxLogEntries {
            logEntries = Array(logEntries.prefix(maxLogEntries))
        }
    }
    
    // MARK: - File Logging
    
    private func setupLogFile() {
        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return
        }
        
        let logsDirectory = documentsPath.appendingPathComponent("Logs")
        
        do {
            try FileManager.default.createDirectory(at: logsDirectory, withIntermediateDirectories: true)
            
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: Date())
            
            logFileURL = logsDirectory.appendingPathComponent("echonote-\(dateString).log")
        } catch {
            print("Failed to setup log file: \(error)")
        }
    }
    
    private func logToFile(_ entry: LogEntry) {
        guard let logFileURL = logFileURL else { return }
        
        logQueue.async {
            let logLine = entry.formattedMessage + "\n"
            
            if let data = logLine.data(using: .utf8) {
                if FileManager.default.fileExists(atPath: logFileURL.path) {
                    // Append to existing file
                    if let fileHandle = try? FileHandle(forWritingTo: logFileURL) {
                        fileHandle.seekToEndOfFile()
                        fileHandle.write(data)
                        fileHandle.closeFile()
                    }
                } else {
                    // Create new file
                    try? data.write(to: logFileURL)
                }
            }
        }
    }
    
    // MARK: - Sensitive Information Redaction
    
    private func redactSensitiveInfo(_ message: String) -> String {
        var redactedMessage = message
        
        // Redact common sensitive patterns
        let patterns = [
            ("password", "***"),
            ("token", "***"),
            ("key", "***"),
            ("secret", "***"),
            ("api_key", "***"),
            ("auth", "***")
        ]
        
        for (pattern, replacement) in patterns {
            let regex = try? NSRegularExpression(pattern: "\(pattern)\\s*[=:]\\s*\\S+", options: .caseInsensitive)
            let range = NSRange(redactedMessage.startIndex..., in: redactedMessage)
            redactedMessage = regex?.stringByReplacingMatches(in: redactedMessage, options: [], range: range, withTemplate: "\(pattern)=\(replacement)") ?? redactedMessage
        }
        
        return redactedMessage
    }
    
    // MARK: - Log Management
    
    /// Clear in-memory logs
    func clearLogs() {
        logEntries.removeAll()
    }
    
    /// Get logs filtered by criteria
    func getFilteredLogs(level: LogLevel? = nil, category: LogCategory? = nil, searchText: String? = nil) -> [LogEntry] {
        var filtered = logEntries
        
        if let level = level {
            filtered = filtered.filter { $0.level >= level }
        }
        
        if let category = category {
            filtered = filtered.filter { $0.category == category }
        }
        
        if let searchText = searchText, !searchText.isEmpty {
            filtered = filtered.filter { $0.message.localizedCaseInsensitiveContains(searchText) }
        }
        
        return filtered
    }
    
    /// Export logs to string
    func exportLogs() -> String {
        return logEntries.map { $0.formattedMessage }.joined(separator: "\n")
    }
    
    /// Get log file URL for sharing
    func getLogFileURL() -> URL? {
        return logFileURL
    }
    
    // MARK: - Configuration
    
    func setMinimumLogLevel(_ level: LogLevel) {
        minimumLogLevel = level
    }
    
    func enableCategory(_ category: LogCategory) {
        enabledCategories.insert(category)
    }
    
    func disableCategory(_ category: LogCategory) {
        enabledCategories.remove(category)
    }
    
    func toggleLogging() {
        isLoggingEnabled.toggle()
    }
}
