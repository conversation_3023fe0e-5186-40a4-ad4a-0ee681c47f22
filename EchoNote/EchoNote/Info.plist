<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>NSMicrophoneUsageDescription</key>
	<string><PERSON>Note needs access to your microphone to record voice notes and detect keywords for smart organization.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>EchoNote uses speech recognition to automatically transcribe your voice notes and detect keywords for better organization.</string>
	<key>NSSiriUsageDescription</key>
	<string>EchoNote integrates with Siri to let you create voice notes and start recordings using voice commands.</string>
	<key>NSSupportsLiveActivities</key>
	<true/>
	<key>NSUserActivityTypes</key>
	<array>
		<string>com.clevorie.EchoNote.startRecording</string>
		<string>com.clevorie.EchoNote.quickNote</string>
		<string>com.clevorie.EchoNote.playLastRecording</string>
		<string>com.clevorie.EchoNote.searchNotes</string>
	</array>
</dict>
</plist>
