//
//  KeychainManager.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import Security

class KeychainManager {
    
    // MARK: - Error Types
    
    enum KeychainError: Error {
        case itemNotFound
        case duplicateItem
        case invalidData
        case unexpectedStatus(OSStatus)
        
        var localizedDescription: String {
            switch self {
            case .itemNotFound:
                return "Item not found in keychain"
            case .duplicateItem:
                return "Item already exists in keychain"
            case .invalidData:
                return "Invalid data format"
            case .unexpectedStatus(let status):
                return "Unexpected keychain status: \(status)"
            }
        }
    }
    
    // MARK: - Constants
    
    private static let service = "com.clevorie.EchoNote"
    
    // MARK: - Public Methods
    
    /// Save data to keychain
    static func save(_ data: Data, forKey key: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        // Delete existing item if it exists
        SecItemDelete(query as CFDictionary)
        
        let status = SecItemAdd(query as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            throw KeychainError.unexpectedStatus(status)
        }
    }
    
    /// Save string to keychain
    static func save(_ string: String, forKey key: String) throws {
        guard let data = string.data(using: .utf8) else {
            throw KeychainError.invalidData
        }
        try save(data, forKey: key)
    }
    
    /// Load data from keychain
    static func load(forKey key: String) throws -> Data {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess else {
            if status == errSecItemNotFound {
                throw KeychainError.itemNotFound
            }
            throw KeychainError.unexpectedStatus(status)
        }
        
        guard let data = result as? Data else {
            throw KeychainError.invalidData
        }
        
        return data
    }
    
    /// Load string from keychain
    static func loadString(forKey key: String) throws -> String {
        let data = try load(forKey: key)
        guard let string = String(data: data, encoding: .utf8) else {
            throw KeychainError.invalidData
        }
        return string
    }
    
    /// Delete item from keychain
    static func delete(forKey key: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            throw KeychainError.unexpectedStatus(status)
        }
    }
    
    /// Check if item exists in keychain
    static func exists(forKey key: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: false
        ]
        
        let status = SecItemCopyMatching(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    /// Clear all keychain items for this app
    static func clearAll() throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            throw KeychainError.unexpectedStatus(status)
        }
    }
}

// MARK: - Convenience Methods for Common Data Types

extension KeychainManager {
    
    /// Save Codable object to keychain
    static func save<T: Codable>(_ object: T, forKey key: String) throws {
        let data = try JSONEncoder().encode(object)
        try save(data, forKey: key)
    }
    
    /// Load Codable object from keychain
    static func load<T: Codable>(_ type: T.Type, forKey key: String) throws -> T {
        let data = try load(forKey: key)
        return try JSONDecoder().decode(type, from: data)
    }
    
    /// Save Bool to keychain
    static func save(_ bool: Bool, forKey key: String) throws {
        let data = Data([bool ? 1 : 0])
        try save(data, forKey: key)
    }
    
    /// Load Bool from keychain
    static func loadBool(forKey key: String) throws -> Bool {
        let data = try load(forKey: key)
        guard let byte = data.first else {
            throw KeychainError.invalidData
        }
        return byte == 1
    }
}

// MARK: - Secure Data Extension

extension Data {
    /// Securely clear data from memory
    mutating func secureClear() {
        self.withUnsafeMutableBytes { pointer in
            memset_s(pointer.baseAddress, pointer.count, 0, pointer.count)
        }
    }
}

// MARK: - EchoNote Specific Keychain Keys

extension KeychainManager {
    
    enum EchoNoteKeys {
        static let encryptionKey = "encryption_key"
        static let privacySettings = "privacy_settings"
        static let userSecrets = "user_secrets"
        static let analyticsOptOut = "analytics_opt_out"
    }
    
    /// Generate and store encryption key for local data
    static func generateEncryptionKey() throws -> Data {
        var keyData = Data(count: 32) // 256-bit key
        let result = keyData.withUnsafeMutableBytes { pointer in
            SecRandomCopyBytes(kSecRandomDefault, 32, pointer.bindMemory(to: UInt8.self).baseAddress!)
        }
        
        guard result == errSecSuccess else {
            throw KeychainError.unexpectedStatus(result)
        }
        
        try save(keyData, forKey: EchoNoteKeys.encryptionKey)
        return keyData
    }
    
    /// Get or generate encryption key
    static func getEncryptionKey() throws -> Data {
        do {
            return try load(forKey: EchoNoteKeys.encryptionKey)
        } catch KeychainError.itemNotFound {
            return try generateEncryptionKey()
        }
    }
}
