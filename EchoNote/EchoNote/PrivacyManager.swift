import Foundation
import SwiftData

// MARK: - App Privacy Settings

struct AppPrivacySettings: Codable {
    var allowAnalytics: Bool = false
    var allowCrashReporting: Bool = true
    var allowUsageTracking: Bool = false
    var shareImprovementData: Bool = false
    var enableCloudSync: Bool = false
    var autoDeleteOldNotes: Bool = false
    var autoDeleteDays: Int = 365
    var encryptLocalData: Bool = true

    static let `default` = AppPrivacySettings()
}

// MARK: - Data Deletion Options

enum DataDeletionScope {
    case allData
    case notesOnly
    case keywordsOnly
    case audioFilesOnly
    case userPreferencesOnly
    case analyticsDataOnly
    
    var displayName: String {
        switch self {
        case .allData:
            return "All Data"
        case .notesOnly:
            return "Notes Only"
        case .keywordsOnly:
            return "Keywords Only"
        case .audioFilesOnly:
            return "Audio Files Only"
        case .userPreferencesOnly:
            return "User Preferences Only"
        case .analyticsDataOnly:
            return "Analytics Data Only"
        }
    }
    
    var description: String {
        switch self {
        case .allData:
            return "Permanently delete all app data including notes, audio files, keywords, and preferences"
        case .notesOnly:
            return "Delete all notes and their associated audio files"
        case .keywordsOnly:
            return "Delete all custom keywords and their usage data"
        case .audioFilesOnly:
            return "Delete all audio recordings while keeping text notes"
        case .userPreferencesOnly:
            return "Reset all app settings and preferences to defaults"
        case .analyticsDataOnly:
            return "Delete all analytics and usage tracking data"
        }
    }
}

// MARK: - Privacy Manager

@MainActor
class PrivacyManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var privacySettings: AppPrivacySettings = .default
    @Published var hasAcceptedPrivacyPolicy: Bool = false
    @Published var privacyPolicyVersion: String = "1.0"
    @Published var lastPrivacyPolicyAcceptanceDate: Date?
    
    // MARK: - Private Properties
    
    private let userDefaults = UserDefaults.standard
    private let fileManager = FileManager.default
    private var modelContext: ModelContext?
    private let secureDataManager: SecureDataManager?
    
    // Keys for UserDefaults
    private enum Keys {
        static let privacySettings = "privacySettings"
        static let hasAcceptedPrivacyPolicy = "hasAcceptedPrivacyPolicy"
        static let privacyPolicyVersion = "privacyPolicyVersion"
        static let lastAcceptanceDate = "lastPrivacyPolicyAcceptanceDate"
    }
    
    // MARK: - Initialization
    
    init(modelContext: ModelContext? = nil) {
        self.modelContext = modelContext

        // Initialize secure data manager
        do {
            self.secureDataManager = try SecureDataManager()
        } catch {
            print("Failed to initialize secure data manager: \(error)")
            self.secureDataManager = nil
        }

        loadPrivacySettings()
    }
    
    // MARK: - Privacy Settings Management
    
    func updatePrivacySettings(_ newSettings: AppPrivacySettings) {
        privacySettings = newSettings
        savePrivacySettings()
    }
    
    func resetPrivacySettings() {
        privacySettings = .default
        savePrivacySettings()
    }
    
    func acceptPrivacyPolicy(version: String = "1.0") {
        hasAcceptedPrivacyPolicy = true
        privacyPolicyVersion = version
        lastPrivacyPolicyAcceptanceDate = Date()
        savePrivacySettings()
    }
    
    func revokePrivacyPolicyAcceptance() {
        hasAcceptedPrivacyPolicy = false
        lastPrivacyPolicyAcceptanceDate = nil
        savePrivacySettings()
    }
    
    // MARK: - Data Deletion
    
    func deleteData(scope: DataDeletionScope) async throws {
        switch scope {
        case .allData:
            try await deleteAllData()
        case .notesOnly:
            try await deleteNotes()
        case .keywordsOnly:
            try await deleteKeywords()
        case .audioFilesOnly:
            try await deleteAudioFiles()
        case .userPreferencesOnly:
            try await deleteUserPreferences()
        case .analyticsDataOnly:
            try await deleteAnalyticsData()
        }
    }
    
    private func deleteAllData() async throws {
        // Delete all SwiftData models
        try await deleteNotes()
        try await deleteKeywords()
        try await deleteUserPreferences()
        
        // Delete audio files
        try await deleteAudioFiles()
        
        // Delete analytics data
        try await deleteAnalyticsData()
        
        // Reset privacy settings
        resetPrivacySettings()
        revokePrivacyPolicyAcceptance()
    }
    
    private func deleteNotes() async throws {
        guard let context = modelContext else { return }
        
        // Fetch all notes
        let descriptor = FetchDescriptor<Note>()
        let notes = try context.fetch(descriptor)
        
        // Delete each note
        for note in notes {
            context.delete(note)
        }
        
        try context.save()
    }
    
    private func deleteKeywords() async throws {
        guard let context = modelContext else { return }
        
        // Fetch all keywords
        let descriptor = FetchDescriptor<Keyword>()
        let keywords = try context.fetch(descriptor)
        
        // Delete each keyword
        for keyword in keywords {
            context.delete(keyword)
        }
        
        try context.save()
    }
    
    private func deleteUserPreferences() async throws {
        guard let context = modelContext else { return }
        
        // Fetch all user preferences
        let descriptor = FetchDescriptor<UserPreferences>()
        let preferences = try context.fetch(descriptor)
        
        // Delete each preference
        for preference in preferences {
            context.delete(preference)
        }
        
        try context.save()
    }
    
    private func deleteAudioFiles() async throws {
        let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        
        // Get all audio files in documents directory
        let audioExtensions = ["m4a", "wav", "mp3", "aac"]
        let contents = try fileManager.contentsOfDirectory(at: documentsURL, includingPropertiesForKeys: nil)
        
        for url in contents {
            let fileExtension = url.pathExtension.lowercased()
            if audioExtensions.contains(fileExtension) {
                try fileManager.removeItem(at: url)
            }
        }
    }
    
    private func deleteAnalyticsData() async throws {
        // Remove analytics-related UserDefaults
        let analyticsKeys = [
            "analytics_session_count",
            "analytics_notes_created",
            "analytics_keywords_used",
            "analytics_recording_duration",
            "analytics_last_session_date"
        ]
        
        for key in analyticsKeys {
            userDefaults.removeObject(forKey: key)
        }
    }
    
    // MARK: - Data Export
    
    func exportUserData() async throws -> URL {
        let exportData = try await gatherUserData()
        let jsonData = try JSONSerialization.data(withJSONObject: exportData, options: .prettyPrinted)

        let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let exportURL = documentsURL.appendingPathComponent("user_data_export_\(Date().timeIntervalSince1970).json")

        try jsonData.write(to: exportURL)
        return exportURL
    }

    private func gatherUserData() async throws -> [String: Any] {
        var exportData: [String: Any] = [:]

        // Add privacy settings
        let privacyData = try JSONEncoder().encode(privacySettings)
        if let privacyDict = try JSONSerialization.jsonObject(with: privacyData) as? [String: Any] {
            exportData["privacySettings"] = privacyDict
        }

        exportData["privacyPolicyAcceptance"] = [
            "accepted": hasAcceptedPrivacyPolicy,
            "version": privacyPolicyVersion,
            "date": lastPrivacyPolicyAcceptanceDate?.timeIntervalSince1970 as Any
        ]

        // Add notes data (if context is available)
        if let context = modelContext {
            let notesDescriptor = FetchDescriptor<Note>()
            let notes = try context.fetch(notesDescriptor)
            exportData["notesCount"] = notes.count

            let keywordsDescriptor = FetchDescriptor<Keyword>()
            let keywords = try context.fetch(keywordsDescriptor)
            exportData["keywordsCount"] = keywords.count
        }

        return exportData
    }
    
    // MARK: - Secure Storage
    
    func isDataEncrypted() -> Bool {
        return privacySettings.encryptLocalData
    }
    
    func enableDataEncryption() {
        var newSettings = privacySettings
        newSettings.encryptLocalData = true
        updatePrivacySettings(newSettings)
    }
    
    func disableDataEncryption() {
        var newSettings = privacySettings
        newSettings.encryptLocalData = false
        updatePrivacySettings(newSettings)
    }
    
    // MARK: - Privacy Policy
    
    func getPrivacyPolicyURL() -> URL? {
        // In a real app, this would point to your privacy policy
        return URL(string: "https://example.com/privacy-policy")
    }
    
    func needsPrivacyPolicyUpdate() -> Bool {
        let currentVersion = "1.0"
        return privacyPolicyVersion != currentVersion
    }
    
    // MARK: - Persistence
    
    private func savePrivacySettings() {
        do {
            // Save to keychain for enhanced security
            try KeychainManager.save(privacySettings, forKey: KeychainManager.EchoNoteKeys.privacySettings)
            try KeychainManager.save(hasAcceptedPrivacyPolicy, forKey: Keys.hasAcceptedPrivacyPolicy)

            // Also save to UserDefaults as backup
            let data = try JSONEncoder().encode(privacySettings)
            userDefaults.set(data, forKey: Keys.privacySettings)
            userDefaults.set(hasAcceptedPrivacyPolicy, forKey: Keys.hasAcceptedPrivacyPolicy)
            userDefaults.set(privacyPolicyVersion, forKey: Keys.privacyPolicyVersion)

            if let date = lastPrivacyPolicyAcceptanceDate {
                userDefaults.set(date, forKey: Keys.lastAcceptanceDate)
            }
        } catch {
            print("Failed to save privacy settings: \(error)")
        }
    }
    
    private func loadPrivacySettings() {
        // Try to load from keychain first (more secure)
        do {
            privacySettings = try KeychainManager.load(AppPrivacySettings.self, forKey: KeychainManager.EchoNoteKeys.privacySettings)
            hasAcceptedPrivacyPolicy = try KeychainManager.loadBool(forKey: Keys.hasAcceptedPrivacyPolicy)
        } catch {
            // Fallback to UserDefaults
            if let data = userDefaults.data(forKey: Keys.privacySettings),
               let settings = try? JSONDecoder().decode(AppPrivacySettings.self, from: data) {
                privacySettings = settings
            }
            hasAcceptedPrivacyPolicy = userDefaults.bool(forKey: Keys.hasAcceptedPrivacyPolicy)
        }

        // Load other privacy-related settings
        privacyPolicyVersion = userDefaults.string(forKey: Keys.privacyPolicyVersion) ?? "1.0"
        lastPrivacyPolicyAcceptanceDate = userDefaults.object(forKey: Keys.lastAcceptanceDate) as? Date
    }
}
