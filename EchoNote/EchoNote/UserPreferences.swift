//
//  UserPreferences.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import SwiftData

@Model
final class UserPreferences {
    var id: UUID
    var recordingMode: RecordingMode
    var theme: AppTheme
    var privacySettings: PrivacySettings
    var autoStopDuration: TimeInterval // seconds
    var maxRecordingDuration: TimeInterval // seconds
    var microphoneAnimationEnabled: Bool
    var headphoneBreathingSpeed: Double // Animation speed multiplier (0.5 = slow, 1.0 = normal, 2.0 = fast)
    var backgroundMonitorEnabled: Bool // Enable background speech monitoring
    var recordAudioWhenCreatingNote: Bool // Whether to record audio when creating notes
    var showNotesOverview: Bool
    var hapticFeedbackEnabled: Bool
    var language: String
    var createdDate: Date
    var modifiedDate: Date
    
    init() {
        self.id = UUID()
        self.recordingMode = .tapToRecord
        self.theme = .system
        self.privacySettings = PrivacySettings()
        self.autoStopDuration = 5.0 // 5 seconds of silence
        self.maxRecordingDuration = 300.0 // 5 minutes
        self.microphoneAnimationEnabled = true
        self.headphoneBreathingSpeed = 1.0 // Normal speed
        self.backgroundMonitorEnabled = false // Disabled by default for privacy
        self.recordAudioWhenCreatingNote = true // Enabled by default
        self.showNotesOverview = true
        self.hapticFeedbackEnabled = true
        self.language = "en"
        self.createdDate = Date()
        self.modifiedDate = Date()
    }
    
    func updateModifiedDate() {
        self.modifiedDate = Date()
    }
}

enum RecordingMode: String, CaseIterable, Codable {
    case tapToRecord = "tap_to_record"
    case continuous = "continuous"
    case pushToTalk = "push_to_talk"
    
    var displayName: String {
        switch self {
        case .tapToRecord:
            return "Tap to Record"
        case .continuous:
            return "Continuous"
        case .pushToTalk:
            return "Push to Talk"
        }
    }
}

enum AppTheme: String, CaseIterable, Codable {
    case light = "light"
    case dark = "dark"
    case system = "system"
    
    var displayName: String {
        switch self {
        case .light:
            return "Light"
        case .dark:
            return "Dark"
        case .system:
            return "System"
        }
    }
}

struct PrivacySettings: Codable {
    var speechRecognitionEnabled: Bool
    var dataCollectionEnabled: Bool
    var analyticsEnabled: Bool
    
    init() {
        self.speechRecognitionEnabled = true
        self.dataCollectionEnabled = false
        self.analyticsEnabled = false
    }
}
