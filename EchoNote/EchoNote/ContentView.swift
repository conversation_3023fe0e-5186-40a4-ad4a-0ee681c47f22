//
//  ContentView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData

struct ContentView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var notes: [Note]
    @Query private var keywords: [Keyword]

    @StateObject private var permissionsService = PermissionsService()
    @State private var showingPermissionRequest = false

    var body: some View {
        Group {
            if showingPermissionRequest {
                PermissionRequestView {
                    showingPermissionRequest = false
                }
            } else {
                MainTabView()
                    .withServiceContainer(ServiceContainer.shared)
            }
        }
        .errorHandling()
        .onAppear {
            setupInitialData()
            checkPermissions()
        }
    }

    private func setupInitialData() {
        // Configure ServiceContainer with ModelContext
        ServiceContainer.shared.configure(with: modelContext)

        // Create default user preferences if they don't exist
        let dataManager = DataManager(modelContext: modelContext)
        _ = dataManager.createOrUpdateUserPreferences()

        // Initialize BackgroundMonitorService with DataManager
        BackgroundMonitorService.shared.setDataManager(dataManager)

        // Create sample data if no notes exist
        if notes.isEmpty {
            createSampleData()
        }
    }

    private func checkPermissions() {
        Task {
            await permissionsService.checkAllPermissions()

            await MainActor.run {
                // Show permission request if not all required permissions are granted
                // and we haven't already requested them
                if !permissionsService.allRequiredPermissionsGranted && !permissionsService.hasRequestedPermissions {
                    showingPermissionRequest = true
                }
            }
        }
    }

    private func createSampleData() {
        let dataManager = DataManager(modelContext: modelContext)

        // Create sample keywords
        let sampleKeywords = [
            ("Idea", "#FF3B30"),
            ("Groceries", "#34C759"),
            ("Meeting", "#AF52DE"),
            ("Task", "#FFCC00"),
            ("Work", "#007AFF"),
            ("Personal", "#FF9500"),
            ("Important", "#FF2D92"),
            ("Reminder", "#5856D6")
        ]

        var createdKeywords: [Keyword] = []
        for (text, color) in sampleKeywords {
            let keyword = dataManager.createKeyword(text: text, color: color)
            createdKeywords.append(keyword)
        }

        // Create sample notes with associated keywords
        let sampleNotesData = [
            ("Meeting Notes", "Discussed project timeline and deliverables for Q1. Need to follow up on budget approval and resource allocation.", [0, 2]), // Idea, Meeting
            ("Grocery List", "Milk, bread, eggs, apples, and chicken for dinner. Don't forget organic vegetables and Greek yogurt.", [1]), // Groceries
            ("App Idea", "Voice note app with AI-powered keyword detection and organization. Could integrate with calendar and task management.", [0, 3]), // Idea, Task
            ("Daily Tasks", "Complete code review, update documentation, and prepare presentation for tomorrow's client meeting.", [3, 4]), // Task, Work
            ("Weekend Plans", "Visit the farmer's market, call mom, and finish reading that book. Maybe catch a movie if time permits.", [5]), // Personal
            ("Project Deadline", "Remember to submit the final report by Friday. Double-check all calculations and references.", [6, 7, 4]), // Important, Reminder, Work
            ("Birthday Party", "Sarah's birthday is next Saturday. Need to buy a gift and confirm the restaurant reservation.", [7, 5]), // Reminder, Personal
            ("Workout Routine", "Start with 30 minutes cardio, then strength training. Focus on core exercises this week.", [5, 3]) // Personal, Task
        ]

        for (title, content, keywordIndices) in sampleNotesData {
            let noteKeywords = keywordIndices.compactMap { index in
                index < createdKeywords.count ? createdKeywords[index] : nil
            }

            let note = dataManager.createNote(
                title: title,
                content: content,
                audioURL: nil,
                keywords: noteKeywords
            )

            // Set some random durations for demo purposes
            note.duration = TimeInterval.random(in: 15...180) // 15 seconds to 3 minutes

            // Make some notes favorites
            if [0, 2, 5].contains(sampleNotesData.firstIndex(where: { $0.0 == title })) {
                note.isFavorite = true
            }

            // Set different creation dates for variety
            if let index = sampleNotesData.firstIndex(where: { $0.0 == title }) {
                note.createdAt = Calendar.current.date(byAdding: .day, value: -index, to: Date()) ?? Date()
                note.updatedAt = note.createdAt
            }
        }

        // Save all changes
        do {
            try modelContext.save()
        } catch {
            print("Error saving sample data: \(error)")
        }
    }
}

#Preview {
    ContentView()
        .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
