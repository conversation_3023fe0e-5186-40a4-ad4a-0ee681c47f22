//
//  HomeView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData

struct HomeView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.serviceContainer) private var serviceContainer
    @Query private var notes: [Note]
    @Query private var keywords: [Keyword]
    @Query private var userPreferences: [UserPreferences]
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var viewModel = HomeViewModel()

    private var recordingCoordinator: RecordingCoordinator {
        serviceContainer.getRecordingCoordinator()
    }
    
    private var preferences: UserPreferences? {
        userPreferences.first
    }
    
    private var shouldShowNotesOverview: Bool {
        viewModel.shouldShowNotesOverview(preferences: preferences)
    }

    private var keywordData: [(keyword: String, count: Int, color: String)] {
        viewModel.generateChartData(notes: notes, keywords: keywords)
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Apply theme background
                Group {
                    if themeManager.isGradientTheme,
                       let gradient = themeManager.applyGradient() {
                        gradient.ignoresSafeArea()
                    } else {
                        themeManager.applyBackground().ignoresSafeArea()
                    }
                }

                if shouldShowNotesOverview {
                    // Normal layout with notes overview
                    VStack(spacing: 0) {
                        // Header
                        headerView

                        ScrollView {
                            VStack(spacing: 20) {
                                // Notes Overview Card
                                notesOverviewCard

                                // Center Microphone Section
                                centerMicrophoneSection

                                Spacer(minLength: 100) // Space for tab bar
                            }
                            .padding(.horizontal, 20)
                        }
                    }
                } else {
                    // Centered layout when notes overview is off
                    VStack(spacing: 0) {
                        // Header
                        headerView

                        // Centered microphone button
                        VStack {
                            Spacer()

                            HStack {
                                Spacer()
                                centerMicrophoneSection
                                Spacer()
                            }

                            Spacer()
                            Spacer(minLength: 100) // Account for tab bar
                        }
                        .padding(.horizontal, 20)
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .fullScreenCover(isPresented: $viewModel.showingRecordingView) {
            RecordingView(isPresented: $viewModel.showingRecordingView)
        }
        .onAppear {
            viewModel.handleViewAppear(preferences: preferences)
        }
    }
    
    private var headerView: some View {
        HStack {
            if shouldShowNotesOverview {
                // Chart type selector
                HStack(spacing: 15) {
                    ForEach(ChartType.allCases, id: \.self) { chartType in
                        Button(action: {
                            selectedChartType = chartType
                        }) {
                            Image(systemName: chartType.iconName)
                                .font(.system(size: 14))
                                .foregroundColor(selectedChartType == chartType ? .black : .white)
                                .frame(width: 30, height: 30)
                                .background(
                                    Circle()
                                        .fill(selectedChartType == chartType ? .green : Color.gray.opacity(0.3))
                                )
                        }
                    }
                }
            } else {
                Spacer()
            }
            
            Spacer()
            
            // Header actions
            HStack(spacing: 15) {
                Button(action: {
                    // Search action
                }) {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.white)
                }
                
                Button(action: {
                    // Notifications action
                }) {
                    Image(systemName: "bell")
                        .foregroundColor(.white)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    private var notesOverviewCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Notes Overview")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
            }
            
            Text("Total Notes: \(notes.count)")
                .font(.subheadline)
                .foregroundColor(.gray)
            
            // Simple chart visualization
            if !keywords.isEmpty {
                SimpleChartView(data: keywordData, chartType: selectedChartType)
                    .frame(height: 200)
            } else {
                Text("No keywords yet")
                    .foregroundColor(.gray)
                    .frame(height: 200)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.gray.opacity(0.1))
        )
    }
    
    private var centerMicrophoneSection: some View {
        AnimatedHeadphoneButton(viewModel: viewModel)
    }
    



}

enum ChartType: CaseIterable {
    case pie, bar, line, radar
    
    var iconName: String {
        switch self {
        case .pie: return "chart.pie.fill"
        case .bar: return "chart.bar.fill"
        case .line: return "chart.line.uptrend.xyaxis"
        case .radar: return "dot.radiowaves.left.and.right"
        }
    }
}



struct SimpleChartView: View {
    let data: [(keyword: String, count: Int, color: String)]
    let chartType: ChartType

    var body: some View {
        switch chartType {
        case .pie:
            PieChartView(data: data)
        case .bar:
            BarChartView(data: data)
        case .line:
            LineChartView(data: data)
        case .radar:
            RadarChartView(data: data)
        }
    }
}

struct PieChartView: View {
    let data: [(keyword: String, count: Int, color: String)]

    private var total: Int {
        data.reduce(0) { $0 + $1.count }
    }

    var body: some View {
        HStack {
            // Simple pie chart representation
            ZStack {
                Circle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(width: 120, height: 120)

                ForEach(Array(data.enumerated()), id: \.offset) { index, item in
                    Circle()
                        .trim(from: 0, to: CGFloat(item.count) / CGFloat(total))
                        .stroke(Color(hex: item.color), lineWidth: 20)
                        .frame(width: 120, height: 120)
                        .rotationEffect(.degrees(Double(index) * 90))
                }
            }

            // Legend
            VStack(alignment: .leading, spacing: 8) {
                ForEach(data, id: \.keyword) { item in
                    HStack {
                        Circle()
                            .fill(Color(hex: item.color))
                            .frame(width: 12, height: 12)
                        Text("\(item.keyword): \(item.count)")
                            .font(.caption)
                            .foregroundColor(.white)
                    }
                }
            }

            Spacer()
        }
    }
}

struct BarChartView: View {
    let data: [(keyword: String, count: Int, color: String)]

    private var maxCount: Int {
        data.map(\.count).max() ?? 1
    }

    var body: some View {
        HStack(alignment: .bottom, spacing: 12) {
            ForEach(data, id: \.keyword) { item in
                VStack {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color(hex: item.color))
                        .frame(width: 30, height: CGFloat(item.count) / CGFloat(maxCount) * 120)

                    Text(item.keyword)
                        .font(.caption)
                        .foregroundColor(.white)
                        .rotationEffect(.degrees(-45))
                }
            }
        }
        .padding()
    }
}

struct LineChartView: View {
    let data: [(keyword: String, count: Int, color: String)]

    var body: some View {
        Text("Line Chart")
            .foregroundColor(.gray)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct RadarChartView: View {
    let data: [(keyword: String, count: Int, color: String)]

    var body: some View {
        Text("Radar Chart")
            .foregroundColor(.gray)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Animation Manager
class HeadphoneAnimationManager: ObservableObject {
    static let shared = HeadphoneAnimationManager()

    @Published var isBreathing = false
    @Published var tiltAngle: Double = 0

    private var breathingTimer: Timer?
    private var tiltingTimer: Timer?
    private var isAnimating = false
    private var breathingSpeed: Double = 1.0

    private init() {
        startContinuousAnimations()
    }

    func updateBreathingSpeed(_ speed: Double) {
        breathingSpeed = speed
        if isAnimating {
            stopAnimations()
            startContinuousAnimations()
        }
    }

    func startContinuousAnimations() {
        guard !isAnimating else { return }
        isAnimating = true

        // Calculate intervals based on breathing speed
        let breathingInterval = 2.0 / breathingSpeed
        let breathingDuration = 2.0 / breathingSpeed
        let tiltingInterval = 1.5 / breathingSpeed
        let tiltingDuration = 1.5 / breathingSpeed

        // 呼吸动画 - 使用用户设置的速度
        breathingTimer = Timer.scheduledTimer(withTimeInterval: breathingInterval, repeats: true) { _ in
            withAnimation(.easeInOut(duration: breathingDuration)) {
                self.isBreathing.toggle()
            }
        }

        // 倾斜动画 - 使用用户设置的速度
        tiltingTimer = Timer.scheduledTimer(withTimeInterval: tiltingInterval, repeats: true) { _ in
            withAnimation(.easeInOut(duration: tiltingDuration)) {
                self.tiltAngle = self.tiltAngle == 0 ? 15.0 : (self.tiltAngle == 15.0 ? -15.0 : 0)
            }
        }

        // 立即开始第一次动画
        withAnimation(.easeInOut(duration: breathingDuration)) {
            isBreathing = true
        }
        withAnimation(.easeInOut(duration: tiltingDuration)) {
            tiltAngle = 15.0
        }
    }

    func stopAnimations() {
        breathingTimer?.invalidate()
        tiltingTimer?.invalidate()
        breathingTimer = nil
        tiltingTimer = nil
        isAnimating = false
    }

    deinit {
        stopAnimations()
    }
}

// MARK: - Animated Headphone Button

struct AnimatedHeadphoneButton: View {
    @ObservedObject var viewModel: HomeViewModel
    @Environment(\.serviceContainer) private var serviceContainer
    @StateObject private var animationManager = HeadphoneAnimationManager.shared
    @Query private var userPreferences: [UserPreferences]

    private var preferences: UserPreferences? {
        userPreferences.first
    }

    private var recordingCoordinator: RecordingCoordinator {
        serviceContainer.getRecordingCoordinator()
    }

    private var isBackgroundMonitorEnabled: Bool {
        preferences?.backgroundMonitorEnabled ?? false
    }

    private var isActivelyMonitoring: Bool {
        let enabled = isBackgroundMonitorEnabled
        let isListening = case .listening = recordingCoordinator.currentState
        let result = enabled && (isListening || recordingCoordinator.isBackgroundMonitoringEnabled)

        // Debug logging for state tracking
        if enabled && !monitoring && !starting && !hasError {
            print("🔍 State mismatch - Enabled: \(enabled), Monitoring: \(monitoring), Starting: \(starting)")
            print("🔍 Attempting to restart background monitoring...")

            // Try to restart the service
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                if !self.backgroundMonitor.isMonitoring && !self.backgroundMonitor.isStarting {
                    self.backgroundMonitor.startMonitoring()
                }
            }
        } else if enabled && starting {
            print("🔄 Service starting - Enabled: \(enabled), Starting: \(starting)")
        } else if enabled && monitoring {
            print("✅ Service active - Enabled: \(enabled), Monitoring: \(monitoring)")
        } else if enabled && hasError {
            print("❌ Service error - Enabled: \(enabled), Error: \(backgroundMonitor.lastError ?? "Unknown")")
        }

        return result
    }

    // Color scheme based on monitoring state
    private var glowColor: Color {
        isActivelyMonitoring ? .purple : .green
    }

    private var buttonColor: Color {
        isActivelyMonitoring ? .purple : .green
    }

    private var pulseColor: Color {
        isActivelyMonitoring ? .purple : .green
    }

    private var iconColor: Color {
        if isActivelyMonitoring {
            return Color(red: 1.0, green: 0.84, blue: 0.0) // Gold for active monitoring
        } else {
            return Color(red: 1.0, green: 0.84, blue: 0.0) // Gold for normal state
        }
    }

    private var headphoneIcon: String {
        isActivelyMonitoring ? "headphones.circle.fill" : "headphones"
    }



    var body: some View {
        VStack(spacing: 20) {
            Button(action: {
                Task {
                    await viewModel.startManualRecording()
                }
            }) {
                ZStack {
                    // Outer breathing glow effect - changes color based on monitoring state
                    Circle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [
                                    glowColor.opacity(0.3),
                                    glowColor.opacity(0.1),
                                    Color.clear
                                ]),
                                center: .center,
                                startRadius: 80,
                                endRadius: 140
                            )
                        )
                        .frame(width: 280, height: 280)
                        .scaleEffect(animationManager.isBreathing ? 1.2 : 1.0)
                        .opacity(animationManager.isBreathing ? (isActivelyMonitoring ? 1.0 : 0.8) : (isActivelyMonitoring ? 0.6 : 0.4))

                    // Main headphone button - changes gradient based on monitoring state
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [buttonColor, buttonColor.opacity(0.8)]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 200, height: 200)
                        .shadow(color: buttonColor.opacity(0.5), radius: 20, x: 0, y: 10)
                        .scaleEffect(animationManager.isBreathing ? 1.05 : 1.0)

                    // Inner pulse effect - enhanced when monitoring
                    Circle()
                        .stroke(pulseColor.opacity(0.6), lineWidth: isActivelyMonitoring ? 4 : 3)
                        .frame(width: 220, height: 220)
                        .scaleEffect(animationManager.isBreathing ? 1.1 : 0.9)
                        .opacity(animationManager.isBreathing ? 0.0 : (isActivelyMonitoring ? 1.0 : 0.8))

                    // Headphone icon with monitoring state indication
                    ZStack {
                        Image(systemName: headphoneIcon)
                            .font(.system(size: 80, weight: .bold))
                            .foregroundColor(iconColor)
                            .scaleEffect(animationManager.isBreathing ? 1.02 : 1.0)
                            .rotationEffect(.degrees(animationManager.tiltAngle))

                        // Active monitoring indicator
                        if isActivelyMonitoring {
                            Circle()
                                .fill(Color.red)
                                .frame(width: 16, height: 16)
                                .offset(x: 35, y: -35)
                                .scaleEffect(animationManager.isBreathing ? 1.2 : 1.0)
                                .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: animationManager.isBreathing)
                        }
                    }
                }
            }
            .scaleEffect(showingRecordingView ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: showingRecordingView)

            VStack(spacing: 4) {
                if isActivelyMonitoring {
                    Text("🔴 Background monitoring active")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.red)
                        .opacity(animationManager.isBreathing ? 1.0 : 0.8)
                }
            }
        }
        .padding(.top, 40)
        .padding(.bottom, isActivelyMonitoring ? 120 : 40) // Extra bottom padding when monitoring text is shown
        .onAppear {
            // Update animation speed when view appears
            if let speed = preferences?.headphoneBreathingSpeed {
                animationManager.updateBreathingSpeed(speed)
            }
        }
        .onChange(of: preferences?.headphoneBreathingSpeed) { _, newSpeed in
            // Update animation speed when preference changes
            if let speed = newSpeed {
                animationManager.updateBreathingSpeed(speed)
            }
        }
        .onChange(of: isBackgroundMonitorEnabled) { _, isEnabled in
            // Update visual state when background monitoring is toggled
            withAnimation(.easeInOut(duration: 0.5)) {
                // Animation will automatically update due to computed properties
            }
        }
        .onChange(of: backgroundMonitor.isMonitoring) { _, isMonitoring in
            // Update visual state when monitoring status changes
            print("🎧 HomeView: Background monitoring status changed to: \(isMonitoring)")
            withAnimation(.easeInOut(duration: 0.3)) {
                // Animation will automatically update due to computed properties
            }
        }
        .onAppear {
            // Force refresh of monitoring status when view appears
            print("🎧 AnimatedHeadphoneButton appeared - checking background monitor status")
            print("   - Background monitor enabled: \(isBackgroundMonitorEnabled)")
            print("   - Recording coordinator state: \(recordingCoordinator.currentState)")
            print("   - Background monitoring enabled: \(recordingCoordinator.isBackgroundMonitoringEnabled)")
            print("   - Is actively monitoring: \(isActivelyMonitoring)")
            print("   - Current glow color: \(isActivelyMonitoring ? "purple" : "green")")

            // Start background monitoring if enabled and not already active
            if isBackgroundMonitorEnabled && !recordingCoordinator.isBackgroundMonitoringEnabled {
                Task {
                    await recordingCoordinator.startBackgroundMonitoring()
                }
            }

            // Force a UI update with animation
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    // The computed properties will be re-evaluated
                }
            }
        }
    }

}

#Preview {
    HomeView()
        .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
