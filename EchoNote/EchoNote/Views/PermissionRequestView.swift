//
//  PermissionRequestView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI

struct PermissionRequestView: View {
    @StateObject private var permissionsService = PermissionsService()
    @StateObject private var themeManager = ThemeManager.shared
    @State private var currentStep: PermissionStep = .welcome
    @State private var isRequestingPermissions = false
    let onComplete: () -> Void
    
    var body: some View {
        ZStack {
            themeManager.applyBackground().ignoresSafeArea()
            
            VStack(spacing: 30) {
                Spacer()
                
                // App Icon and Title
                VStack(spacing: 20) {
                    Image(systemName: "waveform.circle.fill")
                        .font(.system(size: 80))
                        .foregroundColor(.green)
                    
                    Text("EchoNote")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("Voice notes with smart keyword detection")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                }
                
                Spacer()
                
                // Permission content based on current step
                permissionContent
                
                Spacer()
                
                // Action buttons
                actionButtons
                
                Spacer()
            }
            .padding(.horizontal, 30)
        }
        .onAppear {
            Task {
                await permissionsService.checkAllPermissions()
                if permissionsService.allRequiredPermissionsGranted {
                    onComplete()
                }
            }
        }
    }
    
    @ViewBuilder
    private var permissionContent: some View {
        switch currentStep {
        case .welcome:
            welcomeContent
        case .microphone:
            microphonePermissionContent
        case .speechRecognition:
            speechRecognitionContent
        case .complete:
            completionContent
        }
    }
    
    private var welcomeContent: some View {
        VStack(spacing: 20) {
            Text("Welcome to EchoNote")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("To get started, we need a couple of permissions to make your voice note experience seamless.")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
        }
    }
    
    private var microphonePermissionContent: some View {
        VStack(spacing: 20) {
            Image(systemName: "headphones.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.yellow)
            
            Text("Microphone Access")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("EchoNote needs access to your microphone to record voice notes and detect keywords.")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
        }
    }
    
    private var speechRecognitionContent: some View {
        VStack(spacing: 20) {
            Image(systemName: "text.bubble.fill")
                .font(.system(size: 60))
                .foregroundColor(.green)
            
            Text("Speech Recognition")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("Enable speech recognition to automatically transcribe your voice notes and detect keywords.")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
        }
    }
    
    private var completionContent: some View {
        VStack(spacing: 20) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.green)
            
            Text("All Set!")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("You're ready to start creating voice notes with EchoNote.")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
        }
    }
    
    @ViewBuilder
    private var actionButtons: some View {
        switch currentStep {
        case .welcome:
            Button(action: {
                currentStep = .microphone
            }) {
                Text("Get Started")
                    .font(.headline)
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.green)
                    )
            }
            
        case .microphone:
            VStack(spacing: 12) {
                Button(action: {
                    requestMicrophonePermission()
                }) {
                    HStack {
                        if isRequestingPermissions {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.black)
                        }
                        Text(isRequestingPermissions ? "Requesting..." : "Allow Microphone Access")
                    }
                    .font(.headline)
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.green)
                    )
                }
                .disabled(isRequestingPermissions)
                
                Button(action: {
                    currentStep = .speechRecognition
                }) {
                    Text("Skip for now")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }
            }
            
        case .speechRecognition:
            VStack(spacing: 12) {
                Button(action: {
                    requestSpeechRecognitionPermission()
                }) {
                    HStack {
                        if isRequestingPermissions {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.black)
                        }
                        Text(isRequestingPermissions ? "Requesting..." : "Enable Speech Recognition")
                    }
                    .font(.headline)
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.green)
                    )
                }
                .disabled(isRequestingPermissions)
                
                Button(action: {
                    currentStep = .complete
                }) {
                    Text("Skip for now")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }
            }
            
        case .complete:
            Button(action: {
                onComplete()
            }) {
                Text("Start Using EchoNote")
                    .font(.headline)
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.green)
                    )
            }
        }
    }
    
    private func requestMicrophonePermission() {
        isRequestingPermissions = true
        
        Task {
            let result = await permissionsService.requestPermission(.microphone)
            
            await MainActor.run {
                isRequestingPermissions = false
                
                if result.wasGranted {
                    currentStep = .speechRecognition
                } else {
                    // Show settings prompt or continue to next step
                    currentStep = .speechRecognition
                }
            }
        }
    }
    
    private func requestSpeechRecognitionPermission() {
        isRequestingPermissions = true
        
        Task {
            let result = await permissionsService.requestPermission(.speechRecognition)
            
            await MainActor.run {
                isRequestingPermissions = false
                currentStep = .complete
            }
        }
    }
}

enum PermissionStep {
    case welcome
    case microphone
    case speechRecognition
    case complete
}

#Preview {
    PermissionRequestView(onComplete: {})
}
