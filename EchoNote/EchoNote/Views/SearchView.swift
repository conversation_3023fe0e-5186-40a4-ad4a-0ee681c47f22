//
//  SearchView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData

struct SearchView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    
    @State private var searchText = ""
    @State private var searchFilter = SearchFilter()
    @State private var showingFilters = false
    @State private var searchService: SearchService?
    @StateObject private var accessibilityService = AccessibilityService.shared
    
    @Query private var keywords: [Keyword]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search bar
                searchBar
                
                // Search content
                if let service = searchService {
                    searchContent(service: service)
                } else {
                    ProgressView("Loading...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            }
            .background(Color.black)
            .navigationTitle("Search")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    <PERSON><PERSON>("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>(action: {
                        showingFilters.toggle()
                    }) {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                            .foregroundColor(.white)
                    }
                    .accessibilityLabel("Search filters")
                    .accessibilityHint(accessibilityService.buttonHint("open search filters"))
                }
            }
            .sheet(isPresented: $showingFilters) {
                SearchFiltersView(filter: $searchFilter, keywords: keywords)
            }
        }
        .onAppear {
            if searchService == nil {
                searchService = SearchService(modelContext: modelContext)
            }
        }
    }
    
    private var searchBar: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.gray)
                
                TextField("Search notes...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                    .foregroundColor(.white)
                    .font(accessibilityService.scaledFont(.body))
                    .onSubmit {
                        performSearch()
                    }
                    .onChange(of: searchText) { oldValue, newValue in
                        // Debounce search
                        Task {
                            try? await Task.sleep(nanoseconds: 300_000_000) // 300ms
                            if searchText == newValue {
                                await searchService?.generateSuggestions(for: newValue)
                                if !newValue.isEmpty {
                                    performSearch()
                                }
                            }
                        }
                    }
                    .accessibilityLabel("Search field")
                    .accessibilityHint("Enter text to search through your notes")
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                        searchService?.searchResults = []
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.gray)
                    }
                    .accessibilityLabel("Clear search")
                    .accessibilityHint(accessibilityService.buttonHint("clear search text"))
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.2))
            )
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            // Search suggestions
            if let service = searchService, !service.searchSuggestions.isEmpty && !searchText.isEmpty {
                searchSuggestions(service: service)
            }
        }
    }
    
    private func searchSuggestions(service: SearchService) -> some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(service.searchSuggestions, id: \.self) { suggestion in
                    Button(action: {
                        searchText = suggestion
                        performSearch()
                    }) {
                        Text(suggestion)
                            .font(accessibilityService.scaledFont(.caption))
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(Color.blue.opacity(0.3))
                            )
                    }
                    .accessibilityLabel("Search suggestion: \(suggestion)")
                    .accessibilityHint(accessibilityService.buttonHint("use this search suggestion"))
                }
            }
            .padding(.horizontal, 20)
        }
    }
    
    private func searchContent(service: SearchService) -> some View {
        Group {
            if searchText.isEmpty {
                emptySearchState(service: service)
            } else if service.isSearching {
                loadingState
            } else if service.searchResults.isEmpty {
                noResultsState
            } else {
                searchResults(service: service)
            }
        }
    }
    
    private func emptySearchState(service: SearchService) -> some View {
        VStack(spacing: 20) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("Search your notes")
                .font(accessibilityService.scaledFont(.title2))
                .fontWeight(accessibilityService.fontWeight(.semibold))
                .foregroundColor(.white)
            
            Text("Enter keywords to find notes, content, or tags")
                .font(accessibilityService.scaledFont(.body))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
            
            // Recent searches
            if !service.recentSearches.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("Recent Searches")
                            .font(accessibilityService.scaledFont(.headline))
                            .fontWeight(accessibilityService.fontWeight(.semibold))
                            .foregroundColor(.white)
                        
                        Spacer()
                        
                        Button("Clear") {
                            service.clearRecentSearches()
                        }
                        .font(accessibilityService.scaledFont(.caption))
                        .foregroundColor(.blue)
                    }
                    
                    LazyVStack(spacing: 8) {
                        ForEach(service.recentSearches, id: \.self) { recentSearch in
                            Button(action: {
                                searchText = recentSearch
                                performSearch()
                            }) {
                                HStack {
                                    Image(systemName: "clock")
                                        .foregroundColor(.gray)
                                    
                                    Text(recentSearch)
                                        .font(accessibilityService.scaledFont(.body))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                    
                                    Image(systemName: "arrow.up.left")
                                        .foregroundColor(.gray)
                                        .font(.caption)
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color.gray.opacity(0.1))
                                )
                            }
                            .accessibilityLabel("Recent search: \(recentSearch)")
                            .accessibilityHint(accessibilityService.buttonHint("search for \(recentSearch)"))
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 40)
    }
    
    private var loadingState: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Searching...")
                .font(accessibilityService.scaledFont(.body))
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var noResultsState: some View {
        VStack(spacing: 20) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No results found")
                .font(accessibilityService.scaledFont(.title2))
                .fontWeight(accessibilityService.fontWeight(.semibold))
                .foregroundColor(.white)
            
            Text("Try different keywords or check your filters")
                .font(accessibilityService.scaledFont(.body))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 40)
    }
    
    private func searchResults(service: SearchService) -> some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(service.searchResults) { result in
                    SearchResultRow(result: result)
                        .onTapGesture {
                            // Navigate to note detail
                        }
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            .padding(.bottom, 100)
        }
    }
    
    private func performSearch() {
        guard let service = searchService else { return }
        
        Task {
            await service.search(query: searchText, filter: searchFilter)
        }
    }
}

// MARK: - Search Result Row

struct SearchResultRow: View {
    let result: SearchResult
    @StateObject private var accessibilityService = AccessibilityService.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Title
            Text(result.highlightedTitle.replacingOccurrences(of: "**", with: ""))
                .font(accessibilityService.scaledFont(.headline))
                .fontWeight(accessibilityService.fontWeight(.semibold))
                .foregroundColor(.white)
                .lineLimit(2)
            
            // Content preview
            if !result.note.content.isEmpty {
                Text(result.highlightedContent.replacingOccurrences(of: "**", with: ""))
                    .font(accessibilityService.scaledFont(.body))
                    .foregroundColor(.gray)
                    .lineLimit(3)
            }
            
            // Keywords
            if !result.note.keywords.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(result.note.keywords, id: \.id) { keyword in
                            Text(keyword.text)
                                .font(accessibilityService.scaledFont(.caption))
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color(hex: keyword.color) ?? .blue)
                                )
                        }
                    }
                    .padding(.horizontal, 1)
                }
            }
            
            // Metadata
            HStack {
                if result.note.isFavorite {
                    Image(systemName: "heart.fill")
                        .foregroundColor(.red)
                        .font(.caption)
                }
                
                if result.note.audioURL != nil {
                    Image(systemName: "waveform")
                        .foregroundColor(.green)
                        .font(.caption)
                }
                
                Spacer()
                
                Text(result.note.createdAt, style: .date)
                    .font(accessibilityService.scaledFont(.caption))
                    .foregroundColor(.gray)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel(accessibilityService.noteLabel(result.note))
        .accessibilityHint(accessibilityService.buttonHint("view note details"))
    }
}

// MARK: - Search Filters View

struct SearchFiltersView: View {
    @Binding var filter: SearchFilter
    let keywords: [Keyword]
    @Environment(\.dismiss) private var dismiss
    @StateObject private var accessibilityService = AccessibilityService.shared

    @State private var startDate = Date()
    @State private var endDate = Date()
    @State private var hasDateRange = false

    var body: some View {
        NavigationView {
            Form {
                Section("Date Range") {
                    Toggle("Filter by date", isOn: $hasDateRange)
                        .onChange(of: hasDateRange) { oldValue, newValue in
                            if newValue {
                                filter.dateRange = DateRange(startDate: startDate, endDate: endDate)
                            } else {
                                filter.dateRange = nil
                            }
                        }

                    if hasDateRange {
                        DatePicker("From", selection: $startDate, displayedComponents: .date)
                            .onChange(of: startDate) { oldValue, newValue in
                                filter.dateRange = DateRange(startDate: newValue, endDate: endDate)
                            }

                        DatePicker("To", selection: $endDate, displayedComponents: .date)
                            .onChange(of: endDate) { oldValue, newValue in
                                filter.dateRange = DateRange(startDate: startDate, endDate: newValue)
                            }
                    }
                }

                Section("Keywords") {
                    ForEach(keywords, id: \.id) { keyword in
                        HStack {
                            Button(action: {
                                toggleKeyword(keyword)
                            }) {
                                HStack {
                                    Image(systemName: filter.keywords.contains(where: { $0.id == keyword.id }) ? "checkmark.circle.fill" : "circle")
                                        .foregroundColor(filter.keywords.contains(where: { $0.id == keyword.id }) ? .blue : .gray)

                                    Text(keyword.text)
                                        .foregroundColor(.primary)

                                    Spacer()
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }

                Section("Options") {
                    Toggle("Favorites only", isOn: $filter.isFavoriteOnly)
                    Toggle("With audio only", isOn: $filter.hasAudioOnly)
                }

                Section("Sort by") {
                    Picker("Sort option", selection: $filter.sortBy) {
                        ForEach(SearchSortOption.allCases, id: \.self) { option in
                            Text(option.rawValue).tag(option)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
            }
            .navigationTitle("Search Filters")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Reset") {
                        resetFilters()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }

    private func toggleKeyword(_ keyword: Keyword) {
        if let index = filter.keywords.firstIndex(where: { $0.id == keyword.id }) {
            filter.keywords.remove(at: index)
        } else {
            filter.keywords.append(keyword)
        }
    }

    private func resetFilters() {
        filter = SearchFilter()
        hasDateRange = false
        startDate = Date()
        endDate = Date()
    }
}

#Preview {
    SearchView()
        .modelContainer(for: [Note.self, Keyword.self])
}
