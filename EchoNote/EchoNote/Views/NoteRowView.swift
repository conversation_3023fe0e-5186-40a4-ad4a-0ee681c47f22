//
//  NoteRowView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData

struct NoteRowView: View {
    let note: Note
    let dataManager: DataManager
    
    @State private var showingDeleteAlert = false
    
    var body: some View {
        HStack(spacing: 12) {
            // Main content area
            VStack(alignment: .leading, spacing: 8) {
                // Title row
                Text(note.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .lineLimit(1)

                // Content preview - More lines for better readability
                Text(note.content)
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .lineLimit(4)
                    .lineSpacing(2)
                    .multilineTextAlignment(.leading)

                // Bottom row with metadata
                HStack {
                    Text(formatDate(note.createdAt))
                        .font(.caption)
                        .foregroundColor(.gray)

                    Spacer()

                    // Audio and duration indicators
                    HStack(spacing: 6) {
                        if note.audioURL != nil {
                            HStack(spacing: 3) {
                                Image(systemName: "waveform")
                                    .font(.caption)
                                    .foregroundColor(.blue)

                                Text("Audio")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                            }
                        }

                        if note.duration > 0 {
                            HStack(spacing: 3) {
                                Image(systemName: "clock")
                                    .font(.caption)
                                    .foregroundColor(.orange)

                                Text(formatDuration(note.duration))
                                    .font(.caption)
                                    .foregroundColor(.orange)
                            }
                        }
                    }
                }
            }

            // Right side: Keywords and Actions
            VStack(alignment: .trailing, spacing: 8) {
                // Action buttons
                HStack(spacing: 12) {
                    Button(action: {
                        dataManager.toggleNoteFavorite(note)
                    }) {
                        Image(systemName: note.isFavorite ? "heart.fill" : "heart")
                            .font(.system(size: 16))
                            .foregroundColor(note.isFavorite ? .yellow : .gray)
                    }

                    Button(action: {
                        showingDeleteAlert = true
                    }) {
                        Image(systemName: "trash")
                            .font(.system(size: 16))
                            .foregroundColor(.red)
                    }
                }

                // Keywords tags
                VStack(alignment: .trailing, spacing: 4) {
                    ForEach(Array(note.keywords.prefix(3)), id: \.id) { keyword in
                        HStack(spacing: 3) {
                            Image(systemName: keyword.icon)
                                .font(.caption2)
                                .foregroundColor(.white)

                            Text(keyword.text)
                                .font(.caption2)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                        }
                        .padding(.horizontal, 6)
                        .padding(.vertical, 3)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color(hex: keyword.color))
                        )
                    }

                    if note.keywords.count > 3 {
                        Text("+\(note.keywords.count - 3)")
                            .font(.caption2)
                            .foregroundColor(.gray)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 3)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.gray.opacity(0.3))
                            )
                    }
                }

                Spacer()
            }
            .frame(width: 90)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
        )
        .alert("Delete Note", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                dataManager.deleteNote(note)
            }
        } message: {
            Text("Are you sure you want to delete this note? This action cannot be undone.")
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        if duration == 0 {
            return "0s"
        }

        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        let seconds = Int(duration) % 60

        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else if minutes > 0 {
            return String(format: "%d:%02d", minutes, seconds)
        } else {
            return String(format: "%ds", seconds)
        }
    }
}



#Preview {
    @Previewable @State var sampleNote = Note(title: "Sample Note", content: "This is a sample note content for preview purposes.")
    @Previewable @State var sampleDataManager = {
        let container = try! ModelContainer(for: Note.self, configurations: ModelConfiguration(isStoredInMemoryOnly: true))
        return DataManager(modelContext: container.mainContext)
    }()

    VStack {
        NoteRowView(note: sampleNote, dataManager: sampleDataManager)
    }
    .padding()
    .background(Color.black)
}
