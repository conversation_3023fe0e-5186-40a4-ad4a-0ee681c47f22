//
//  KeywordsView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData

struct KeywordsView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var keywords: [Keyword]
    @StateObject private var themeManager = ThemeManager.shared

    @State private var showingAddKeyword = false
    @State private var searchText = ""
    
    private var dataManager: DataManager {
        DataManager(modelContext: modelContext)
    }
    
    private var filteredKeywords: [Keyword] {
        if searchText.isEmpty {
            return keywords.sorted { $0.usageCount > $1.usageCount }
        } else {
            return keywords.filter { $0.text.localizedCaseInsensitiveContains(searchText) }
                .sorted { $0.usageCount > $1.usageCount }
        }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                themeManager.applyBackground().ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header
                    headerView
                    
                    // Search bar
                    searchBar
                    
                    // Keywords list
                    if filteredKeywords.isEmpty {
                        emptyStateView
                    } else {
                        keywordsList
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showingAddKeyword) {
            AddKeywordView(dataManager: dataManager)
        }
    }
    
    private var headerView: some View {
        HStack {
            Text("Keywords")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Spacer()
            
            Button(action: {
                showingAddKeyword = true
            }) {
                Image(systemName: "plus")
                    .font(.title2)
                    .foregroundColor(.green)
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.gray)
            
            TextField("Search keywords...", text: $searchText)
                .foregroundColor(.white)
                .textFieldStyle(PlainTextFieldStyle())
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.gray.opacity(0.2))
        )
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    private var keywordsList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(filteredKeywords, id: \.id) { keyword in
                    KeywordRowView(keyword: keyword, dataManager: dataManager)
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            .padding(.bottom, 100) // Space for tab bar
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Image(systemName: "tag")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Keywords Yet")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("Add keywords to categorize your voice notes automatically")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            
            Button(action: {
                showingAddKeyword = true
            }) {
                Text("Add Your First Keyword")
                    .font(.headline)
                    .foregroundColor(.black)
                    .padding(.horizontal, 30)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(Color.green)
                    )
            }
            .padding(.top, 10)
            
            Spacer()
        }
    }
}

struct KeywordRowView: View {
    let keyword: Keyword
    let dataManager: DataManager
    
    @State private var showingEditKeyword = false
    
    var body: some View {
        HStack(spacing: 15) {
            // Icon with color background
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(hex: keyword.color))
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: keyword.icon)
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.white)
                )
            
            // Keyword text
            VStack(alignment: .leading, spacing: 4) {
                Text(keyword.text)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text("Used \(keyword.usageCount) times")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            // Enabled toggle
            Toggle("", isOn: Binding(
                get: { keyword.isEnabled },
                set: { newValue in
                    dataManager.updateKeyword(keyword, isEnabled: newValue)
                }
            ))
            .toggleStyle(SwitchToggleStyle(tint: .green))
            .scaleEffect(0.8)
            
            // Edit button
            Button(action: {
                showingEditKeyword = true
            }) {
                Image(systemName: "pencil")
                    .foregroundColor(.gray)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
        )
        .sheet(isPresented: $showingEditKeyword) {
            EditKeywordView(keyword: keyword, dataManager: dataManager)
        }
    }
}

#Preview {
    KeywordsView()
        .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
