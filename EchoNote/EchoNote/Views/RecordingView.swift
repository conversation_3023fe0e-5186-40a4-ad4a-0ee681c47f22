//
//  RecordingView.swift
//  EchoNote
//
//  Created by <PERSON> on 30/6/2025.
//

import SwiftUI
import SwiftData
import AVFoundation

struct RecordingView: View {
    @Binding var isPresented: Bool
    @StateObject private var viewModel = RecordingViewModel()
    @StateObject private var themeManager = ThemeManager.shared
    
    var body: some View {
        ZStack {
            themeManager.applyBackground().ignoresSafeArea()
            
            VStack(spacing: 40) {
                // Header with cancel button
                HStack {
                    Button("Cancel") {
                        Task {
                            await viewModel.cancelRecording()
                            isPresented = false
                        }
                    }
                    .foregroundColor(.white)
                    
                    Spacer()
                }
                .padding(.top, 20)
                
                Spacer()
                
                // Recording state display
                VStack(spacing: 30) {
                    // State indicator
                    stateIndicatorView
                    
                    // Waveform visualization
                    WaveformView(
                        data: WaveformData(
                            levels: viewModel.audioLevels,
                            currentTime: viewModel.recordingDuration,
                            duration: viewModel.recordingDuration,
                            isRecording: viewModel.isRecording
                        ),
                        configuration: WaveformConfiguration(
                            style: .gradient,
                            primaryColor: .green,
                            secondaryColor: .yellow,
                            animationDuration: 0.1
                        )
                    )
                    .frame(height: 100)

                    // Recording duration
                    if viewModel.isRecording {
                        VStack(spacing: 4) {
                            HStack(spacing: 8) {
                                Image(systemName: "clock")
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.8))
                                Text(viewModel.formattedDuration)
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                                    .monospacedDigit()
                            }

                            // Progress bar (visual representation)
                            ProgressView(value: min(viewModel.recordingDuration, 300), total: 300)
                                .progressViewStyle(LinearProgressViewStyle(tint: .green))
                                .frame(width: 120)
                                .scaleEffect(y: 2)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.black.opacity(0.3))
                        )
                    }

                    // Live transcription
                    if !viewModel.transcribedText.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: "text.bubble")
                                    .font(.caption)
                                    .foregroundColor(.green)
                                Text("Live Transcription")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.white.opacity(0.8))
                                Spacer()
                                // Typing indicator
                                if viewModel.isTranscribing {
                                    HStack(spacing: 2) {
                                        ForEach(0..<3, id: \.self) { index in
                                            Circle()
                                                .fill(Color.green)
                                                .frame(width: 4, height: 4)
                                                .scaleEffect(1.0)
                                                .animation(
                                                    .easeInOut(duration: 0.6)
                                                    .repeatForever(autoreverses: true)
                                                    .delay(Double(index) * 0.2),
                                                    value: viewModel.isTranscribing
                                                )
                                        }
                                    }
                                }
                            }

                            ScrollView {
                                Text(viewModel.transcribedText)
                                    .font(.body)
                                    .foregroundColor(.white)
                                    .multilineTextAlignment(.leading)
                                    .padding()
                                    .frame(maxWidth: .infinity, alignment: .leading)
                            }
                            .frame(maxHeight: 120)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.black.opacity(0.4))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color.green.opacity(0.3), lineWidth: 1)
                                )
                        )
                        .transition(.opacity.combined(with: .scale(scale: 0.95)))
                        .animation(.easeInOut(duration: 0.3), value: !viewModel.transcribedText.isEmpty)
                    }
                }
                
                Spacer()
                
                // Control buttons
                controlButtonsView
                
                Spacer()
            }
            .padding(.horizontal, 30)
        }
    }
    
    @ViewBuilder
    private var stateIndicatorView: some View {
        switch viewModel.currentState {
        case .idle:
            VStack(spacing: 8) {
                Image(systemName: "mic.slash")
                    .font(.system(size: 48))
                    .foregroundColor(.gray)
                Text("Ready to record")
                    .font(.headline)
                    .foregroundColor(.white)
            }
            
        case .listening(let keywords):
            VStack(spacing: 12) {
                ZStack {
                    // Animated listening waves
                    ForEach(0..<3, id: \.self) { index in
                        Circle()
                            .stroke(Color.blue.opacity(0.3), lineWidth: 2)
                            .frame(width: 60 + CGFloat(index * 20), height: 60 + CGFloat(index * 20))
                            .scaleEffect(1.0)
                            .animation(
                                .easeInOut(duration: 2.0)
                                .repeatForever(autoreverses: false)
                                .delay(Double(index) * 0.3),
                                value: true
                            )
                    }

                    // Ear icon
                    Image(systemName: "ear")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.blue)
                }

                Text("Listening for keywords...")
                    .font(.title3)
                    .fontWeight(.medium)
                    .foregroundColor(.white)

                if !keywords.isEmpty {
                    HStack {
                        ForEach(keywords, id: \.self) { keyword in
                            Text(keyword)
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color.blue.opacity(0.3))
                                )
                                .foregroundColor(.white)
                        }
                    }
                }
            }
            
        case .recording:
            VStack(spacing: 12) {
                ZStack {
                    // Pulsing background circle
                    Circle()
                        .fill(Color.red.opacity(0.3))
                        .frame(width: 80, height: 80)
                        .scaleEffect(1.2)
                        .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: viewModel.isRecording)

                    // Microphone icon
                    Image(systemName: "mic.fill")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.red)
                        .scaleEffect(1.1)
                        .animation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true), value: viewModel.isRecording)
                }

                Text("Recording...")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                // Recording indicator dots
                HStack(spacing: 4) {
                    ForEach(0..<3, id: \.self) { index in
                        Circle()
                            .fill(Color.red)
                            .frame(width: 6, height: 6)
                            .scaleEffect(1.0)
                            .animation(
                                .easeInOut(duration: 0.6)
                                .repeatForever(autoreverses: true)
                                .delay(Double(index) * 0.2),
                                value: viewModel.isRecording
                            )
                    }
                }
            }
            
        case .processing:
            VStack(spacing: 8) {
                ProgressView()
                    .scaleEffect(1.5)
                    .tint(.white)
                Text("Processing...")
                    .font(.headline)
                    .foregroundColor(.white)
            }
            
        case .error(let message):
            VStack(spacing: 8) {
                Image(systemName: "exclamationmark.triangle")
                    .font(.system(size: 48))
                    .foregroundColor(.red)
                Text("Error")
                    .font(.headline)
                    .foregroundColor(.white)
                Text(message)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    @ViewBuilder
    private var controlButtonsView: some View {
        HStack(spacing: 30) {
            // Pause/Resume button (if recording or paused)
            if viewModel.isRecording || viewModel.isPaused {
                Button(action: {
                    Task {
                        if viewModel.isPaused {
                            await viewModel.resumeRecording()
                        } else {
                            await viewModel.pauseRecording()
                        }
                    }
                }) {
                    Circle()
                        .fill(viewModel.isPaused ? Color.green : Color.orange)
                        .frame(width: 60, height: 60)
                        .overlay(
                            Group {
                                if viewModel.isPaused {
                                    // Play icon for resume
                                    Image(systemName: "play.fill")
                                        .font(.system(size: 20, weight: .bold))
                                        .foregroundColor(.white)
                                } else {
                                    // Pause icon
                                    HStack(spacing: 4) {
                                        Rectangle()
                                            .fill(Color.white)
                                            .frame(width: 6, height: 20)
                                        Rectangle()
                                            .fill(Color.white)
                                            .frame(width: 6, height: 20)
                                    }
                                }
                            }
                        )
                        .shadow(color: (viewModel.isPaused ? Color.green : Color.orange).opacity(0.3), radius: 8, x: 0, y: 4)
                }
                .accessibilityLabel(viewModel.isPaused ? "Resume recording" : "Pause recording")
            }

            // Stop button
            Button(action: {
                Task {
                    await viewModel.stopRecording()
                    isPresented = false
                }
            }) {
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [Color.red, Color.red.opacity(0.8)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                    .overlay(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color.white)
                            .frame(width: 24, height: 24)
                    )
                    .shadow(color: .red.opacity(0.4), radius: 10, x: 0, y: 5)
            }
            .disabled(!viewModel.canStop)
            .scaleEffect(viewModel.canStop ? 1.0 : 0.8)
            .opacity(viewModel.canStop ? 1.0 : 0.6)
            .animation(.easeInOut(duration: 0.2), value: viewModel.canStop)
            .accessibilityLabel("Stop recording")
        }
    }
    
        .onAppear {
            viewModel.handleViewAppear()
        }
        .onDisappear {
            viewModel.handleViewDisappear()
        }
}

#Preview {
    RecordingView(isPresented: .constant(true))
        .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
