import SwiftUI

enum ShareOption {
    case systemShare
    case textOnly
    case audioOnly
    case copyToClipboard
    case exportAsFile
}

struct ShareOptionsView: View {
    let note: Note
    let onShareOptionSelected: (ShareOption) -> Void
    @Environment(\.dismiss) private var dismiss
    @StateObject private var themeManager = ThemeManager.shared
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                HStack {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(.gray)
                    
                    Spacer()
                    
                    Text("Share Note")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    // Invisible button for balance
                    Button("") { }
                        .opacity(0)
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)
                .padding(.bottom, 20)
                
                // Share options
                ScrollView {
                    VStack(spacing: 16) {
                        // System Share (AirDrop, Messages, Mail, etc.)
                        ShareOptionRow(
                            icon: "square.and.arrow.up",
                            title: "Share via System",
                            subtitle: "AirDrop, Messages, Mail, and more",
                            color: .blue
                        ) {
                            onShareOptionSelected(.systemShare)
                            dismiss()
                        }
                        
                        Divider()
                            .background(Color.gray.opacity(0.3))
                        
                        // Text Only
                        ShareOptionRow(
                            icon: "doc.text",
                            title: "Share Text Only",
                            subtitle: "Share note content without audio",
                            color: .green
                        ) {
                            onShareOptionSelected(.textOnly)
                            dismiss()
                        }
                        
                        // Audio Only (if available)
                        if note.audioURL != nil {
                            ShareOptionRow(
                                icon: "waveform",
                                title: "Share Audio Only",
                                subtitle: "Share just the audio recording",
                                color: .orange
                            ) {
                                onShareOptionSelected(.audioOnly)
                                dismiss()
                            }
                        }
                        
                        Divider()
                            .background(Color.gray.opacity(0.3))
                        
                        // Copy to Clipboard
                        ShareOptionRow(
                            icon: "doc.on.clipboard",
                            title: "Copy to Clipboard",
                            subtitle: "Copy note text to clipboard",
                            color: .purple
                        ) {
                            onShareOptionSelected(.copyToClipboard)
                            dismiss()
                        }
                        
                        // Export as File
                        ShareOptionRow(
                            icon: "square.and.arrow.down",
                            title: "Export as Text File",
                            subtitle: "Save as .txt file",
                            color: .indigo
                        ) {
                            onShareOptionSelected(.exportAsFile)
                            dismiss()
                        }
                        
                        Spacer(minLength: 50)
                    }
                    .padding(.horizontal, 20)
                }
            }
            .background(themeManager.applyBackground())
        }
        .navigationBarHidden(true)
    }
}

struct ShareOptionRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Icon
                Image(systemName: icon)
                    .font(.system(size: 24))
                    .foregroundColor(color)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(color.opacity(0.2))
                    )
                
                // Text content
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)
                    
                    Text(subtitle)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // Chevron
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(.gray)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    let sampleNote = Note(title: "Sample Note", content: "This is a sample note content for preview purposes.")
    
    ShareOptionsView(note: sampleNote) { option in
        print("Selected option: \(option)")
    }
}
