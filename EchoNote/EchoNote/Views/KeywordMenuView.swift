import SwiftUI

struct KeywordMenuView: View {
    let keywords: [Keyword]
    @Binding var selectedKeyword: Keyword?
    let totalNotesCount: Int
    let notesCount: (Keyword) -> Int
    
    @Environment(\.dismiss) private var dismiss
    @StateObject private var themeManager = ThemeManager.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // All Notes option
            Button(action: {
                selectedKeyword = nil
                dismiss()
            }) {
                HStack {
                    Image(systemName: "doc.text")
                        .foregroundColor(.primary)
                        .frame(width: 20)
                    
                    Text("All Notes (\(totalNotesCount))")
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    if selectedKeyword == nil {
                        Image(systemName: "checkmark")
                            .foregroundColor(.blue)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
            }
            .buttonStyle(PlainButtonStyle())
            
            if !keywords.isEmpty {
                Divider()
                    .padding(.horizontal, 16)
                
                // Keyword options
                ForEach(keywords, id: \.id) { keyword in
                    Button(action: {
                        selectedKeyword = keyword
                        dismiss()
                    }) {
                        HStack {
                            Image(systemName: keyword.icon)
                                .foregroundColor(Color(hex: keyword.color))
                                .frame(width: 20)
                            
                            Text("\(keyword.text) (\(notesCount(keyword)))")
                                .foregroundColor(Color(hex: keyword.color))
                            
                            Spacer()
                            
                            if selectedKeyword?.id == keyword.id {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.blue)
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .background(Color(.systemBackground))
        .frame(minWidth: 200, maxWidth: 300)
        .cornerRadius(12)
        .shadow(radius: 8)
    }
}

#Preview {
    KeywordMenuView(
        keywords: [],
        selectedKeyword: .constant(nil),
        totalNotesCount: 5,
        notesCount: { _ in 2 }
    )
}
