//
//  WaveformView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import AVFoundation

// MARK: - Waveform Style

enum WaveformStyle {
    case bars
    case line
    case filled
    case gradient
}

// MARK: - Waveform Configuration

struct WaveformConfiguration {
    var style: WaveformStyle = .bars
    var barWidth: CGFloat = 4
    var barSpacing: CGFloat = 2
    var cornerRadius: CGFloat = 2
    var primaryColor: Color = .green
    var secondaryColor: Color = .gray
    var backgroundColor: Color = .clear
    var animationDuration: Double = 0.2
    var maxBars: Int = 50
    var amplitudeScale: CGFloat = 1.0
    var showBaseline: Bool = false
    var baselineColor: Color = .gray
    var gradientColors: [Color] = [.green, .yellow, .red]
}

// MARK: - Waveform Data

struct WaveformData {
    var levels: [Float]
    var currentTime: TimeInterval
    var duration: TimeInterval
    var isRecording: Bool
    
    init(levels: [Float] = [], currentTime: TimeInterval = 0, duration: TimeInterval = 0, isRecording: Bool = false) {
        self.levels = levels
        self.currentTime = currentTime
        self.duration = duration
        self.isRecording = isRecording
    }
    
    var normalizedLevels: [Float] {
        guard !levels.isEmpty else { return [] }
        let maxLevel = levels.max() ?? 1.0
        return levels.map { maxLevel > 0 ? $0 / maxLevel : 0 }
    }
    
    var progress: Double {
        guard duration > 0 else { return 0 }
        return currentTime / duration
    }
}

// MARK: - Waveform View

struct WaveformView: View {
    let data: WaveformData
    let configuration: WaveformConfiguration
    @StateObject private var accessibilityService = AccessibilityService.shared
    
    init(data: WaveformData, configuration: WaveformConfiguration = WaveformConfiguration()) {
        self.data = data
        self.configuration = configuration
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background
                configuration.backgroundColor
                
                // Baseline
                if configuration.showBaseline {
                    Rectangle()
                        .fill(configuration.baselineColor)
                        .frame(height: 1)
                        .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
                
                // Waveform content
                waveformContent(in: geometry)
                
                // Progress indicator for playback
                if !data.isRecording && data.duration > 0 {
                    progressIndicator(in: geometry)
                }
            }
        }
        .accessibilityElement(children: .ignore)
        .accessibilityLabel("Audio waveform")
        .accessibilityValue(accessibilityDescription)
        .accessibilityAddTraits(.allowsDirectInteraction)
    }
    
    @ViewBuilder
    private func waveformContent(in geometry: GeometryProxy) -> some View {
        switch configuration.style {
        case .bars:
            barsWaveform(in: geometry)
        case .line:
            lineWaveform(in: geometry)
        case .filled:
            filledWaveform(in: geometry)
        case .gradient:
            gradientWaveform(in: geometry)
        }
    }
    
    private func barsWaveform(in geometry: GeometryProxy) -> some View {
        HStack(spacing: configuration.barSpacing) {
            ForEach(0..<min(configuration.maxBars, data.normalizedLevels.count), id: \.self) { index in
                let level = data.normalizedLevels[index]
                let height = max(2, CGFloat(level) * geometry.size.height * configuration.amplitudeScale)
                let color = barColor(for: level, at: index)
                
                RoundedRectangle(cornerRadius: configuration.cornerRadius)
                    .fill(color)
                    .frame(width: configuration.barWidth, height: height)
                    .animation(
                        accessibilityService.springAnimation(
                            response: configuration.animationDuration,
                            dampingFraction: 0.8
                        ),
                        value: level
                    )
            }
            
            // Fill remaining space with empty bars if needed
            if data.normalizedLevels.count < configuration.maxBars {
                ForEach(data.normalizedLevels.count..<configuration.maxBars, id: \.self) { _ in
                    RoundedRectangle(cornerRadius: configuration.cornerRadius)
                        .fill(configuration.secondaryColor.opacity(0.3))
                        .frame(width: configuration.barWidth, height: 2)
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private func lineWaveform(in geometry: GeometryProxy) -> some View {
        Path { path in
            guard !data.normalizedLevels.isEmpty else { return }
            
            let width = geometry.size.width
            let height = geometry.size.height
            let centerY = height / 2
            let stepX = width / CGFloat(data.normalizedLevels.count - 1)
            
            path.move(to: CGPoint(x: 0, y: centerY))
            
            for (index, level) in data.normalizedLevels.enumerated() {
                let x = CGFloat(index) * stepX
                let y = centerY - (CGFloat(level) * height * configuration.amplitudeScale / 2)
                path.addLine(to: CGPoint(x: x, y: y))
            }
        }
        .stroke(configuration.primaryColor, lineWidth: 2)
        .animation(
            accessibilityService.springAnimation(
                response: configuration.animationDuration,
                dampingFraction: 0.8
            ),
            value: data.normalizedLevels
        )
    }
    
    private func filledWaveform(in geometry: GeometryProxy) -> some View {
        Path { path in
            guard !data.normalizedLevels.isEmpty else { return }
            
            let width = geometry.size.width
            let height = geometry.size.height
            let centerY = height / 2
            let stepX = width / CGFloat(data.normalizedLevels.count - 1)
            
            // Top path
            path.move(to: CGPoint(x: 0, y: centerY))
            for (index, level) in data.normalizedLevels.enumerated() {
                let x = CGFloat(index) * stepX
                let y = centerY - (CGFloat(level) * height * configuration.amplitudeScale / 2)
                path.addLine(to: CGPoint(x: x, y: y))
            }
            
            // Bottom path (mirrored)
            for (index, level) in data.normalizedLevels.enumerated().reversed() {
                let x = CGFloat(index) * stepX
                let y = centerY + (CGFloat(level) * height * configuration.amplitudeScale / 2)
                path.addLine(to: CGPoint(x: x, y: y))
            }
            
            path.closeSubpath()
        }
        .fill(configuration.primaryColor.opacity(0.6))
        .animation(
            accessibilityService.springAnimation(
                response: configuration.animationDuration,
                dampingFraction: 0.8
            ),
            value: data.normalizedLevels
        )
    }
    
    private func gradientWaveform(in geometry: GeometryProxy) -> some View {
        HStack(spacing: configuration.barSpacing) {
            ForEach(0..<min(configuration.maxBars, data.normalizedLevels.count), id: \.self) { index in
                let level = data.normalizedLevels[index]
                let height = max(2, CGFloat(level) * geometry.size.height * configuration.amplitudeScale)
                let gradient = gradientColor(for: level)
                
                RoundedRectangle(cornerRadius: configuration.cornerRadius)
                    .fill(gradient)
                    .frame(width: configuration.barWidth, height: height)
                    .animation(
                        accessibilityService.springAnimation(
                            response: configuration.animationDuration,
                            dampingFraction: 0.8
                        ),
                        value: level
                    )
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private func progressIndicator(in geometry: GeometryProxy) -> some View {
        Rectangle()
            .fill(Color.white.opacity(0.8))
            .frame(width: 2, height: geometry.size.height)
            .position(
                x: CGFloat(data.progress) * geometry.size.width,
                y: geometry.size.height / 2
            )
            .animation(.linear(duration: 0.1), value: data.progress)
    }
    
    // MARK: - Color Helpers
    
    private func barColor(for level: Float, at index: Int) -> Color {
        if data.isRecording {
            // Use intensity-based coloring for recording
            if level > 0.8 {
                return .red
            } else if level > 0.5 {
                return .orange
            } else if level > 0.2 {
                return configuration.primaryColor
            } else {
                return configuration.secondaryColor
            }
        } else {
            // Use progress-based coloring for playback
            let progressIndex = Int(data.progress * Double(configuration.maxBars))
            return index <= progressIndex ? configuration.primaryColor : configuration.secondaryColor.opacity(0.5)
        }
    }
    
    private func gradientColor(for level: Float) -> LinearGradient {
        let colors = configuration.gradientColors
        let stops = colors.enumerated().map { index, color in
            Gradient.Stop(color: color, location: Double(index) / Double(colors.count - 1))
        }
        
        return LinearGradient(
            gradient: Gradient(stops: stops),
            startPoint: .bottom,
            endPoint: .top
        )
    }
    
    // MARK: - Accessibility
    
    private var accessibilityDescription: String {
        if data.isRecording {
            let averageLevel = data.normalizedLevels.isEmpty ? 0 : data.normalizedLevels.reduce(0, +) / Float(data.normalizedLevels.count)
            return accessibilityService.audioLevelLabel(averageLevel)
        } else {
            let progressPercent = Int(data.progress * 100)
            return "Playback progress: \(progressPercent) percent"
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        // Recording waveform
        WaveformView(
            data: WaveformData(
                levels: Array(0..<50).map { _ in Float.random(in: 0...1) },
                isRecording: true
            ),
            configuration: WaveformConfiguration(style: .bars)
        )
        .frame(height: 100)
        
        // Playback waveform
        WaveformView(
            data: WaveformData(
                levels: Array(0..<50).map { _ in Float.random(in: 0...1) },
                currentTime: 30,
                duration: 60,
                isRecording: false
            ),
            configuration: WaveformConfiguration(style: .filled, primaryColor: .blue)
        )
        .frame(height: 80)
    }
    .padding()
    .background(Color.black)
}
