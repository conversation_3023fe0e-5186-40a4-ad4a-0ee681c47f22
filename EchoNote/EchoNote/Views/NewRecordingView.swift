//
//  NewRecordingView.swift
//  EchoNote
//
//  Created by <PERSON> on 30/6/2025.
//

import SwiftUI
import SwiftData
import AVFoundation

struct NewRecordingView: View {
    @Binding var isPresented: Bool
    @Environment(\.serviceContainer) private var serviceContainer
    @StateObject private var themeManager = ThemeManager.shared
    
    private var recordingCoordinator: RecordingCoordinator {
        serviceContainer.getRecordingCoordinator()
    }
    
    private var voiceRecordingService: VoiceRecordingServiceProtocol {
        serviceContainer.getVoiceRecordingService()
    }
    
    var body: some View {
        ZStack {
            themeManager.applyBackground().ignoresSafeArea()
            
            VStack(spacing: 40) {
                // Header with cancel button
                HStack {
                    Button("Cancel") {
                        Task {
                            await recordingCoordinator.cancelRecording()
                            isPresented = false
                        }
                    }
                    .foregroundColor(.white)
                    
                    Spacer()
                }
                .padding(.top, 20)
                
                Spacer()
                
                // Recording state display
                VStack(spacing: 30) {
                    // State indicator
                    stateIndicatorView
                    
                    // Waveform visualization
                    WaveformView(audioLevels: voiceRecordingService.audioLevels)
                        .frame(height: 100)
                    
                    // Recording duration
                    if case .recording = recordingCoordinator.currentState {
                        Text(formatDuration(voiceRecordingService.recordingDuration))
                            .font(.title2)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                    }
                    
                    // Live transcription
                    if !voiceRecordingService.transcribedText.isEmpty {
                        ScrollView {
                            Text(voiceRecordingService.transcribedText)
                                .font(.body)
                                .foregroundColor(.white.opacity(0.9))
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.black.opacity(0.3))
                                )
                        }
                        .frame(maxHeight: 150)
                    }
                }
                
                Spacer()
                
                // Control buttons
                controlButtonsView
                
                Spacer()
            }
            .padding(.horizontal, 30)
        }
    }
    
    @ViewBuilder
    private var stateIndicatorView: some View {
        switch recordingCoordinator.currentState {
        case .idle:
            VStack(spacing: 8) {
                Image(systemName: "mic.slash")
                    .font(.system(size: 48))
                    .foregroundColor(.gray)
                Text("Ready to record")
                    .font(.headline)
                    .foregroundColor(.white)
            }
            
        case .listening(let keywords):
            VStack(spacing: 8) {
                Image(systemName: "ear")
                    .font(.system(size: 48))
                    .foregroundColor(.blue)
                Text("Listening for keywords...")
                    .font(.headline)
                    .foregroundColor(.white)
                Text(keywords.joined(separator: ", "))
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
            
        case .recording:
            VStack(spacing: 8) {
                Image(systemName: "mic.fill")
                    .font(.system(size: 48))
                    .foregroundColor(.red)
                    .scaleEffect(1.1)
                    .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: true)
                Text("Recording...")
                    .font(.headline)
                    .foregroundColor(.white)
            }
            
        case .processing:
            VStack(spacing: 8) {
                ProgressView()
                    .scaleEffect(1.5)
                    .tint(.white)
                Text("Processing...")
                    .font(.headline)
                    .foregroundColor(.white)
            }
            
        case .error(let message):
            VStack(spacing: 8) {
                Image(systemName: "exclamationmark.triangle")
                    .font(.system(size: 48))
                    .foregroundColor(.red)
                Text("Error")
                    .font(.headline)
                    .foregroundColor(.white)
                Text(message)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    @ViewBuilder
    private var controlButtonsView: some View {
        HStack(spacing: 40) {
            // Stop button
            Button(action: {
                Task {
                    await recordingCoordinator.stopRecording()
                    isPresented = false
                }
            }) {
                Circle()
                    .fill(Color.red)
                    .frame(width: 80, height: 80)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.white)
                            .frame(width: 30, height: 30)
                    )
            }
            .disabled(!recordingCoordinator.currentState.isActive)
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

#Preview {
    NewRecordingView(isPresented: .constant(true))
        .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
