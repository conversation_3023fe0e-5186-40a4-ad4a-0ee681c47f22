import SwiftUI
import SwiftData

struct EditKeywordsView: View {
    let note: Note
    let dataManager: DataManager
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \Keyword.text) private var allKeywords: [Keyword]
    @StateObject private var themeManager = ThemeManager.shared
    
    @State private var selectedKeywords: Set<UUID> = []
    @State private var searchText = ""
    @State private var showingAddKeyword = false
    
    var availableKeywords: [Keyword] {
        allKeywords.filter { $0.isEnabled }
    }
    
    var filteredKeywords: [Keyword] {
        if searchText.isEmpty {
            return availableKeywords
        } else {
            return availableKeywords.filter { 
                $0.text.localizedCaseInsensitiveContains(searchText) 
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                themeManager.applyBackground().ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header
                    headerView
                    
                    // Search bar
                    searchBarView
                    
                    // Keywords list
                    keywordsListView
                    
                    Spacer()
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            // Initialize selected keywords
            selectedKeywords = Set(note.keywords.map { $0.id })
        }
        .sheet(isPresented: $showingAddKeyword) {
            AddKeywordView(dataManager: dataManager)
        }
    }
    
    private var headerView: some View {
        HStack {
            Button("Cancel") {
                dismiss()
            }
            .foregroundColor(.gray)
            
            Spacer()
            
            Text("Edit Keywords")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Spacer()
            
            Button("Save") {
                saveKeywords()
            }
            .foregroundColor(.blue)
            .fontWeight(.semibold)
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
        .padding(.bottom, 20)
    }
    
    private var searchBarView: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.gray)
            
            TextField("Search keywords...", text: $searchText)
                .foregroundColor(.white)
            
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.gray.opacity(0.2))
        )
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
    }
    
    private var keywordsListView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                // Add new keyword option
                Button(action: {
                    showingAddKeyword = true
                }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                        
                        Text("Add New Keyword")
                            .font(.body)
                            .foregroundColor(.blue)
                        
                        Spacer()
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.blue.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                            )
                    )
                }
                .buttonStyle(PlainButtonStyle())
                
                // Existing keywords
                ForEach(filteredKeywords, id: \.id) { keyword in
                    SelectableKeywordRowView(
                        keyword: keyword,
                        isSelected: selectedKeywords.contains(keyword.id),
                        onToggle: {
                            toggleKeyword(keyword)
                        }
                    )
                }
                
                if filteredKeywords.isEmpty && !searchText.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 40))
                            .foregroundColor(.gray)
                        
                        Text("No keywords found")
                            .font(.headline)
                            .foregroundColor(.gray)
                        
                        Text("Try a different search term or add a new keyword")
                            .font(.body)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 40)
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 100)
        }
    }
    
    private func toggleKeyword(_ keyword: Keyword) {
        if selectedKeywords.contains(keyword.id) {
            selectedKeywords.remove(keyword.id)
        } else {
            selectedKeywords.insert(keyword.id)
        }
    }
    
    private func saveKeywords() {
        // Get the selected keyword objects
        let newKeywords = availableKeywords.filter { selectedKeywords.contains($0.id) }
        
        // Update the note's keywords
        dataManager.updateNoteKeywords(note, keywords: newKeywords)
        
        dismiss()
    }
}

struct SelectableKeywordRowView: View {
    let keyword: Keyword
    let isSelected: Bool
    let onToggle: () -> Void
    
    var body: some View {
        Button(action: onToggle) {
            HStack(spacing: 16) {
                // Keyword icon and color
                Image(systemName: keyword.icon)
                    .font(.title2)
                    .foregroundColor(Color(hex: keyword.color))
                    .frame(width: 30)
                
                // Keyword text
                VStack(alignment: .leading, spacing: 4) {
                    Text(keyword.text)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.white)

                    Text("Used \(keyword.usageCount) times")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                Spacer()
                
                // Selection indicator
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.title2)
                    .foregroundColor(isSelected ? .blue : .gray)
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(isSelected ? 0.2 : 0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.blue.opacity(0.5) : Color.gray.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    @Previewable @State var sampleNote = Note(title: "Sample Note", content: "Sample content")

    EditKeywordsView(note: sampleNote, dataManager: DataManager(modelContext: ModelContext(try! ModelContainer(for: Note.self, Keyword.self))))
        .modelContainer(for: [Note.self, Keyword.self], inMemory: true)
}
