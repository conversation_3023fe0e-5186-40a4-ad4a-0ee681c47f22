//
//  IconSelector.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI

// MARK: - Icon Categories

enum IconCategory: String, CaseIterable, Identifiable {
    case general = "General"
    case work = "Work"
    case lifestyle = "Lifestyle"
    case travel = "Travel"
    case food = "Food"
    case entertainment = "Entertainment"
    case health = "Health"
    case education = "Education"
    
    var id: String { rawValue }
    
    var icons: [String] {
        switch self {
        case .general:
            return [
                "lightbulb.fill", "star.fill", "heart.fill", "flag.fill",
                "bookmark.fill", "tag.fill", "bell.fill", "exclamationmark.triangle.fill",
                "checkmark.circle.fill", "xmark.circle.fill", "plus.circle.fill", "minus.circle.fill",
                "questionmark.circle.fill", "info.circle.fill", "gear.fill", "house.fill"
            ]
        case .work:
            return [
                "briefcase.fill", "folder.fill", "doc.text.fill", "calendar",
                "clock.fill", "chart.bar.fill", "chart.pie.fill", "target",
                "paperclip", "envelope.fill", "phone.fill", "video.fill",
                "person.2.fill", "building.2.fill", "laptopcomputer", "desktopcomputer"
            ]
        case .lifestyle:
            return [
                "house.fill", "bed.double.fill", "sofa.fill", "lamp.table.fill",
                "shower.fill", "washer.fill", "refrigerator.fill", "oven.fill",
                "cup.and.saucer.fill", "wineglass.fill", "gift.fill", "balloon.fill",
                "party.popper.fill", "gamecontroller.fill", "tv.fill", "speaker.wave.3.fill"
            ]
        case .travel:
            return [
                "airplane", "car.fill", "bus.fill", "train.side.front.car",
                "bicycle", "scooter", "ferry.fill", "sailboat.fill",
                "location.fill", "map.fill", "compass.drawing", "globe",
                "camera.fill", "backpack.fill", "suitcase.fill", "tent.fill"
            ]
        case .food:
            return [
                "fork.knife", "cup.and.saucer.fill", "wineglass.fill", "birthday.cake",
                "carrot.fill", "leaf.fill", "fish.fill", "takeoutbag.and.cup.and.straw.fill",
                "pizza.fill", "hamburger.fill", "popcorn.fill", "pretzel.fill",
                "ice.cream.fill", "cookie.fill", "apple.logo", "banana.fill"
            ]
        case .entertainment:
            return [
                "tv.fill", "music.note", "headphones", "speaker.wave.3.fill",
                "gamecontroller.fill", "dice.fill", "puzzlepiece.fill", "paintbrush.fill",
                "camera.fill", "video.fill", "film.fill", "photo.fill",
                "book.fill", "magazine.fill", "newspaper.fill", "theatermasks.fill"
            ]
        case .health:
            return [
                "heart.fill", "cross.fill", "pills.fill", "syringe.fill",
                "thermometer", "stethoscope", "bandage.fill", "medical.thermometer.fill",
                "figure.walk", "figure.run", "dumbbell.fill", "bicycle",
                "leaf.fill", "drop.fill", "lungs.fill", "brain.head.profile"
            ]
        case .education:
            return [
                "book.fill", "graduationcap.fill", "pencil", "highlighter",
                "ruler.fill", "scissors", "paperclip", "stapler.fill",
                "backpack.fill", "studentdesk", "chalkboard.2", "globe",
                "atom", "flask.fill", "testtube.2", "microscope.fill"
            ]
        }
    }
}

// MARK: - Icon Selector View

struct IconSelector: View {
    @Binding var selectedIcon: String
    @Binding var selectedColor: String
    @Environment(\.dismiss) private var dismiss
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var accessibilityService = AccessibilityService.shared
    
    @State private var selectedCategory: IconCategory = .general
    @State private var searchText = ""
    
    private let availableColors = [
        "#FF3B30", "#FF9500", "#FFCC00", "#34C759", "#5AC8FA",
        "#007AFF", "#5856D6", "#AF52DE", "#FF2D92", "#8E8E93",
        "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
        "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9"
    ]
    
    var filteredIcons: [String] {
        let categoryIcons = selectedCategory.icons
        if searchText.isEmpty {
            return categoryIcons
        } else {
            return categoryIcons.filter { icon in
                icon.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                themeManager.applyBackground().ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header
                    headerView
                    
                    // Preview Section
                    previewSection
                    
                    // Search Bar
                    searchBar
                    
                    // Category Tabs
                    categoryTabs
                    
                    // Icon Grid
                    iconGrid
                    
                    // Color Selection
                    colorSelection
                }
            }
        }
        .navigationBarHidden(true)
    }
    
    // MARK: - View Components
    
    private var headerView: some View {
        HStack {
            Button("Cancel") {
                dismiss()
            }
            .foregroundColor(themeManager.textSecondary)
            
            Spacer()
            
            Text("Choose Icon")
                .font(accessibilityService.scaledFont(.headline))
                .fontWeight(accessibilityService.fontWeight(.semibold))
                .foregroundColor(themeManager.textPrimary)
            
            Spacer()
            
            Button("Done") {
                dismiss()
            }
            .foregroundColor(themeManager.accentColor)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    private var previewSection: some View {
        VStack(spacing: 16) {
            // Preview Card
            HStack(spacing: 16) {
                // Icon Preview
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(hex: selectedColor))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Image(systemName: selectedIcon)
                            .font(.system(size: 28, weight: .medium))
                            .foregroundColor(.white)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Preview")
                        .font(accessibilityService.scaledFont(.headline))
                        .fontWeight(accessibilityService.fontWeight(.semibold))
                        .foregroundColor(themeManager.textPrimary)
                    
                    Text("How your keyword will look")
                        .font(accessibilityService.scaledFont(.subheadline))
                        .foregroundColor(themeManager.textSecondary)
                }
                
                Spacer()
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(themeManager.surfaceColor.opacity(0.5))
            )
            
            Divider()
                .background(themeManager.borderColor)
        }
        .padding(.horizontal, 20)
    }
    
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(themeManager.textSecondary)
            
            TextField("Search icons...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
                .foregroundColor(themeManager.textPrimary)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(themeManager.surfaceColor.opacity(0.5))
        )
        .padding(.horizontal, 20)
        .padding(.bottom, 16)
    }
    
    private var categoryTabs: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(IconCategory.allCases) { category in
                    Button(action: {
                        selectedCategory = category
                        searchText = ""
                    }) {
                        Text(category.rawValue)
                            .font(accessibilityService.scaledFont(.subheadline))
                            .fontWeight(accessibilityService.fontWeight(.medium))
                            .foregroundColor(selectedCategory == category ? .white : themeManager.textSecondary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(selectedCategory == category ? themeManager.accentColor : themeManager.surfaceColor.opacity(0.3))
                            )
                    }
                    .accessibilityLabel("\(category.rawValue) category")
                    .accessibilityHint(accessibilityService.buttonHint("select \(category.rawValue) icon category"))
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.bottom, 16)
    }
    
    private var iconGrid: some View {
        ScrollView {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 5), spacing: 12) {
                ForEach(filteredIcons, id: \.self) { icon in
                    Button(action: {
                        selectedIcon = icon
                        
                        // Haptic feedback
                        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                        impactFeedback.impactOccurred()
                    }) {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(selectedIcon == icon ? Color(hex: selectedColor) : themeManager.surfaceColor.opacity(0.3))
                            .frame(width: 50, height: 50)
                            .overlay(
                                Image(systemName: icon)
                                    .font(.system(size: 20, weight: .medium))
                                    .foregroundColor(selectedIcon == icon ? .white : themeManager.textSecondary)
                            )
                            .scaleEffect(selectedIcon == icon ? 1.1 : 1.0)
                            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: selectedIcon)
                    }
                    .accessibilityLabel("Icon: \(icon)")
                    .accessibilityHint(accessibilityService.buttonHint("select this icon"))
                    .accessibilityAddTraits(selectedIcon == icon ? [.isSelected] : [])
                }
            }
            .padding(.horizontal, 20)
        }
    }
    
    private var colorSelection: some View {
        VStack(spacing: 16) {
            Divider()
                .background(themeManager.borderColor)
            
            HStack {
                Text("Color")
                    .font(accessibilityService.scaledFont(.headline))
                    .fontWeight(accessibilityService.fontWeight(.semibold))
                    .foregroundColor(themeManager.textPrimary)
                
                Spacer()
            }
            .padding(.horizontal, 20)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(availableColors, id: \.self) { color in
                        Button(action: {
                            selectedColor = color
                            
                            // Haptic feedback
                            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                            impactFeedback.impactOccurred()
                        }) {
                            Circle()
                                .fill(Color(hex: color))
                                .frame(width: 40, height: 40)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white, lineWidth: selectedColor == color ? 3 : 0)
                                )
                                .scaleEffect(selectedColor == color ? 1.2 : 1.0)
                                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: selectedColor)
                        }
                        .accessibilityLabel("Color option")
                        .accessibilityHint(accessibilityService.buttonHint("select this color"))
                        .accessibilityAddTraits(selectedColor == color ? [.isSelected] : [])
                    }
                }
                .padding(.horizontal, 20)
            }
            .padding(.bottom, 20)
        }
    }
}

#Preview {
    IconSelector(selectedIcon: .constant("lightbulb.fill"), selectedColor: .constant("#FF3B30"))
}
