//
//  NotesView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData

struct NotesView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \Note.createdAt, order: .reverse) private var notes: [Note]
    @Query(sort: \Keyword.text) private var keywords: [Keyword]

    @State private var selectedFilter: NoteFilter = .all
    @State private var searchText = ""
    @State private var showingGridView = false
    @State private var selectedKeyword: Keyword? = nil
    @State private var showingKeywordMenu = false
    @State private var showingShareSheet = false
    @State private var shareItems: [Any] = []
    @State private var showingDuplicateAlert = false
    @State private var duplicatedNote: Note?
    @State private var showingEditKeywords = false
    @State private var selectedNoteForKeywordEdit: Note?
    @StateObject private var accessibilityService = AccessibilityService.shared
    @StateObject private var themeManager = ThemeManager.shared
    
    private var dataManager: DataManager {
        DataManager(modelContext: modelContext)
    }
    
    // Optimized computed property with memoization-like behavior
    private var filteredNotes: [Note] {
        return PerformanceMonitoringService.shared.measurePerformance(of: {
            var filtered = notes

            // Apply filter efficiently
            switch selectedFilter {
            case .all:
                break
            case .recent:
                let oneWeekAgo = Calendar.current.date(byAdding: .weekOfYear, value: -1, to: Date()) ?? Date()
                filtered = filtered.filter { $0.createdAt >= oneWeekAgo }
            case .favorites:
                filtered = filtered.filter { $0.isFavorite }
            case .withAudio:
                filtered = filtered.filter { $0.audioURL != nil && !$0.audioURL!.isEmpty }
            case .today:
                let startOfDay = Calendar.current.startOfDay(for: Date())
                filtered = filtered.filter { $0.createdAt >= startOfDay }
            case .thisWeek:
                let startOfWeek = Calendar.current.dateInterval(of: .weekOfYear, for: Date())?.start ?? Date()
                filtered = filtered.filter { $0.createdAt >= startOfWeek }
            }

            // Apply keyword filter
            if let selectedKeyword = selectedKeyword {
                filtered = filtered.filter { note in
                    note.keywords.contains { $0.id == selectedKeyword.id }
                }
            }

            // Apply search filter
            if !searchText.isEmpty {
                filtered = filtered.filter { note in
                    note.title.localizedCaseInsensitiveContains(searchText) ||
                    note.content.localizedCaseInsensitiveContains(searchText)
                }
            }

            return filtered
        }, eventType: .dataLoad, metadata: ["filter": selectedFilter.rawValue])
    }
    
    private var notesCount: [Keyword: Int] {
        var counts: [Keyword: Int] = [:]
        for keyword in keywords {
            counts[keyword] = notes.filter { note in
                note.keywords.contains { $0.id == keyword.id }
            }.count
        }
        return counts
    }

    var body: some View {
        NavigationView {
            ZStack {
                themeManager.applyBackground().ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header
                    headerView
                    
                    // Search bar
                    searchBarView
                    
                    // Filter buttons
                    filterButtonsView
                    
                    // Notes content
                    if filteredNotes.isEmpty {
                        emptyStateView
                    } else {
                        notesContent
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: shareItems)
        }
        .sheet(isPresented: $showingEditKeywords) {
            if let note = selectedNoteForKeywordEdit {
                EditKeywordsView(note: note, dataManager: dataManager)
            }
        }
        .alert("Note Duplicated", isPresented: $showingDuplicateAlert) {
            Button("OK") { }
        } message: {
            Text("A copy of the note has been created successfully.")
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 15) {
            // Title and controls
            HStack {
                Text("Notes")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Spacer()

                HStack(spacing: 15) {
                    // Keyword filter menu
                    Button(action: {
                        showingKeywordMenu.toggle()
                    }) {
                        Image(systemName: "ellipsis")
                            .font(.title2)
                            .foregroundColor(.white)
                    }
                    .popover(isPresented: $showingKeywordMenu) {
                        KeywordMenuView(
                            keywords: keywords.filter { $0.isEnabled },
                            selectedKeyword: $selectedKeyword,
                            totalNotesCount: notes.count,
                            notesCount: { keyword in
                                notesCount[keyword] ?? 0
                            }
                        )
                        .presentationCompactAdaptation(.popover)
                    }

                    // View toggle
                    Button(action: {
                        showingGridView.toggle()
                    }) {
                        Image(systemName: showingGridView ? "list.bullet" : "square.grid.2x2")
                            .font(.title2)
                            .foregroundColor(.white)
                    }
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    private var searchBarView: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.gray)
            
            TextField("Search notes...", text: $searchText)
                .foregroundColor(.white)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.gray.opacity(0.2))
        )
        .padding(.horizontal, 20)
    }
    
    private var filterButtonsView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(NoteFilter.allCases, id: \.self) { filter in
                    Button(action: {
                        selectedFilter = filter
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: filter.icon)
                                .font(.caption)
                            Text(filter.displayName)
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 15)
                                .fill(selectedFilter == filter ? filter.color : Color.gray.opacity(0.2))
                        )
                        .foregroundColor(selectedFilter == filter ? .white : .gray)
                    }
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.vertical, 10)
    }
    
    private var notesContent: some View {
        ScrollView {
            if showingGridView {
                gridView
            } else {
                listView
            }
        }
    }
    
    private var listView: some View {
        LazyVStack(spacing: 12) {
            ForEach(filteredNotes, id: \.id) { note in
                NavigationLink(destination: NoteDetailView(note: note, dataManager: dataManager)) {
                    NoteRowView(note: note, dataManager: dataManager)
                        .id(note.id) // Optimize reuse
                }
                .buttonStyle(PlainButtonStyle())
                .contextMenu {
                    contextMenuForNote(note)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
        .padding(.bottom, 100) // Space for tab bar
        .drawingGroup() // Enable Metal acceleration for better performance
    }
    
    private var gridView: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 12) {
            ForEach(filteredNotes, id: \.id) { note in
                NavigationLink(destination: NoteDetailView(note: note, dataManager: dataManager)) {
                    NoteCardView(note: note, dataManager: dataManager)
                        .id(note.id) // Optimize reuse
                }
                .buttonStyle(PlainButtonStyle())
                .contextMenu {
                    contextMenuForNote(note)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
        .padding(.bottom, 100) // Space for tab bar
        .drawingGroup() // Enable Metal acceleration for better performance
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Image(systemName: "note.text")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Notes Yet")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("Start recording your first voice note by tapping the microphone button")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            
            Spacer()
        }
    }

    // MARK: - Context Menu

    @ViewBuilder
    private func contextMenuForNote(_ note: Note) -> some View {
        // Favorite/Unfavorite
        Button(action: {
            dataManager.toggleNoteFavorite(note)
        }) {
            Label(note.isFavorite ? "Remove from Favorites" : "Add to Favorites",
                  systemImage: note.isFavorite ? "heart.slash" : "heart.fill")
        }

        // Share
        Button(action: {
            shareNote(note)
        }) {
            Label("Share", systemImage: "square.and.arrow.up")
        }

        // Duplicate
        Button(action: {
            duplicateNote(note)
        }) {
            Label("Duplicate", systemImage: "doc.on.doc")
        }

        // Copy Text
        Button(action: {
            copyNoteText(note)
        }) {
            Label("Copy Text", systemImage: "doc.on.clipboard")
        }

        // Edit Keywords
        Button(action: {
            selectedNoteForKeywordEdit = note
            showingEditKeywords = true
        }) {
            Label("Edit Keywords", systemImage: "tag")
        }

        Divider()

        // Play Audio (if available)
        if note.audioURL != nil {
            Button(action: {
                print("Playing audio for note: \(note.title)")
            }) {
                Label("Play Audio", systemImage: "play.circle")
            }
        }

        // Export as Text File
        Button(action: {
            exportAsTextFile(note)
        }) {
            Label("Export as File", systemImage: "square.and.arrow.down")
        }

        Divider()

        // Delete
        Button(role: .destructive, action: {
            dataManager.deleteNote(note)
        }) {
            Label("Delete", systemImage: "trash")
        }
    }

    // MARK: - Context Menu Actions

    private func shareNote(_ note: Note) {
        var items: [Any] = []

        let formattedText = createFormattedNoteText(note)
        items.append(formattedText)

        if let audioURLString = note.audioURL, !audioURLString.isEmpty {
            var audioURL: URL?

            if audioURLString.hasPrefix("file://") {
                audioURL = URL(string: audioURLString)
            } else if audioURLString.hasPrefix("/") {
                audioURL = URL(fileURLWithPath: audioURLString)
            } else {
                let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
                audioURL = documentsPath.appendingPathComponent(audioURLString)
            }

            if let audioURL = audioURL, FileManager.default.fileExists(atPath: audioURL.path) {
                items.append(audioURL)
            }
        }

        shareItems = items
        showingShareSheet = true
    }

    private func duplicateNote(_ note: Note) {
        let duplicatedNote = dataManager.createNote(
            title: "\(note.title) (Copy)",
            content: note.content,
            audioURL: note.audioURL,
            keywords: note.keywords
        )

        if note.duration > 0 {
            duplicatedNote.duration = note.duration
        }

        self.duplicatedNote = duplicatedNote
        showingDuplicateAlert = true
    }

    private func copyNoteText(_ note: Note) {
        let formattedText = createFormattedNoteText(note)
        UIPasteboard.general.string = formattedText
    }

    private func exportAsTextFile(_ note: Note) {
        let formattedText = createFormattedNoteText(note)
        let fileName = "\(note.title).txt"

        // Create temporary file
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)

        do {
            try formattedText.write(to: tempURL, atomically: true, encoding: .utf8)
            shareItems = [tempURL]
            showingShareSheet = true
        } catch {
            print("Failed to create file: \(error)")
        }
    }

    private func createFormattedNoteText(_ note: Note) -> String {
        var text = "📝 \(note.title)\n\n"
        text += note.content

        if !note.keywords.isEmpty {
            text += "\n\n🏷️ Keywords: "
            text += note.keywords.map { $0.text }.joined(separator: ", ")
        }

        text += "\n\n📅 Created: \(formatDate(note.createdAt))"

        if note.duration > 0 {
            text += "\n🎵 Audio Duration: \(formatDuration(note.duration))"
        }

        text += "\n\n📱 Shared from EchoNote"

        return text
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

enum NoteFilter: String, CaseIterable {
    case all, recent, favorites, withAudio, today, thisWeek

    var displayName: String {
        switch self {
        case .all: return "All Notes"
        case .recent: return "Recent"
        case .favorites: return "Favorites"
        case .withAudio: return "With Audio"
        case .today: return "Today"
        case .thisWeek: return "This Week"
        }
    }

    var color: Color {
        switch self {
        case .all: return Color.blue
        case .recent: return Color.orange
        case .favorites: return Color.yellow
        case .withAudio: return Color.purple
        case .today: return Color.green
        case .thisWeek: return Color.cyan
        }
    }

    var icon: String {
        switch self {
        case .all: return "doc.text"
        case .recent: return "clock"
        case .favorites: return "heart.fill"
        case .withAudio: return "waveform"
        case .today: return "calendar"
        case .thisWeek: return "calendar.badge.clock"
        }
    }
}

#Preview {
    NotesView()
        .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
