//
//  AddKeywordView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData

struct AddKeywordView: View {
    @Environment(\.dismiss) private var dismiss
    let dataManager: DataManager
    @StateObject private var themeManager = ThemeManager.shared

    @State private var keywordText = ""
    @State private var selectedColor = "#34C759"
    @State private var selectedIcon = "lightbulb.fill"
    @State private var showingIconSelector = false
    
    private let availableColors = [
        "#FF3B30", // Red
        "#FF9500", // Orange
        "#FFCC00", // Yellow
        "#34C759", // Green
        "#5AC8FA", // Blue
        "#007AFF", // System Blue
        "#5856D6", // Purple
        "#AF52DE", // Magenta
        "#FF2D92", // Pink
        "#8E8E93"  // Gray
    ]
    
    var body: some View {
        NavigationView {
            ZStack {
                themeManager.applyBackground().ignoresSafeArea()
                
                VStack(spacing: 30) {
                    // Header
                    headerView
                    
                    // Keyword input
                    keywordInputSection
                    
                    // Icon & Color selection
                    iconSelectionSection

                    // Preview
                    previewSection
                    
                    Spacer()
                    
                    // Save button
                    saveButton
                }
                .padding(.horizontal, 20)
            }
        }
        .navigationBarHidden(true)
        .fullScreenCover(isPresented: $showingIconSelector) {
            IconSelector(selectedIcon: $selectedIcon, selectedColor: $selectedColor)
        }
    }
    
    private var headerView: some View {
        HStack {
            Button("Cancel") {
                dismiss()
            }
            .foregroundColor(.gray)
            
            Spacer()
            
            Text("Add Keyword")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Spacer()
            
            Button("Save") {
                saveKeyword()
            }
            .foregroundColor(keywordText.isEmpty ? .gray : .green)
            .disabled(keywordText.isEmpty)
        }
        .padding(.top, 10)
    }
    
    private var keywordInputSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Keyword")
                .font(.headline)
                .foregroundColor(.white)

            TextField("Enter keyword (e.g., Idea, Groceries)", text: $keywordText)
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.2))
                )
                .foregroundColor(.white)
                .autocapitalization(.words)
                .disableAutocorrection(true)
        }
    }
    
    private var iconSelectionSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Icon")
                .font(.headline)
                .foregroundColor(.white)

            Button(action: {
                showingIconSelector = true
            }) {
                HStack(spacing: 16) {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(hex: selectedColor))
                        .frame(width: 50, height: 50)
                        .overlay(
                            Image(systemName: selectedIcon)
                                .font(.system(size: 24, weight: .medium))
                                .foregroundColor(.white)
                        )

                    VStack(alignment: .leading, spacing: 4) {
                        Text("Choose Icon & Color")
                            .font(.headline)
                            .foregroundColor(.white)

                        Text("Tap to customize appearance")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(.gray)
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.1))
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    private var previewSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Preview")
                .font(.headline)
                .foregroundColor(.white)

            HStack(spacing: 16) {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(hex: selectedColor))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Image(systemName: selectedIcon)
                            .font(.system(size: 24, weight: .medium))
                            .foregroundColor(.white)
                    )

                VStack(alignment: .leading, spacing: 4) {
                    Text(keywordText.isEmpty ? "Your keyword" : keywordText)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    Text("Keyword Preview")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }

                Spacer()
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.2))
            )
        }
    }
    
    private var saveButton: some View {
        Button(action: saveKeyword) {
            Text("Add Keyword")
                .font(.headline)
                .foregroundColor(.black)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(keywordText.isEmpty ? Color.gray : Color.green)
                )
        }
        .disabled(keywordText.isEmpty)
        .padding(.bottom, 30)
    }
    
    private func saveKeyword() {
        guard !keywordText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }

        let trimmedText = keywordText.trimmingCharacters(in: .whitespacesAndNewlines)
        _ = dataManager.createKeyword(text: trimmedText, color: selectedColor, icon: selectedIcon)

        dismiss()
    }
}

struct EditKeywordView: View {
    @Environment(\.dismiss) private var dismiss
    let keyword: Keyword
    let dataManager: DataManager
    @StateObject private var themeManager = ThemeManager.shared

    @State private var keywordText: String
    @State private var selectedColor: String
    @State private var selectedIcon: String
    @State private var showingIconSelector = false

    private let availableColors = [
        "#FF3B30", "#FF9500", "#FFCC00", "#34C759", "#5AC8FA",
        "#007AFF", "#5856D6", "#AF52DE", "#FF2D92", "#8E8E93"
    ]

    init(keyword: Keyword, dataManager: DataManager) {
        self.keyword = keyword
        self.dataManager = dataManager
        self._keywordText = State(initialValue: keyword.text)
        self._selectedColor = State(initialValue: keyword.color)
        self._selectedIcon = State(initialValue: keyword.icon)
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                themeManager.applyBackground().ignoresSafeArea()
                
                VStack(spacing: 30) {
                    // Header
                    HStack {
                        Button("Cancel") {
                            dismiss()
                        }
                        .foregroundColor(.gray)

                        Spacer()

                        Text("Edit Keyword")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)

                        Spacer()

                        Button("Save") {
                            saveChanges()
                        }
                        .foregroundColor(keywordText.isEmpty ? .gray : .green)
                        .disabled(keywordText.isEmpty)
                    }
                    .padding(.top, 10)

                    // Preview Section
                    previewSection
                    
                    // Keyword input
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Keyword")
                            .font(.headline)
                            .foregroundColor(.white)

                        TextField("Enter keyword", text: $keywordText)
                            .padding(12)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.gray.opacity(0.2))
                            )
                            .foregroundColor(.white)
                            .autocapitalization(.words)
                            .disableAutocorrection(true)
                    }
                    
                    // Icon selection
                    iconSelectionSection
                    
                    Spacer()
                    
                    // Delete button
                    Button(action: deleteKeyword) {
                        Text("Delete Keyword")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 16)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.red)
                            )
                    }
                    .padding(.bottom, 30)
                }
                .padding(.horizontal, 20)
            }
        }
        .navigationBarHidden(true)
        .fullScreenCover(isPresented: $showingIconSelector) {
            IconSelector(selectedIcon: $selectedIcon, selectedColor: $selectedColor)
        }
    }
    
    // MARK: - View Components

    private var previewSection: some View {
        VStack(spacing: 16) {
            HStack(spacing: 16) {
                // Icon Preview
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(hex: selectedColor))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Image(systemName: selectedIcon)
                            .font(.system(size: 28, weight: .medium))
                            .foregroundColor(.white)
                    )

                VStack(alignment: .leading, spacing: 4) {
                    Text(keywordText.isEmpty ? "Keyword Name" : keywordText)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    Text("Keyword Preview")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }

                Spacer()
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.gray.opacity(0.1))
            )
        }
    }

    private var iconSelectionSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Icon")
                .font(.headline)
                .foregroundColor(.white)

            Button(action: {
                showingIconSelector = true
            }) {
                HStack(spacing: 16) {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(hex: selectedColor))
                        .frame(width: 50, height: 50)
                        .overlay(
                            Image(systemName: selectedIcon)
                                .font(.system(size: 24, weight: .medium))
                                .foregroundColor(.white)
                        )

                    VStack(alignment: .leading, spacing: 4) {
                        Text("Choose Icon & Color")
                            .font(.headline)
                            .foregroundColor(.white)

                        Text("Tap to customize appearance")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(.gray)
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.1))
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }

    private func saveChanges() {
        let trimmedText = keywordText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedText.isEmpty else { return }

        dataManager.updateKeyword(keyword, text: trimmedText, color: selectedColor, icon: selectedIcon)
        dismiss()
    }
    
    private func deleteKeyword() {
        dataManager.deleteKeyword(keyword)
        dismiss()
    }
}

#Preview {
    @Previewable @State var sampleDataManager = {
        let container = try! ModelContainer(for: Keyword.self, configurations: ModelConfiguration(isStoredInMemoryOnly: true))
        return DataManager(modelContext: container.mainContext)
    }()

    AddKeywordView(dataManager: sampleDataManager)
}
