import SwiftUI

struct PrivacySettingsView: View {
    @StateObject private var permissionsService = PermissionsService()
    @StateObject private var privacyManager = PrivacyManager()
    
    @State private var showingDataDeletionAlert = false
    @State private var selectedDeletionScope: DataDeletionScope = .allData
    @State private var showingExportSheet = false
    @State private var exportedFileURL: URL?
    @State private var showingPermissionAlert = false
    @State private var permissionAlertMessage = ""
    
    var body: some View {
        NavigationView {
            List {
                permissionsSection
                privacySettingsSection
                dataManagementSection
                privacyPolicySection
            }
            .navigationTitle("Privacy & Security")
            .navigationBarTitleDisplayMode(.large)
            .task {
                await permissionsService.checkAllPermissions()
            }
            .alert("Data Deletion", isPresented: $showingDataDeletionAlert) {
                Button("Cancel", role: .cancel) { }
                Button("Delete", role: .destructive) {
                    Task {
                        await deleteData()
                    }
                }
            } message: {
                Text("Are you sure you want to delete \(selectedDeletionScope.displayName.lowercased())? This action cannot be undone.")
            }
            .alert("Permission Status", isPresented: $showingPermissionAlert) {
                Button("OK") { }
                Button("Open Settings") {
                    permissionsService.openAppSettings()
                }
            } message: {
                Text(permissionAlertMessage)
            }
            .sheet(isPresented: $showingExportSheet) {
                if let url = exportedFileURL {
                    ShareSheet(items: [url])
                }
            }
        }
    }
    
    // MARK: - Permissions Section
    
    private var permissionsSection: some View {
        Section("App Permissions") {
            ForEach(PermissionType.allCases, id: \.rawValue) { permission in
                PermissionRow(
                    permission: permission,
                    status: permissionsService.getPermissionStatus(permission),
                    onTap: {
                        Task {
                            await requestPermission(permission)
                        }
                    }
                )
            }
            
            if !permissionsService.allRequiredPermissionsGranted {
                Button("Request All Required Permissions") {
                    Task {
                        await requestAllPermissions()
                    }
                }
                .foregroundColor(.blue)
            }
        }
    }
    
    // MARK: - Privacy Settings Section
    
    private var privacySettingsSection: some View {
        Section("Privacy Settings") {
            Toggle("Allow Analytics", isOn: Binding(
                get: { privacyManager.privacySettings.allowAnalytics },
                set: { newValue in
                    var settings = privacyManager.privacySettings
                    settings.allowAnalytics = newValue
                    privacyManager.updatePrivacySettings(settings)
                }
            ))
            
            Toggle("Allow Crash Reporting", isOn: Binding(
                get: { privacyManager.privacySettings.allowCrashReporting },
                set: { newValue in
                    var settings = privacyManager.privacySettings
                    settings.allowCrashReporting = newValue
                    privacyManager.updatePrivacySettings(settings)
                }
            ))
            
            Toggle("Share Usage Data", isOn: Binding(
                get: { privacyManager.privacySettings.allowUsageTracking },
                set: { newValue in
                    var settings = privacyManager.privacySettings
                    settings.allowUsageTracking = newValue
                    privacyManager.updatePrivacySettings(settings)
                }
            ))
            
            Toggle("Encrypt Local Data", isOn: Binding(
                get: { privacyManager.privacySettings.encryptLocalData },
                set: { newValue in
                    var settings = privacyManager.privacySettings
                    settings.encryptLocalData = newValue
                    privacyManager.updatePrivacySettings(settings)
                }
            ))
        }
    }
    
    // MARK: - Data Management Section
    
    private var dataManagementSection: some View {
        Section("Data Management") {
            Button("Export My Data") {
                Task {
                    await exportUserData()
                }
            }
            .foregroundColor(.blue)
            
            Menu("Delete Data") {
                ForEach([DataDeletionScope.notesOnly, .keywordsOnly, .audioFilesOnly, .userPreferencesOnly, .analyticsDataOnly], id: \.displayName) { scope in
                    Button(scope.displayName) {
                        selectedDeletionScope = scope
                        showingDataDeletionAlert = true
                    }
                }
                
                Divider()
                
                Button("Delete All Data", role: .destructive) {
                    selectedDeletionScope = .allData
                    showingDataDeletionAlert = true
                }
            }
            .foregroundColor(.red)
        }
    }
    
    // MARK: - Privacy Policy Section
    
    private var privacyPolicySection: some View {
        Section("Legal") {
            if let privacyURL = privacyManager.getPrivacyPolicyURL() {
                Link("Privacy Policy", destination: privacyURL)
                    .foregroundColor(.blue)
            }
            
            HStack {
                Text("Privacy Policy Accepted")
                Spacer()
                Text(privacyManager.hasAcceptedPrivacyPolicy ? "Yes" : "No")
                    .foregroundColor(privacyManager.hasAcceptedPrivacyPolicy ? .green : .red)
            }
            
            if let acceptanceDate = privacyManager.lastPrivacyPolicyAcceptanceDate {
                HStack {
                    Text("Acceptance Date")
                    Spacer()
                    Text(acceptanceDate, style: .date)
                        .foregroundColor(.secondary)
                }
            }
            
            if privacyManager.needsPrivacyPolicyUpdate() {
                Button("Accept Updated Privacy Policy") {
                    privacyManager.acceptPrivacyPolicy()
                }
                .foregroundColor(.blue)
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func requestPermission(_ permission: PermissionType) async {
        let result = await permissionsService.requestPermission(permission)
        
        if !result.wasGranted {
            permissionAlertMessage = "Permission for \(permission.displayName) was denied. You can enable it in Settings."
            showingPermissionAlert = true
        }
    }
    
    private func requestAllPermissions() async {
        let results = await permissionsService.requestAllRequiredPermissions()
        
        let deniedPermissions = results.filter { !$0.wasGranted }
        if !deniedPermissions.isEmpty {
            let permissionNames = deniedPermissions.map { $0.permission.displayName }.joined(separator: ", ")
            permissionAlertMessage = "The following permissions were denied: \(permissionNames). You can enable them in Settings."
            showingPermissionAlert = true
        }
    }
    
    private func deleteData() async {
        do {
            try await privacyManager.deleteData(scope: selectedDeletionScope)
        } catch {
            print("Failed to delete data: \(error)")
        }
    }
    
    private func exportUserData() async {
        do {
            let exportURL = try await privacyManager.exportUserData()
            exportedFileURL = exportURL
            showingExportSheet = true
        } catch {
            print("Failed to export user data: \(error)")
        }
    }
}

// MARK: - Permission Row

struct PermissionRow: View {
    let permission: PermissionType
    let status: PermissionStatus
    let onTap: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(permission.displayName)
                        .font(.headline)
                    
                    if permission.isRequired {
                        Text("Required")
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.red.opacity(0.2))
                            .foregroundColor(.red)
                            .cornerRadius(4)
                    }
                }
                
                Text(permission.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing) {
                Text(status.displayText)
                    .font(.caption)
                    .foregroundColor(status.isGranted ? .green : .red)
                
                if !status.isGranted && status != .restricted {
                    Button("Request") {
                        onTap()
                    }
                    .font(.caption)
                    .buttonStyle(.bordered)
                }
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            if !status.isGranted && status != .restricted {
                onTap()
            }
        }
    }
}

// MARK: - Share Sheet

struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: items, applicationActivities: nil)
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - Preview

#Preview {
    PrivacySettingsView()
}
