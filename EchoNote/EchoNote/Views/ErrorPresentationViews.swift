//
//  ErrorPresentationViews.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI

// MARK: - Error Alert View

struct ErrorAlertView: View {
    let error: EchoNoteError
    let onDismiss: () -> Void
    let onRecoveryAction: (RecoveryAction) -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // Error icon and title
            VStack(spacing: 12) {
                Image(systemName: error.category.icon)
                    .font(.system(size: 50))
                    .foregroundColor(error.severity.color)
                
                Text(error.category.displayName)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
            }
            
            // Error message
            Text(error.userMessage)
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
            
            // Recovery actions
            if !error.recoveryActions.isEmpty {
                VStack(spacing: 12) {
                    ForEach(Array(error.recoveryActions.enumerated()), id: \.offset) { index, action in
                        Button(action: {
                            onRecoveryAction(action)
                        }) {
                            HStack {
                                Text(action.title)
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                
                                Spacer()
                                
                                Image(systemName: "arrow.right")
                                    .font(.caption)
                            }
                            .foregroundColor(action.isDestructive ? .white : .black)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(action.isDestructive ? Color.red : Color.green)
                            )
                        }
                    }
                }
            }
            
            // Dismiss button
            Button(action: onDismiss) {
                Text("Dismiss")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )
            }
        }
        .padding(30)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.black)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(error.severity.color.opacity(0.3), lineWidth: 2)
                )
        )
        .padding(.horizontal, 40)
    }
}

// MARK: - Error Toast View

struct ErrorToastView: View {
    let error: EchoNoteError
    let onDismiss: () -> Void
    @State private var isVisible = false
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: error.category.icon)
                .font(.title2)
                .foregroundColor(error.severity.color)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(error.category.displayName)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.gray)
                
                Text(error.userMessage)
                    .font(.subheadline)
                    .foregroundColor(.white)
                    .lineLimit(2)
            }
            
            Spacer()
            
            Button(action: onDismiss) {
                Image(systemName: "xmark")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(error.severity.color.opacity(0.5), lineWidth: 1)
                )
        )
        .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
        .scaleEffect(isVisible ? 1 : 0.8)
        .opacity(isVisible ? 1 : 0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isVisible)
        .onAppear {
            isVisible = true
            
            // Auto-dismiss after delay for non-critical errors
            if error.severity != .critical {
                DispatchQueue.main.asyncAfter(deadline: .now() + 4) {
                    onDismiss()
                }
            }
        }
    }
}

// MARK: - Error Banner View

struct ErrorBannerView: View {
    let error: EchoNoteError
    let onDismiss: () -> Void
    let onAction: (RecoveryAction) -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                Image(systemName: error.category.icon)
                    .font(.title2)
                    .foregroundColor(error.severity.color)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(error.category.displayName)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.gray)
                    
                    Text(error.userMessage)
                        .font(.subheadline)
                        .foregroundColor(.white)
                        .lineLimit(nil)
                }
                
                Spacer()
                
                Button(action: onDismiss) {
                    Image(systemName: "xmark")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            
            // Recovery actions (show first action only)
            if let firstAction = error.recoveryActions.first {
                Button(action: {
                    onAction(firstAction)
                }) {
                    HStack {
                        Text(firstAction.title)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                        
                        Spacer()
                        
                        Image(systemName: "arrow.right")
                            .font(.caption)
                    }
                    .foregroundColor(firstAction.isDestructive ? .white : .black)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(firstAction.isDestructive ? Color.red : error.severity.color)
                    )
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(error.severity.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Error List View

struct ErrorListView: View {
    @StateObject private var errorService = ErrorHandlingService.shared
    @State private var selectedSeverity: ErrorSeverity?
    @State private var selectedCategory: ErrorCategory?
    
    var filteredErrors: [ErrorHandlingService.ErrorRecord] {
        var filtered = errorService.errorHistory
        
        if let severity = selectedSeverity {
            filtered = filtered.filter { $0.error.severity == severity }
        }
        
        if let category = selectedCategory {
            filtered = filtered.filter { $0.error.category == category }
        }
        
        return filtered
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Filters
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        // Severity filters
                        ForEach(ErrorSeverity.allCases, id: \.self) { severity in
                            FilterChip(
                                title: severity.displayName,
                                isSelected: selectedSeverity == severity,
                                color: severity.color
                            ) {
                                selectedSeverity = selectedSeverity == severity ? nil : severity
                            }
                        }
                        
                        Divider()
                            .frame(height: 20)
                        
                        // Category filters
                        ForEach(ErrorCategory.allCases, id: \.self) { category in
                            FilterChip(
                                title: category.displayName,
                                isSelected: selectedCategory == category,
                                color: .blue
                            ) {
                                selectedCategory = selectedCategory == category ? nil : category
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                }
                .padding(.vertical, 10)
                
                // Error list
                List(filteredErrors) { record in
                    ErrorRowView(record: record)
                        .listRowBackground(Color.clear)
                        .listRowSeparator(.hidden)
                }
                .listStyle(PlainListStyle())
                .background(Color.black)
            }
            .background(Color.black)
            .navigationTitle("Error Log")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Clear") {
                        errorService.clearErrorHistory()
                    }
                    .foregroundColor(.red)
                }
            }
        }
    }
}

// MARK: - Supporting Views

struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(isSelected ? .black : color)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isSelected ? color : color.opacity(0.2))
                )
        }
    }
}

struct ErrorRowView: View {
    let record: ErrorHandlingService.ErrorRecord
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: record.error.category.icon)
                .font(.title2)
                .foregroundColor(record.error.severity.color)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(record.error.category.displayName)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.gray)
                    
                    Spacer()
                    
                    Text(formatTimestamp(record.timestamp))
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                Text(record.error.userMessage)
                    .font(.subheadline)
                    .foregroundColor(.white)
                    .lineLimit(2)
                
                if let context = record.context {
                    Text("Context: \(context)")
                        .font(.caption)
                        .foregroundColor(.gray)
                        .lineLimit(1)
                }
            }
            
            Spacer()
            
            VStack {
                Circle()
                    .fill(record.error.severity.color)
                    .frame(width: 8, height: 8)
                
                if record.wasHandled {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.caption)
                        .foregroundColor(.green)
                }
            }
        }
        .padding(.vertical, 8)
    }
    
    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter.string(from: date)
    }
}

// MARK: - Error Handling Modifier

struct ErrorHandlingModifier: ViewModifier {
    @StateObject private var errorService = ErrorHandlingService.shared
    @State private var showingToast = false
    @State private var toastError: EchoNoteError?

    func body(content: Content) -> some View {
        content
            .overlay(
                // Toast overlay
                VStack {
                    Spacer()

                    if showingToast, let error = toastError {
                        ErrorToastView(error: error) {
                            dismissToast()
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 100) // Above tab bar
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                    }
                }
                .animation(.spring(), value: showingToast)
            )
            .alert("Error", isPresented: $errorService.showingErrorAlert) {
                if let error = errorService.currentError {
                    ForEach(Array(error.recoveryActions.enumerated()), id: \.offset) { index, action in
                        Button(action.title) {
                            errorService.executeRecoveryAction(action)
                        }
                    }

                    Button("Dismiss", role: .cancel) {
                        errorService.dismissError()
                    }
                }
            } message: {
                if let error = errorService.currentError {
                    Text(error.userMessage)
                }
            }
            .onReceive(errorService.$currentError) { error in
                if let error = error, error.severity <= .medium {
                    // Show toast for low/medium severity errors
                    showToast(error)
                    errorService.dismissError()
                }
            }
    }

    private func showToast(_ error: EchoNoteError) {
        toastError = error
        showingToast = true
    }

    private func dismissToast() {
        showingToast = false
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            toastError = nil
        }
    }
}

extension View {
    func errorHandling() -> some View {
        modifier(ErrorHandlingModifier())
    }
}

// MARK: - Debug Error Console

struct DebugErrorConsoleView: View {
    @StateObject private var errorService = ErrorHandlingService.shared
    @StateObject private var loggingService = LoggingService.shared
    @State private var selectedTab = 0

    var body: some View {
        NavigationView {
            VStack {
                Picker("Console Type", selection: $selectedTab) {
                    Text("Errors").tag(0)
                    Text("Logs").tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()

                if selectedTab == 0 {
                    ErrorListView()
                } else {
                    LogListView()
                }
            }
            .background(Color.black)
            .navigationTitle("Debug Console")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct LogListView: View {
    @StateObject private var loggingService = LoggingService.shared
    @State private var selectedLevel: LogLevel?
    @State private var selectedCategory: LogCategory?

    var filteredLogs: [LogEntry] {
        loggingService.getFilteredLogs(level: selectedLevel, category: selectedCategory)
    }

    var body: some View {
        VStack(spacing: 0) {
            // Filters
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(LogLevel.allCases, id: \.self) { level in
                        FilterChip(
                            title: level.rawValue.capitalized,
                            isSelected: selectedLevel == level,
                            color: .blue
                        ) {
                            selectedLevel = selectedLevel == level ? nil : level
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
            .padding(.vertical, 10)

            List(filteredLogs) { entry in
                LogRowView(entry: entry)
                    .listRowBackground(Color.clear)
                    .listRowSeparator(.hidden)
            }
            .listStyle(PlainListStyle())
        }
    }
}

struct LogRowView: View {
    let entry: LogEntry

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(entry.level.emoji)
                Text(entry.category.rawValue.uppercased())
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.gray)

                Spacer()

                Text(formatTimestamp(entry.timestamp))
                    .font(.caption)
                    .foregroundColor(.gray)
            }

            Text(entry.message)
                .font(.caption)
                .foregroundColor(.white)
                .lineLimit(nil)

            if let metadata = entry.metadata, !metadata.isEmpty {
                Text(metadata.map { "\($0.key): \($0.value)" }.joined(separator: ", "))
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
        }
        .padding(.vertical, 4)
    }

    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter.string(from: date)
    }
}

// MARK: - Crash Reports View

struct CrashReportsView: View {
    @State private var crashReports: [CrashReport] = []
    @State private var summary: CrashReportsSummary?

    var body: some View {
        NavigationView {
            VStack {
                if let summary = summary {
                    // Summary section
                    VStack(spacing: 16) {
                        HStack {
                            VStack(alignment: .leading) {
                                Text("Total Crashes")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                Text("\(summary.totalCrashes)")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                            }

                            Spacer()

                            VStack(alignment: .trailing) {
                                Text("Recent")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                Text("\(summary.recentCrashes.count)")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.orange)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.gray.opacity(0.1))
                        )

                        // Crash types breakdown
                        HStack(spacing: 12) {
                            ForEach(Array(summary.crashesByType.keys), id: \.self) { type in
                                VStack {
                                    Text(type.rawValue.capitalized)
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                    Text("\(summary.crashesByType[type] ?? 0)")
                                        .font(.headline)
                                        .foregroundColor(.white)
                                }
                                .padding(.vertical, 8)
                                .padding(.horizontal, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color.red.opacity(0.2))
                                )
                            }
                        }
                    }
                    .padding(.horizontal)
                }

                // Crash reports list
                List(crashReports, id: \.id) { report in
                    CrashReportRowView(report: report)
                        .listRowBackground(Color.clear)
                        .listRowSeparator(.hidden)
                }
                .listStyle(PlainListStyle())
            }
            .background(Color.black)
            .navigationTitle("Crash Reports")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Clear All") {
                        CrashReportingService.shared.clearAllCrashReports()
                        loadCrashReports()
                    }
                    .foregroundColor(.red)
                }
            }
            .onAppear {
                loadCrashReports()
            }
        }
    }

    private func loadCrashReports() {
        crashReports = CrashReportingService.shared.getAllCrashReports()
        summary = CrashReportingService.shared.getCrashReportsSummary()
    }
}

struct CrashReportRowView: View {
    let report: CrashReport

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(report.crashType.rawValue.capitalized)
                    .font(.headline)
                    .foregroundColor(.white)

                Spacer()

                Text(formatTimestamp(report.timestamp))
                    .font(.caption)
                    .foregroundColor(.gray)
            }

            Text("App: \(report.appVersion) | OS: \(report.osVersion)")
                .font(.caption)
                .foregroundColor(.gray)

            if !report.userInfo.isEmpty {
                Text(report.userInfo.map { "\($0.key): \($0.value)" }.joined(separator: " | "))
                    .font(.caption)
                    .foregroundColor(.orange)
                    .lineLimit(2)
            }

            Text(report.stackTrace)
                .font(.caption2)
                .foregroundColor(.gray)
                .lineLimit(3)
        }
        .padding(.vertical, 8)
    }

    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

#Preview {
    ErrorListView()
}
