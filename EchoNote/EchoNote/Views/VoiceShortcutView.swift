//
//  VoiceShortcutView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import IntentsUI

struct VoiceShortcutView: UIViewControllerRepresentable {
    let shortcut: SiriShortcut
    @Environment(\.dismiss) private var dismiss
    
    func makeUIViewController(context: Context) -> INUIAddVoiceShortcutViewController {
        let activity = NSUserActivity(activityType: shortcut.activityType)
        activity.title = shortcut.title
        activity.suggestedInvocationPhrase = shortcut.phrase
        activity.isEligibleForSearch = true
        activity.isEligibleForPrediction = true
        activity.persistentIdentifier = shortcut.id
        
        let shortcutToAdd = INShortcut(userActivity: activity)
        let viewController = INUIAddVoiceShortcutViewController(shortcut: shortcutToAdd)
        viewController.delegate = context.coordinator
        
        return viewController
    }
    
    func updateUIViewController(_ uiViewController: INUIAddVoiceShortcutViewController, context: Context) {
        // No updates needed
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, INUIAddVoiceShortcutViewControllerDelegate {
        let parent: VoiceShortcutView

        init(_ parent: VoiceShortcutView) {
            self.parent = parent
        }

        nonisolated func addVoiceShortcutViewController(_ controller: INUIAddVoiceShortcutViewController, didFinishWith voiceShortcut: INVoiceShortcut?, error: Error?) {
            if let error = error {
                print("Error adding voice shortcut: \(error.localizedDescription)")
            } else if voiceShortcut != nil {
                print("Successfully added voice shortcut: \(parent.shortcut.title)")

                // Donate the intent to help Siri learn
                Task { @MainActor in
                    SiriShortcutsService.shared.suggestContextualShortcuts(for: .afterRecording)
                }
            }

            Task { @MainActor in
                parent.dismiss()
            }
        }

        nonisolated func addVoiceShortcutViewControllerDidCancel(_ controller: INUIAddVoiceShortcutViewController) {
            Task { @MainActor in
                parent.dismiss()
            }
        }
    }
}

#Preview {
    VoiceShortcutView(shortcut: SiriShortcut(
        id: "startRecording",
        title: "Start Recording",
        subtitle: "Begin recording a new voice note",
        phrase: "Start recording",
        activityType: "com.clevorie.EchoNote.startRecording",
        iconName: "headphones"
    ))
}
