//
//  NoteDetailView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData

struct NoteDetailView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.hideFloatingButton) private var hideFloatingButton
    let note: Note
    let dataManager: DataManager
    @StateObject private var themeManager = ThemeManager.shared

    @State private var isEditing = false
    @State private var editedTitle: String
    @State private var editedContent: String
    @State private var showingShareSheet = false
    @State private var shareItems: [Any] = []
    @State private var showingShareOptions = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @StateObject private var audioManager = AudioManager()
    @State private var isViewLoaded = false
    
    init(note: Note, dataManager: DataManager) {
        self.note = note
        self.dataManager = dataManager
        self._editedTitle = State(initialValue: note.title)
        self._editedContent = State(initialValue: note.content)
    }
    
    var body: some View {
        NavigationView {
            ZSta<PERSON> {
                themeManager.applyBackground().ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header
                    headerView
                    
                    ScrollView {
                        VStack(spacing: 20) {
                            // Keywords section
                            if !note.keywords.isEmpty {
                                keywordsSection
                            }
                            
                            // Audio playback section (hidden during editing)
                            if note.audioURL != nil && !isEditing {
                                audioPlaybackSection
                            }
                            
                            // Title section
                            titleSection
                            
                            // Content section
                            contentSection
                            
                            // Metadata section
                            metadataSection
                            
                            Spacer(minLength: 50)
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: shareItems)
        }
        .sheet(isPresented: $showingShareOptions) {
            ShareOptionsView(note: note, onShareOptionSelected: { option in
                handleShareOption(option)
            })
            .presentationDetents([.medium])
            .presentationDragIndicator(.visible)
        }
        .alert("Share Status", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
        .onAppear {
            // Preload view for better performance
            isViewLoaded = true
        }
    }
    
    private var headerView: some View {
        HStack {
            Button(isEditing ? "Cancel" : "Close") {
                if isEditing {
                    // Cancel editing - revert changes
                    cancelEditing()
                } else {
                    // Close view normally
                    hideFloatingButton.wrappedValue = false
                    dismiss()
                }
            }
            .foregroundColor(isEditing ? .red : .gray)
            
            Spacer()
            
            Text("Note Details")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Spacer()
            
            HStack(spacing: 15) {
                Button(action: {
                    showingShareOptions = true
                }) {
                    Image(systemName: "square.and.arrow.up")
                        .foregroundColor(.gray)
                }

                Button(action: {
                    dataManager.toggleNoteFavorite(note)
                }) {
                    Image(systemName: note.isFavorite ? "heart.fill" : "heart")
                        .foregroundColor(note.isFavorite ? .yellow : .gray)
                }

                Button(action: {
                    if isEditing {
                        saveChanges()
                        dismiss() // Close after saving
                    } else {
                        isEditing = true
                        hideFloatingButton.wrappedValue = true
                    }
                }) {
                    Text(isEditing ? "Save" : "Edit")
                        .foregroundColor(.green)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    private var keywordsSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Keywords")
                .font(.headline)
                .foregroundColor(.white)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 10) {
                    ForEach(note.keywords, id: \.id) { keyword in
                        HStack(spacing: 6) {
                            Circle()
                                .fill(Color(hex: keyword.color))
                                .frame(width: 8, height: 8)
                            
                            Text(keyword.text)
                                .font(.subheadline)
                                .foregroundColor(.white)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 15)
                                .fill(Color(hex: keyword.color).opacity(0.2))
                        )
                    }
                }
                .padding(.horizontal, 1)
            }
        }
    }
    
    private var audioPlaybackSection: some View {
        VStack(spacing: 15) {
            Text("Audio Recording")
                .font(.headline)
                .foregroundColor(.white)
            
            HStack(spacing: 20) {
                // Play/Pause button
                Button(action: {
                    togglePlayback()
                }) {
                    Image(systemName: audioManager.isPlaying ? "pause.circle.fill" : "play.circle.fill")
                        .font(.system(size: 50))
                        .foregroundColor(.green)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    // Waveform visualization
                    HStack(spacing: 2) {
                        ForEach(0..<20, id: \.self) { index in
                            RoundedRectangle(cornerRadius: 1)
                                .fill(Color.green.opacity(audioManager.isPlaying ? 0.8 : 0.6))
                                .frame(width: 3, height: waveformHeight(for: index))
                                .animation(.easeInOut(duration: 0.1), value: audioManager.playbackAudioLevel)
                        }
                    }
                    .frame(height: 30)

                    // Duration and current time
                    HStack {
                        Text(formatDuration(audioManager.isPlaying ? audioManager.playbackCurrentTime : 0))
                            .font(.caption)
                            .foregroundColor(.green)

                        Text("/")
                            .font(.caption)
                            .foregroundColor(.gray)

                        Text(formatDuration(note.duration))
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
                
                Spacer()
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
            )
        }
    }
    
    private var titleSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Title")
                .font(.headline)
                .foregroundColor(.white)
            
            ZStack {
                // Always present TextField for better performance
                TextField("Note title", text: $editedTitle)
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(isEditing ? 0.2 : 0.1))
                    )
                    .foregroundColor(.white)
                    .disabled(!isEditing) // Disable interaction when not editing
                    .opacity(isEditing ? 1.0 : 0.0) // Hide when not editing
                    .animation(.easeInOut(duration: 0.2), value: isEditing)

                // Read-only text overlay when not editing
                if !isEditing {
                    Text(note.title)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(12)
                        .background(Color.clear)
                        .allowsHitTesting(false) // Allow taps to pass through
                }
            }
        }
    }
    
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Content")
                .font(.headline)
                .foregroundColor(.white)
            
            ZStack {
                // Always present TextEditor for better performance
                ZStack(alignment: .topLeading) {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(isEditing ? 0.2 : 0.1))
                        .frame(minHeight: 200)

                    TextEditor(text: $editedContent)
                        .foregroundColor(.white)
                        .background(Color.clear)
                        .frame(minHeight: 200)
                        .padding(8)
                        .scrollContentBackground(.hidden)
                        .disabled(!isEditing) // Disable interaction when not editing
                        .opacity(isEditing ? 1.0 : 0.0) // Hide when not editing
                        .animation(.easeInOut(duration: 0.2), value: isEditing)
                }

                // Read-only text overlay when not editing
                if !isEditing {
                    Text(note.content)
                        .font(.body)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(12)
                        .background(Color.clear)
                        .allowsHitTesting(false) // Allow taps to pass through
                }
            }
        }
    }
    
    private var metadataSection: some View {
        VStack(spacing: 15) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Created")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text(formatDate(note.createdAt))
                        .font(.subheadline)
                        .foregroundColor(.white)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("Modified")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text(formatDate(note.updatedAt))
                        .font(.subheadline)
                        .foregroundColor(.white)
                }
            }
            
            if note.duration > 0 {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Duration")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text(formatDuration(note.duration))
                            .font(.subheadline)
                            .foregroundColor(.white)
                    }
                    
                    Spacer()
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
        )
    }
    
    private func togglePlayback() {
        guard let audioURLString = note.audioURL,
              let audioURL = URL(string: audioURLString) else {
            print("No audio URL available for this note")
            return
        }

        if audioManager.isPlaying {
            audioManager.pausePlayback()
        } else {
            // If we're not currently playing this specific audio file, start playing it
            if audioManager.currentAudioURL != audioURL {
                audioManager.playRecording(url: audioURL)
            } else {
                audioManager.resumePlayback()
            }
        }
    }

    private func saveChanges() {
        dataManager.updateNote(note, title: editedTitle, content: editedContent)
        isEditing = false
        hideFloatingButton.wrappedValue = false
    }

    private func cancelEditing() {
        // Revert changes to original values
        editedTitle = note.title
        editedContent = note.content
        isEditing = false
        hideFloatingButton.wrappedValue = false
        dismiss()
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    private func waveformHeight(for index: Int) -> CGFloat {
        if audioManager.isPlaying {
            // Use actual audio level for dynamic visualization
            let baseHeight: CGFloat = 8
            let maxHeight: CGFloat = 24
            let audioLevel = CGFloat(audioManager.playbackAudioLevel)
            let dynamicHeight = baseHeight + (maxHeight - baseHeight) * audioLevel

            // Add some variation based on index for visual appeal
            let variation = sin(Double(index) * 0.5) * 3
            return max(baseHeight, dynamicHeight + CGFloat(variation))
        } else {
            // Static waveform when not playing
            return CGFloat.random(in: 8...24)
        }
    }

    private func handleShareOption(_ option: ShareOption) {
        showingShareOptions = false

        switch option {
        case .systemShare:
            shareNote()
        case .textOnly:
            shareTextOnly()
        case .audioOnly:
            shareAudioOnly()
        case .copyToClipboard:
            copyToClipboard()
        case .exportAsFile:
            exportAsFile()
        }
    }

    private func shareNote() {
        var items: [Any] = []
        var shareDescription = ""

        // Create formatted text content
        let formattedText = createFormattedNoteText()
        items.append(formattedText)
        shareDescription += "Text content"

        // Add audio file if available
        if let audioURLString = note.audioURL {
            print("Audio URL string: \(audioURLString)")

            // Try different URL creation methods
            var audioURL: URL?

            // Method 1: Direct URL creation
            if audioURLString.hasPrefix("file://") {
                audioURL = URL(string: audioURLString)
            } else if audioURLString.hasPrefix("/") {
                // Method 2: File path
                audioURL = URL(fileURLWithPath: audioURLString)
            } else {
                // Method 3: Assume it's a relative path
                let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
                audioURL = documentsPath.appendingPathComponent(audioURLString)
            }

            if let audioURL = audioURL {
                print("Checking audio file at: \(audioURL.path)")
                if FileManager.default.fileExists(atPath: audioURL.path) {
                    items.append(audioURL)
                    shareDescription += " + Audio file"
                    print("Audio file found and added to share items")
                } else {
                    print("Audio file not found at path: \(audioURL.path)")
                }
            } else {
                print("Could not create URL from: \(audioURLString)")
            }
        } else {
            print("No audio URL available")
        }

        print("Sharing items: \(items.count) items - \(shareDescription)")
        shareItems = items
        showingShareSheet = true
    }

    private func shareTextOnly() {
        let formattedText = createFormattedNoteText()
        shareItems = [formattedText]
        showingShareSheet = true
    }

    private func shareAudioOnly() {
        guard let audioURLString = note.audioURL else {
            alertMessage = "This note doesn't have an audio recording."
            showingAlert = true
            return
        }

        print("Attempting to share audio: \(audioURLString)")

        // Try different URL creation methods
        var audioURL: URL?

        if audioURLString.hasPrefix("file://") {
            audioURL = URL(string: audioURLString)
        } else if audioURLString.hasPrefix("/") {
            audioURL = URL(fileURLWithPath: audioURLString)
        } else {
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            audioURL = documentsPath.appendingPathComponent(audioURLString)
        }

        guard let audioURL = audioURL else {
            alertMessage = "Invalid audio file path."
            showingAlert = true
            return
        }

        print("Checking audio file at: \(audioURL.path)")

        guard FileManager.default.fileExists(atPath: audioURL.path) else {
            alertMessage = "Audio file not found. The recording may have been deleted."
            showingAlert = true
            return
        }

        print("Audio file found, sharing...")
        shareItems = [audioURL]
        showingShareSheet = true
    }

    private func copyToClipboard() {
        let formattedText = createFormattedNoteText()
        UIPasteboard.general.string = formattedText

        alertMessage = "Note content copied to clipboard!"
        showingAlert = true
    }

    private func exportAsFile() {
        let formattedText = createFormattedNoteText()
        let fileName = "\(note.title).txt"

        // Create temporary file
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)

        do {
            try formattedText.write(to: tempURL, atomically: true, encoding: .utf8)
            shareItems = [tempURL]
            showingShareSheet = true
        } catch {
            print("Failed to create file: \(error)")
        }
    }

    private func createFormattedNoteText() -> String {
        var text = "📝 \(note.title)\n\n"
        text += note.content

        if !note.keywords.isEmpty {
            text += "\n\n🏷️ Keywords: "
            text += note.keywords.map { $0.text }.joined(separator: ", ")
        }

        text += "\n\n📅 Created: \(formatDate(note.createdAt))"

        if note.duration > 0 {
            text += "\n🎵 Audio Duration: \(formatDuration(note.duration))"
        }

        text += "\n\n📱 Shared from EchoNote"

        return text
    }

}

#Preview {
    @Previewable @State var sampleNote = Note(title: "Sample Note", content: "This is a sample note content for preview purposes.")
    @Previewable @State var sampleDataManager = {
        let container = try! ModelContainer(for: Note.self, configurations: ModelConfiguration(isStoredInMemoryOnly: true))
        return DataManager(modelContext: container.mainContext)
    }()

    NoteDetailView(note: sampleNote, dataManager: sampleDataManager)
}
