//
//  NoteCardView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData

struct NoteCardView: View {
    let note: Note
    let dataManager: DataManager
    
    @State private var showingDeleteAlert = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Compact header
            HStack {
                Text(note.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .lineLimit(1)

                Spacer()

                // Compact actions
                HStack(spacing: 8) {
                    But<PERSON>(action: {
                        toggleFavorite()
                    }) {
                        Image(systemName: note.isFavorite ? "heart.fill" : "heart")
                            .font(.system(size: 14))
                            .foregroundColor(note.isFavorite ? .yellow : .gray)
                    }

                    Button(action: {
                        showingDeleteAlert = true
                    }) {
                        Image(systemName: "trash")
                            .font(.system(size: 14))
                            .foregroundColor(.red)
                    }
                }
            }

            // More content preview with smaller font
            Text(note.content)
                .font(.caption)
                .foregroundColor(.gray)
                .lineLimit(6)
                .lineSpacing(1)
                .multilineTextAlignment(.leading)

            Spacer(minLength: 4)
            
            // Compact footer
            VStack(alignment: .leading, spacing: 4) {
                // Keywords in a more compact layout
                if !note.keywords.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 4) {
                            ForEach(Array(note.keywords.prefix(3)), id: \.id) { keyword in
                                HStack(spacing: 2) {
                                    Image(systemName: keyword.icon)
                                        .font(.system(size: 8))
                                        .foregroundColor(.white)

                                    Text(keyword.text)
                                        .font(.system(size: 9))
                                        .fontWeight(.medium)
                                        .foregroundColor(.white)
                                }
                                .padding(.horizontal, 4)
                                .padding(.vertical, 2)
                                .background(
                                    RoundedRectangle(cornerRadius: 6)
                                        .fill(Color(hex: keyword.color))
                                )
                            }

                            if note.keywords.count > 3 {
                                Text("+\(note.keywords.count - 3)")
                                    .font(.system(size: 9))
                                    .fontWeight(.medium)
                                    .foregroundColor(.gray)
                                    .padding(.horizontal, 4)
                                    .padding(.vertical, 2)
                                    .background(
                                        RoundedRectangle(cornerRadius: 6)
                                            .fill(Color.gray.opacity(0.3))
                                    )
                            }
                        }
                    }
                }

                // Bottom metadata row
                HStack {
                    Text(formatDate(note.createdAt))
                        .font(.system(size: 10))
                        .foregroundColor(.gray)

                    Spacer()

                    HStack(spacing: 4) {
                        if note.audioURL != nil {
                            HStack(spacing: 2) {
                                Image(systemName: "waveform")
                                    .font(.system(size: 9))
                                    .foregroundColor(.blue)

                                Text("Audio")
                                    .font(.system(size: 9))
                                    .foregroundColor(.blue)
                            }
                        }

                        if note.duration > 0 {
                            HStack(spacing: 2) {
                                Image(systemName: "clock")
                                    .font(.system(size: 9))
                                    .foregroundColor(.orange)

                                Text(formatDuration(note.duration))
                                    .font(.system(size: 9))
                                    .foregroundColor(.orange)
                            }
                        }
                    }
                }
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
        .frame(height: 180)
        .alert("Delete Note", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteNote()
            }
        } message: {
            Text("Are you sure you want to delete this note? This action cannot be undone.")
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        let calendar = Calendar.current
        
        if Calendar.current.isDateInToday(date) {
            formatter.timeStyle = .short
            return "Today, \(formatter.string(from: date))"
        } else if Calendar.current.isDateInYesterday(date) {
            formatter.timeStyle = .short
            return "Yesterday, \(formatter.string(from: date))"
        } else if calendar.dateInterval(of: .weekOfYear, for: Date())?.contains(date) == true {
            formatter.dateFormat = "EEEE, h:mm a"
            return formatter.string(from: date)
        } else {
            formatter.dateStyle = .short
            formatter.timeStyle = .short
            return formatter.string(from: date)
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        if duration == 0 {
            return "0s"
        }

        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        let seconds = Int(duration) % 60

        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else if minutes > 0 {
            return String(format: "%d:%02d", minutes, seconds)
        } else {
            return String(format: "%ds", seconds)
        }
    }
    
    private func toggleFavorite() {
        dataManager.toggleNoteFavorite(note)
    }
    
    private func deleteNote() {
        dataManager.deleteNote(note)
    }
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: Note.self, Keyword.self, UserPreferences.self, configurations: config)
    let context = container.mainContext

    // Create sample data
    let keyword1 = Keyword(text: "idea", color: "#FF6B6B")
    let keyword2 = Keyword(text: "work", color: "#4ECDC4")

    let note = Note(
        title: "Sample Note",
        content: "This is a sample note content that demonstrates how the note card will look in the grid view.",
        audioURL: "/path/to/audio.m4a"
    )
    note.keywords.append(keyword1)
    note.keywords.append(keyword2)
    note.duration = 125.5
    note.isFavorite = true

    context.insert(note)
    context.insert(keyword1)
    context.insert(keyword2)

    return NoteCardView(note: note, dataManager: DataManager(modelContext: context))
        .frame(width: 180, height: 180)
        .padding()
        .background(Color.black)
}
