//
//  SiriShortcutsView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import IntentsUI

struct SiriShortcutsView: View {
    @StateObject private var siriService = SiriShortcutsService.shared
    @State private var showingVoiceShortcutView = false
    @State private var selectedShortcut: SiriShortcut?
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.black.ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header
                    headerView
                    
                    ScrollView {
                        VStack(spacing: 20) {
                            // Info section
                            infoSection
                            
                            // Available shortcuts
                            shortcutsSection
                            
                            // Usage tips
                            tipsSection
                            
                            Spacer(minLength: 50)
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showingVoiceShortcutView) {
            if let shortcut = selectedShortcut {
                VoiceShortcutView(shortcut: shortcut)
            }
        }
    }
    
    private var headerView: some View {
        HStack {
            Button(action: { dismiss() }) {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .foregroundColor(.white)
            }
            
            Spacer()
            
            Text("Siri Shortcuts")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Spacer()
            
            // Placeholder for balance
            Color.clear
                .frame(width: 30)
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    private var infoSection: some View {
        VStack(spacing: 15) {
            // Siri icon
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.blue, .purple, .pink],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                
                Image(systemName: "waveform.circle.fill")
                    .font(.system(size: 40))
                    .foregroundColor(.white)
            }
            
            VStack(spacing: 8) {
                Text("Smart Siri Shortcuts")
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                Text("Create intelligent voice shortcuts that understand keywords and content. Say things like 'Hey Siri, record task: finish the report' or 'Hey Siri, add shopping note: buy milk'.")
                    .font(.body)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.gray.opacity(0.1))
        )
    }
    
    private var shortcutsSection: some View {
        VStack(spacing: 0) {
            HStack {
                Text("Available Shortcuts")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
            }
            .padding(.bottom, 15)
            
            VStack(spacing: 1) {
                ForEach(siriService.getAvailableShortcuts()) { shortcut in
                    ShortcutRowView(shortcut: shortcut) {
                        selectedShortcut = shortcut
                        showingVoiceShortcutView = true
                    }
                }
            }
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
            )
        }
    }
    
    private var tipsSection: some View {
        VStack(spacing: 15) {
            HStack {
                Text("Usage Tips")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
            }
            
            VStack(spacing: 12) {
                TipRowView(
                    icon: "1.circle.fill",
                    title: "Smart Phrases",
                    description: "Say 'keyword: content' like 'record task: buy groceries'"
                )

                TipRowView(
                    icon: "2.circle.fill",
                    title: "Natural Language",
                    description: "Use any keyword: work, shopping, ideas, reminders, etc."
                )

                TipRowView(
                    icon: "3.circle.fill",
                    title: "Works Everywhere",
                    description: "Use from lock screen, CarPlay, Apple Watch, or AirPods"
                )

                TipRowView(
                    icon: "4.circle.fill",
                    title: "Example Phrases",
                    description: "'Hey Siri, record shopping: milk and eggs' or 'add work note: meeting tomorrow'"
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.gray.opacity(0.1))
        )
    }
}

struct ShortcutRowView: View {
    let shortcut: SiriShortcut
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 15) {
                // Icon
                ZStack {
                    Circle()
                        .fill(Color.blue.opacity(0.2))
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: shortcut.iconName)
                        .font(.system(size: 18))
                        .foregroundColor(.blue)
                }
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(shortcut.title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                    
                    Text(shortcut.subtitle)
                        .font(.caption)
                        .foregroundColor(.gray)
                    
                    Text("Say: \"\(shortcut.phrase)\"")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
                
                Spacer()
                
                // Add button
                ZStack {
                    Circle()
                        .fill(Color.blue)
                        .frame(width: 30, height: 30)
                    
                    Image(systemName: "plus")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct TipRowView: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(.blue)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.gray)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            Spacer()
        }
    }
}

#Preview {
    SiriShortcutsView()
}
