//
//  AddNoteIntent.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import AppIntents
import SwiftData

@available(iOS 16.0, *)
struct AddNoteIntent: AppIntent {
    static var title: LocalizedStringResource = "Add Note with Keyword"
    static var description = IntentDescription("Create a new note with a specific keyword category")
    static var openAppWhenRun: Bool = true

    @Parameter(title: "Keyword Category", description: "The category or keyword for this note")
    var keyword: String

    @Parameter(title: "Note Content", description: "The content of the note")
    var content: String

    static var parameterSummary: some ParameterSummary {
        Summary("Add note with keyword \(\.$keyword): \(\.$content)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        print("🎯 AddNoteIntent called with keyword: '\(keyword)', content: '\(content)'")

        // Create note through notification system
        await MainActor.run {
            NotificationCenter.default.post(
                name: .siriCreateNoteWithKeyword,
                object: nil,
                userInfo: [
                    "keyword": keyword,
                    "content": content
                ]
            )
        }

        return .result(dialog: "Created note with keyword \(keyword)")
    }
}

@available(iOS 16.0, *)
struct QuickRecordIntent: AppIntent {
    static var title: LocalizedStringResource = "Quick Voice Recording"
    static var description = IntentDescription("Start voice recording with optional keyword")
    static var openAppWhenRun: Bool = true

    @Parameter(title: "Keyword", description: "Optional keyword for categorizing the recording")
    var keyword: String?

    static var parameterSummary: some ParameterSummary {
        Summary("Start recording") {
            \.$keyword
        }
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        print("🎯 QuickRecordIntent called with keyword: '\(keyword ?? "none")'")

        // Trigger recording through notification system
        await MainActor.run {
            NotificationCenter.default.post(
                name: .siriStartRecordingWithKeyword,
                object: nil,
                userInfo: keyword != nil ? ["keyword": keyword!] : [:]
            )
        }

        if let keyword = keyword {
            return .result(dialog: "Started recording for \(keyword)")
        } else {
            return .result(dialog: "Started recording")
        }
    }
}

@available(iOS 16.0, *)
struct SmartNoteIntent: AppIntent {
    static var title: LocalizedStringResource = "Smart Note"
    static var description = IntentDescription("Create a note by speaking naturally: 'keyword: content'")
    static var openAppWhenRun: Bool = true

    @Parameter(title: "Voice Input", description: "Say your keyword and content naturally")
    var voiceInput: String

    static var parameterSummary: some ParameterSummary {
        Summary("Smart note: \(\.$voiceInput)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        print("🎯 SmartNoteIntent called with input: '\(voiceInput)'")

        // Parse the voice input to extract keyword and content
        let (keyword, content) = parseVoiceInput(voiceInput)

        // Create note through notification system
        await MainActor.run {
            NotificationCenter.default.post(
                name: .siriCreateNoteWithKeyword,
                object: nil,
                userInfo: [
                    "keyword": keyword,
                    "content": content
                ]
            )
        }

        return .result(dialog: "Created \(keyword) note: \(content)")
    }
    
    private func parseVoiceInput(_ input: String) -> (keyword: String, content: String) {
        // Try to parse patterns like "keyword: content" or "keyword, content"
        let separators = [":", ",", " "]
        
        for separator in separators {
            if let separatorRange = input.range(of: separator) {
                let keyword = String(input[..<separatorRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
                let content = String(input[separatorRange.upperBound...]).trimmingCharacters(in: .whitespacesAndNewlines)
                
                if !keyword.isEmpty && !content.isEmpty {
                    return (keyword: keyword, content: content)
                }
            }
        }
        
        // If no clear separation, use first word as keyword and rest as content
        let words = input.components(separatedBy: .whitespaces).filter { !$0.isEmpty }
        if words.count >= 2 {
            let keyword = words[0]
            let content = words[1...].joined(separator: " ")
            return (keyword: keyword, content: content)
        }
        
        // Fallback: use "Note" as keyword and full input as content
        return (keyword: "Note", content: input)
    }
}

// MARK: - App Shortcuts Provider

@available(iOS 16.0, *)
struct EchoNoteShortcuts: AppShortcutsProvider {
    static var appShortcuts: [AppShortcut] {
        AppShortcut(
            intent: SmartNoteIntent(),
            phrases: [
                "Record \(.applicationName)",
                "Quick note with \(.applicationName)",
                "Add note to \(.applicationName)",
                "Create note in \(.applicationName)"
            ],
            shortTitle: "Smart Note",
            systemImageName: "note.text"
        )
        
        AppShortcut(
            intent: QuickRecordIntent(),
            phrases: [
                "Start recording in \(.applicationName)",
                "Begin voice memo with \(.applicationName)",
                "Record voice note in \(.applicationName)"
            ],
            shortTitle: "Quick Record",
            systemImageName: "mic.fill"
        )
        
        AppShortcut(
            intent: AddNoteIntent(),
            phrases: [
                "Add note to \(.applicationName)",
                "Create note in \(.applicationName)"
            ],
            shortTitle: "Add Note",
            systemImageName: "plus.circle"
        )
    }
}
