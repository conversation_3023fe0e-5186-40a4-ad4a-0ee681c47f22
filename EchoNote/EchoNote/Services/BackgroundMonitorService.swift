//
//  BackgroundMonitorService.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import Speech
import AVFoundation
import UIKit
import SwiftData

class BackgroundMonitorService: NSObject, ObservableObject {
    static let shared = BackgroundMonitorService()
    
    @Published var isMonitoring = false
    @Published var isStarting = false  // 新增：正在启动状态
    @Published var lastError: String?  // 新增：最后的错误信息
    @Published var detectedKeywords: [String] = []
    @Published var isRecordingAfterKeyword = false
    @Published var keywordDetectionEnabled = true

    // SIMPLIFIED: Session state management
    private var activeSessionKeyword: String?  // 当前活跃会话的关键词
    private var sessionInProgress = false  // 是否有会话正在进行

    // Cooldown mechanism to prevent immediate re-triggering
    private var lastRecordingEndTime: Date?
    private let cooldownPeriod: TimeInterval = 2.0 // 2 seconds cooldown after recording ends

    // App lifecycle management
    private var wasMonitoringBeforeBackground = false

    // Health check timer
    private var healthCheckTimer: Timer?
    private var lastSpeechRecognitionTime: Date?

    // Startup timeout detection
    private var startupTimer: Timer?
    private let startupTimeout: TimeInterval = 10.0  // 10 seconds timeout

    // Speech recognition quality settings (optimized for background mode)
    private let recognitionConfidenceThreshold: Float = 0.05  // Lower for background mode
    private let useOnDeviceRecognition = false  // Disabled for background stability

    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioEngine = AVAudioEngine()

    private var dataManager: DataManager?
    private var keywords: [Keyword] = []
    private var accumulatedText = ""
    private var lastKeywordDetectionTime = Date()
    private var keywordDetectedText = ""
    private var recordingTimer: Timer?
    private var silenceTimer: Timer?
    private var keywordDetectionPoint = 0 // Character position where keyword was detected
    private var lastSpeechTime = Date()
    private var recordingStartTime: Date?
    private var lastTextLength = 0

    // User configurable settings
    private var autoStopDuration: TimeInterval = 3.0 // Default 3 seconds of silence
    private var maxRecordingDuration: TimeInterval = 60.0 // Default 1 minute maximum
    
    private override init() {
        super.init()
        setupSpeechRecognizer()
        setupAppLifecycleObservers()
    }

    private func setupAppLifecycleObservers() {
        // Listen for app going to background
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )

        // Listen for app returning to foreground
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )

        // Listen for app becoming active
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )

        // CRITICAL: Listen for audio session interruptions
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(audioSessionInterrupted),
            name: AVAudioSession.interruptionNotification,
            object: nil
        )
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    @objc private func appDidEnterBackground() {
        print("📱 App entered background - saving monitoring state")
        wasMonitoringBeforeBackground = isMonitoring

        if isMonitoring {
            print("🔄 Stopping monitoring due to background transition")
            stopMonitoring()
        }
    }

    @objc private func appWillEnterForeground() {
        print("📱 App will enter foreground - preparing to restore monitoring")
        // Don't restart immediately, wait for app to become active
    }

    @objc private func appDidBecomeActive() {
        print("📱 App became active")

        if wasMonitoringBeforeBackground {
            print("🔄 Restoring background monitoring after app became active")
            wasMonitoringBeforeBackground = false

            // Add a small delay to ensure the app is fully active
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.restartMonitoringAfterBackground()
            }
        } else {
            print("ℹ️ Background monitoring was not active before background, no need to restore")
        }
    }

    private func restartMonitoringAfterBackground() {
        print("🔄 Attempting to restart background monitoring after app lifecycle event")

        // Ensure we have fresh user settings
        loadUserSettings()

        // Restart monitoring
        startMonitoring()
    }

    // MARK: - Testing Methods

    func testCooldownMechanism() {
        print("🧪 Testing cooldown mechanism")

        // Simulate recording end
        lastRecordingEndTime = Date()
        print("🧪 Set cooldown start time")

        // Test immediate keyword detection (should be blocked)
        checkForKeywords(in: "工作 测试内容")

        // Test after cooldown period
        DispatchQueue.main.asyncAfter(deadline: .now() + cooldownPeriod + 0.1) {
            print("🧪 Testing keyword detection after cooldown period")
            self.checkForKeywords(in: "会议 测试内容")
        }
    }

    func simulateSpeechRecognition(text: String) {
        print("🧪 Simulating speech recognition: '\(text)'")

        // Create a mock result
        accumulatedText = text

        if isRecordingAfterKeyword {
            print("📝 Recording after keyword (total): \(text)")
            print("📏 Text length: \(text.count), Detection point: \(keywordDetectionPoint)")
            print("🚫 Keyword detection disabled - ignoring any new keywords in text")
        } else {
            print("👂 Normal monitoring mode - checking for keywords")
            checkForKeywords(in: text)
        }
    }

    private func restartSpeechRecognition() {
        print("🔄 Restarting speech recognition service...")

        // Stop current recognition completely
        stopSpeechRecognition()

        // Wait longer to ensure complete cleanup before restart
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            if self.isMonitoring {
                print("🔄 Attempting to restart speech recognition after cleanup delay")

                // Double-check that audio engine is completely stopped
                if self.audioEngine.isRunning {
                    print("⚠️ Audio engine still running during restart - forcing stop")
                    self.audioEngine.stop()

                    // Wait a bit more if engine was still running
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        if self.isMonitoring {
                            self.startSpeechRecognition()
                        }
                    }
                } else {
                    self.startSpeechRecognition()
                }
            }
        }
    }

    private func stopSpeechRecognition() {
        print("🛑 Stopping speech recognition")

        // Cancel recognition task
        if let task = recognitionTask {
            print("🛑 Canceling recognition task")
            task.cancel()
            recognitionTask = nil
        }

        // End recognition request
        if let request = recognitionRequest {
            print("🛑 Ending recognition request")
            request.endAudio()
            recognitionRequest = nil
        }

        // Stop audio engine safely
        if audioEngine.isRunning {
            print("🛑 Stopping audio engine")
            audioEngine.stop()
        }

        // CRITICAL: Always remove tap, even if engine isn't running
        let inputNode = audioEngine.inputNode
        if inputNode.numberOfInputs > 0 {
            print("🧹 Removing audio tap from input node")
            inputNode.removeTap(onBus: 0)
        }

        print("✅ Speech recognition stopped and cleaned up")

        // Don't set isMonitoring to false here - we want to restart
    }

    private func startHealthCheckTimer() {
        healthCheckTimer?.invalidate()

        healthCheckTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.performHealthCheck()
        }

        print("🏥 Health check timer started - checking every 30 seconds")
    }

    private func performHealthCheck() {
        guard isMonitoring else { return }

        // Check if we haven't received any speech recognition results for a while
        if let lastTime = lastSpeechRecognitionTime {
            let timeSinceLastRecognition = Date().timeIntervalSince(lastTime)
            if timeSinceLastRecognition > 60.0 { // 1 minute without any recognition
                print("⚠️ No speech recognition activity for \(Int(timeSinceLastRecognition))s - checking service health")

                // Check if speech recognizer is still available
                guard let speechRecognizer = speechRecognizer,
                      speechRecognizer.isAvailable else {
                    print("❌ Speech recognizer is no longer available - restarting")
                    restartSpeechRecognition()
                    return
                }

                // Check if audio engine is still running
                if !audioEngine.isRunning {
                    print("❌ Audio engine stopped running - restarting")
                    restartSpeechRecognition()
                    return
                }

                print("✅ Speech recognition service appears healthy")
            }
        } else {
            // First time - set the timestamp
            lastSpeechRecognitionTime = Date()
        }
    }

    private func handleStartupTimeout() {
        print("⏰ Startup timeout reached - service failed to start within \(startupTimeout) seconds")

        DispatchQueue.main.async {
            self.isStarting = false
            self.lastError = "Service startup timeout after \(Int(self.startupTimeout)) seconds"

            // Force UI update to reflect error state
            self.objectWillChange.send()
        }

        print("❌ Startup failed - isStarting = false, error recorded")
    }
    
    func setDataManager(_ dataManager: DataManager) {
        self.dataManager = dataManager
        loadKeywords()
        loadUserSettings()
    }

    private func loadUserSettings() {
        guard let dataManager = dataManager else { return }

        Task { @MainActor in
            let preferences = dataManager.getUserPreferences()

            if let prefs = preferences {
                self.autoStopDuration = prefs.autoStopDuration
                self.maxRecordingDuration = prefs.maxRecordingDuration
            }

            print("📋 Loaded user settings:")
            print("   - Auto stop after: \(self.autoStopDuration) seconds of silence")
            print("   - Max recording duration: \(self.maxRecordingDuration) seconds")
        }
    }
    
    private func setupSpeechRecognizer() {
        // CRITICAL: Configure speech recognizer for optimal quality (same as RecordingView)
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "en-US"))

        guard let speechRecognizer = speechRecognizer else {
            print("❌ Failed to create speech recognizer")
            return
        }

        // CRITICAL: Enable on-device recognition hint for better quality
        if speechRecognizer.supportsOnDeviceRecognition {
            speechRecognizer.defaultTaskHint = .dictation
            print("✅ Speech recognizer configured with .dictation hint for optimal quality")
        }

        speechRecognizer.delegate = self
        print("✅ Speech recognizer setup completed with professional-grade configuration")
    }
    
    private func loadKeywords() {
        guard let dataManager = dataManager else { return }
        Task { @MainActor in
            keywords = dataManager.getAllKeywords().filter { $0.isEnabled }
        }
    }
    
    func startMonitoring() {
        guard !isMonitoring && !isStarting else {
            print("⚠️ Already monitoring or starting, skipping start")
            return
        }

        print("🎯 BackgroundMonitorService.startMonitoring() called")
        print("📊 DataManager available: \(dataManager != nil)")
        print("🔑 Keywords loaded: \(keywords.count)")

        // Set starting state immediately
        isStarting = true
        lastError = nil  // Clear previous errors
        print("🔄 Setting isStarting = true")

        // Start timeout timer
        startupTimer?.invalidate()
        startupTimer = Timer.scheduledTimer(withTimeInterval: startupTimeout, repeats: false) { [weak self] _ in
            self?.handleStartupTimeout()
        }

        // Debug: Print all loaded keywords
        if keywords.isEmpty {
            print("❌ WARNING: No keywords loaded!")
        } else {
            print("📝 Available keywords:")
            for keyword in keywords {
                print("   - '\(keyword.text)' (enabled: \(keyword.isEnabled))")
            }
        }

        // Request permissions
        requestPermissions { [weak self] granted in
            print("🔐 Permissions granted: \(granted)")
            if granted {
                DispatchQueue.main.async {
                    self?.startSpeechRecognition()
                }
            } else {
                print("❌ Permissions denied - cannot start monitoring")
                DispatchQueue.main.async {
                    self?.startupTimer?.invalidate()  // Cancel timeout timer
                    self?.isStarting = false
                    self?.lastError = "Microphone or speech recognition permission denied"
                    self?.objectWillChange.send()
                }
            }
        }
    }
    
    func stopMonitoring() {
        guard isMonitoring else { return }

        print("🛑 Stopping background monitoring")

        // Stop any ongoing timers
        recordingTimer?.invalidate()
        recordingTimer = nil
        silenceTimer?.invalidate()
        silenceTimer = nil
        healthCheckTimer?.invalidate()
        healthCheckTimer = nil
        startupTimer?.invalidate()  // Also cancel startup timer
        startupTimer = nil

        // Cancel recognition task safely
        if let task = recognitionTask {
            print("🛑 Canceling recognition task")
            task.cancel()
            recognitionTask = nil
        }

        // End recognition request safely
        if let request = recognitionRequest {
            print("🛑 Ending recognition request")
            request.endAudio()
            recognitionRequest = nil
        }

        // Stop audio engine safely
        if audioEngine.isRunning {
            print("🛑 Stopping audio engine")
            audioEngine.stop()
        }

        // CRITICAL: Always remove tap to prevent crashes
        let inputNode = audioEngine.inputNode
        if inputNode.numberOfInputs > 0 {
            print("🧹 Removing audio tap from input node")
            inputNode.removeTap(onBus: 0)
        }

        // Deactivate audio session
        do {
            try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
            print("🎧 Audio session deactivated")
        } catch {
            print("⚠️ Failed to deactivate audio session: \(error)")
        }

        isMonitoring = false
        isStarting = false  // Reset starting state
        isRecordingAfterKeyword = false
        keywordDetectionEnabled = true  // Reset to enabled state
        lastRecordingEndTime = nil  // Clear cooldown
        accumulatedText = ""
        keywordDetectedText = ""
        detectedKeywords.removeAll()
        lastSpeechRecognitionTime = nil

        print("✅ isStarting = false, isMonitoring = false")
    }
    
    private func requestPermissions(completion: @escaping (Bool) -> Void) {
        // Request speech recognition permission
        SFSpeechRecognizer.requestAuthorization { authStatus in
            switch authStatus {
            case .authorized:
                // Request microphone permission
                if #available(iOS 17.0, *) {
                    AVAudioApplication.requestRecordPermission { granted in
                        completion(granted)
                    }
                } else {
                    AVAudioSession.sharedInstance().requestRecordPermission { granted in
                        completion(granted)
                    }
                }
            default:
                completion(false)
            }
        }
    }
    
    private func startSpeechRecognition() {
        guard let speechRecognizer = speechRecognizer,
              speechRecognizer.isAvailable else {
            print("❌ Speech recognizer not available")
            return
        }

        print("🎤 Starting BACKGROUND-OPTIMIZED speech recognition")
        print("📋 Loaded \(keywords.count) keywords for detection")
        print("🔧 Background mode optimizations:")
        print("   - Lower confidence threshold: \(recognitionConfidenceThreshold)")
        print("   - Larger audio buffer: 2048 frames")
        print("   - Search task hint for keywords")
        print("   - Cloud-based recognition for stability")

        do {
            // CRITICAL: Use AudioManager's proven approach

            // Step 1: Clean shutdown of existing components (AudioManager style)
            if audioEngine.isRunning {
                audioEngine.stop()
                audioEngine.inputNode.removeTap(onBus: 0)
                print("🧹 Stopped existing audio engine")
            }

            // Step 2: Configure audio session for BACKGROUND MODE optimization
            let audioSession = AVAudioSession.sharedInstance()

            // CRITICAL: Use .record category for background monitoring (not .playAndRecord)
            try audioSession.setCategory(.record, mode: .measurement, options: [.allowBluetooth, .duckOthers])

            // CRITICAL: Query actual hardware capabilities and use them
            let actualSampleRate = audioSession.sampleRate
            let preferredRate = actualSampleRate > 0 ? actualSampleRate : 48000.0

            print("🔍 BACKGROUND MODE Audio Analysis:")
            print("   - Hardware sample rate: \(actualSampleRate) Hz")
            print("   - Using rate: \(preferredRate) Hz")

            // Don't force sample rate - use what hardware provides
            // try audioSession.setPreferredSampleRate(preferredRate)

            // Use longer buffer for background stability
            try audioSession.setPreferredIOBufferDuration(0.02)  // 20ms for stability

            // CRITICAL: Add delay and error handling for background transitions
            do {
                try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            } catch {
                // If activation fails, wait and retry once
                print("⚠️ Audio session activation failed, retrying in 0.5s: \(error)")
                Thread.sleep(forTimeInterval: 0.5)
                try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            }

            print("🎧 Audio session configured for BACKGROUND MODE")
            print("   - Category: .record (background optimized)")
            print("   - Mode: .measurement (low latency)")
            print("   - Sample Rate: \(preferredRate) Hz (hardware native)")
            print("   - Buffer Duration: 0.02s (background stable)")

            // Step 4: Create recognition request optimized for BACKGROUND MODE
            recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
            guard let recognitionRequest = recognitionRequest else {
                print("❌ Failed to create recognition request")
                return
            }

            // Step 5: Configure request for BACKGROUND MODE optimization
            recognitionRequest.shouldReportPartialResults = true

            // CRITICAL: Use .search hint for better keyword detection (not .dictation)
            recognitionRequest.taskHint = .search

            // CRITICAL: Disable on-device recognition for background stability
            recognitionRequest.requiresOnDeviceRecognition = false

            print("✅ Recognition request created for BACKGROUND MODE")
            print("   - Task Hint: .search (keyword optimized)")
            print("   - On-device: disabled (background stable)")
            print("   - Partial Results: enabled")

            // Step 7: Configure audio engine for BACKGROUND MODE
            let inputNode = audioEngine.inputNode
            let hardwareFormat = inputNode.outputFormat(forBus: 0)

            // CRITICAL: Validate hardware format before proceeding
            guard hardwareFormat.sampleRate > 0 && hardwareFormat.channelCount > 0 else {
                throw NSError(domain: "AudioEngine", code: -3, userInfo: [NSLocalizedDescriptionKey: "Invalid hardware audio format: sampleRate=\(hardwareFormat.sampleRate), channels=\(hardwareFormat.channelCount)"])
            }

            // Remove any existing tap
            inputNode.removeTap(onBus: 0)

            print("🎙️ Installing BACKGROUND-OPTIMIZED audio tap")
            print("   - Hardware Format: \(hardwareFormat)")
            print("   - Sample Rate: \(hardwareFormat.sampleRate) Hz")
            print("   - Channels: \(hardwareFormat.channelCount)")
            print("   - Format: \(hardwareFormat.commonFormat)")
            print("   - Buffer Size: 2048 frames (background stable)")

            // Step 8: Install tap with BACKGROUND-OPTIMIZED configuration
            // Use larger buffer for background stability
            inputNode.installTap(onBus: 0, bufferSize: 2048, format: hardwareFormat) { buffer, _ in
                recognitionRequest.append(buffer)
            }

            audioEngine.prepare()
            try audioEngine.start()
            print("✅ Audio engine started with AudioManager configuration")

            // Step 9: Start recognition task with AudioManager's approach
            recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
                guard let self = self else { return }

                // Handle errors first (AudioManager style)
                if let error = error {
                    // Check if this is a "No speech detected" error and we're already recording
                    let nsError = error as NSError
                    if nsError.domain == "kAFAssistantErrorDomain" && nsError.code == 1110 && self.isRecordingAfterKeyword {
                        return
                    }

                    print("❌ Speech recognition error: \(error.localizedDescription)")

                    // Handle specific error codes like AudioManager
                    if nsError.domain == "kAFAssistantErrorDomain" && nsError.code == 1101 {
                        print("🔄 Speech recognition service unavailable - attempting restart")
                        self.restartSpeechRecognition()
                    } else if error.localizedDescription.contains("canceled") {
                        print("🔄 Speech recognition was canceled - attempting restart")
                        self.restartSpeechRecognition()
                    }
                    return
                }

                // Process results (AudioManager style)
                if let result = result {
                    let transcription = result.bestTranscription.formattedString

                    // Get confidence score like AudioManager
                    let confidence = result.bestTranscription.segments.first?.confidence ?? 0.0

                    print("🎤 Speech recognized: '\(transcription)' (final: \(result.isFinal), confidence: \(String(format: "%.2f", confidence)))")

                    // PROFESSIONAL SOLUTION: Use content quality assessment instead of confidence
                    let shouldAcceptResult = self.shouldAcceptSpeechResult(
                        transcription: transcription,
                        confidence: confidence,
                        isFinal: result.isFinal
                    )

                    if shouldAcceptResult {
                        print("✅ Speech result accepted (quality-based assessment)")

                        // Update accumulated text
                        self.accumulatedText = transcription
                        self.lastSpeechRecognitionTime = Date()

                        // Process based on current state
                        if self.isRecordingAfterKeyword {
                            self.handleRecordingModeResult(transcription: transcription, result: result)
                        } else {
                            self.handleListeningModeResult(transcription: transcription, result: result)
                        }
                    } else {
                        print("🚫 Speech result rejected (quality-based assessment)")
                    }
                }
            }
            print("✅ Recognition task created with AudioManager approach")

            // Cancel startup timeout timer
            startupTimer?.invalidate()
            startupTimer = nil

            isMonitoring = true
            isStarting = false  // Clear starting state
            lastError = nil     // Clear any previous errors
            loadKeywords() // Refresh keywords when starting
            print("🎯 Background monitoring is now ACTIVE - listening for keywords")
            print("✅ isStarting = false, isMonitoring = true")

            // Start health check timer
            startHealthCheckTimer()

            // Force UI update
            DispatchQueue.main.async {
                self.objectWillChange.send()
            }

        } catch {
            print("❌ Speech recognition setup failed: \(error.localizedDescription)")

            // Handle startup failure
            startupTimer?.invalidate()
            startupTimer = nil

            DispatchQueue.main.async {
                self.isStarting = false
                self.isMonitoring = false
                self.lastError = "Speech recognition setup failed: \(error.localizedDescription)"
                self.objectWillChange.send()
            }
        }
    }

    // MARK: - Professional Content Quality Assessment

    private func shouldAcceptSpeechResult(transcription: String, confidence: Float, isFinal: Bool) -> Bool {
        // STRATEGY 1: If confidence is good, always accept
        if confidence >= recognitionConfidenceThreshold {
            print("✅ High confidence (\(String(format: "%.2f", confidence))) - accepted")
            return true
        }

        // STRATEGY 2: Content quality assessment for low/zero confidence
        let contentQuality = assessContentQuality(transcription)

        print("🔍 Content quality assessment:")
        print("   - Transcription: '\(transcription.prefix(50))...'")
        print("   - Confidence: \(String(format: "%.2f", confidence))")
        print("   - Content quality score: \(contentQuality)")
        print("   - Is final: \(isFinal)")

        // STRATEGY 3: Accept based on content quality
        if contentQuality >= 0.7 {
            print("✅ High content quality (\(String(format: "%.2f", contentQuality))) - accepted despite low confidence")
            return true
        }

        // STRATEGY 4: More lenient for final results
        if isFinal && contentQuality >= 0.5 {
            print("✅ Final result with decent quality (\(String(format: "%.2f", contentQuality))) - accepted")
            return true
        }

        // STRATEGY 5: Check for keywords in zero-confidence results
        if confidence == 0.0 && containsKeywords(transcription) {
            print("✅ Contains keywords despite zero confidence - accepted")
            return true
        }

        print("❌ Low quality content - rejected")
        return false
    }

    private func assessContentQuality(_ text: String) -> Float {
        guard !text.isEmpty else { return 0.0 }

        var score: Float = 0.0
        let words = text.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }

        // Factor 1: Word count (longer = better quality)
        let wordCountScore = min(Float(words.count) / 10.0, 1.0) * 0.3

        // Factor 2: Average word length (reasonable length = better quality)
        let avgWordLength = words.reduce(0) { $0 + $1.count } / max(words.count, 1)
        let wordLengthScore: Float = (avgWordLength >= 3 && avgWordLength <= 8) ? 0.2 : 0.1

        // Factor 3: Contains real words (not gibberish)
        let realWordsCount = words.filter { word in
            word.count >= 2 && word.allSatisfy { $0.isLetter }
        }.count
        let realWordsScore = Float(realWordsCount) / Float(max(words.count, 1)) * 0.3

        // Factor 4: Contains keywords
        let keywordScore: Float = containsKeywords(text) ? 0.2 : 0.0

        score = wordCountScore + wordLengthScore + realWordsScore + keywordScore

        return min(score, 1.0)
    }

    private func containsKeywords(_ text: String) -> Bool {
        let lowercaseText = text.lowercased()
        return keywords.contains { keyword in
            keyword.isEnabled && lowercaseText.contains(keyword.text.lowercased())
        }
    }

    // MARK: - AudioManager-style Result Handlers

    private func handleListeningModeResult(transcription: String, result: SFSpeechRecognitionResult) {
        print("👂 Normal monitoring mode - checking for keywords")
        checkForKeywords(in: transcription)

        // If result is final and we're not in extended recording, reset
        if result.isFinal && !isRecordingAfterKeyword {
            accumulatedText = ""
        }
    }

    private func handleRecordingModeResult(transcription: String, result: SFSpeechRecognitionResult) {
        // We're in recording mode - just update the accumulated text
        print("📝 Recording: \(transcription.count) characters")

        // Check if we have new speech content
        if transcription.count > lastTextLength {
            lastSpeechTime = Date()
            lastTextLength = transcription.count
            startSilenceDetection()
        }

        // Check recording duration limits
        if let startTime = recordingStartTime {
            let recordingElapsed = Date().timeIntervalSince(startTime)
            if recordingElapsed > maxRecordingDuration * 0.8 {
                let remaining = maxRecordingDuration - recordingElapsed
                print("⚠️ Recording will auto-stop in \(String(format: "%.1f", remaining)) seconds")
            }
        }
    }
    
    // Old handleRecognitionResult method removed - now using AudioManager-style inline handling
    
    private func checkForKeywords(in text: String) {
        // FIRST CHECK: Is keyword detection globally enabled?
        guard keywordDetectionEnabled else {
            print("🚫 Keyword detection is DISABLED - ignoring all keywords")
            return
        }

        // SECOND CHECK: Are we already recording after a keyword?
        guard !isRecordingAfterKeyword else {
            print("🔇 Skipping keyword detection - already recording after keyword")
            return
        }

        // THIRD CHECK: Are we in cooldown period after previous recording?
        if let lastEndTime = lastRecordingEndTime {
            let timeSinceLastRecording = Date().timeIntervalSince(lastEndTime)
            if timeSinceLastRecording < cooldownPeriod {
                let remaining = cooldownPeriod - timeSinceLastRecording
                print("❄️ In cooldown period - \(String(format: "%.1f", remaining))s remaining before next keyword detection")
                return
            } else {
                print("✅ Cooldown period ended - keyword detection fully active")
                lastRecordingEndTime = nil // Clear the cooldown
            }
        }

        print("🔍 Checking for keywords in: \(text)")

        let lowercaseText = text.lowercased()
        var foundKeywords: [String] = []

        for keyword in keywords {
            if lowercaseText.contains(keyword.text.lowercased()) {
                foundKeywords.append(keyword.text)
                print("✅ Found keyword: \(keyword.text)")
            }
        }

        if !foundKeywords.isEmpty {
            let selectedKeyword = foundKeywords.first!
            startNewSession(with: selectedKeyword)
        }
    }

    // MARK: - SIMPLIFIED Session Management

    private func startNewSession(with keyword: String) {
        print("🚀 STARTING NEW SESSION with keyword: '\(keyword)'")

        // Clear any previous state
        activeSessionKeyword = keyword
        sessionInProgress = true
        detectedKeywords = [keyword]
        accumulatedText = ""

        // Disable keyword detection during session
        keywordDetectionEnabled = false

        // Start recording
        DispatchQueue.main.async {
            self.lastKeywordDetectionTime = Date()
            self.keywordDetectedText = ""
            self.startRecordingAfterKeyword()
        }
    }

    private func endCurrentSession() {
        print("🏁 ENDING CURRENT SESSION")

        guard sessionInProgress, let keyword = activeSessionKeyword else {
            print("⚠️ No active session to end")
            return
        }

        // Create note IMMEDIATELY and SYNCHRONOUSLY
        createNoteImmediately(keyword: keyword, content: accumulatedText)

        // Clear session state
        activeSessionKeyword = nil
        sessionInProgress = false
        detectedKeywords.removeAll()
        accumulatedText = ""

        // Re-enable keyword detection after cooldown
        lastRecordingEndTime = Date()

        DispatchQueue.main.asyncAfter(deadline: .now() + cooldownPeriod) {
            self.keywordDetectionEnabled = true
            print("✅ Keyword detection re-enabled after cooldown")
        }
    }

    private func createNoteImmediately(keyword: String, content: String) {
        print("📝 CREATING NOTE IMMEDIATELY")
        print("   - Keyword: '\(keyword)'")
        print("   - Content length: \(content.count)")

        guard let dataManager = dataManager,
              let keywordObject = keywords.first(where: { $0.text.lowercased() == keyword.lowercased() }),
              !content.isEmpty else {
            print("❌ Cannot create note - missing requirements")
            return
        }

        // Calculate recording duration
        let recordingDuration = recordingStartTime.map { Date().timeIntervalSince($0) } ?? 0.0
        print("   - Recording duration: \(String(format: "%.1f", recordingDuration)) seconds")

        // Extract content after keyword
        let cleanContent = extractContentAfterKeyword(keyword: keyword, content: content)

        // Generate title
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .short)
        let title = "\(keyword) - \(timestamp)"

        // Create note SYNCHRONOUSLY on main thread
        DispatchQueue.main.async {
            let note = dataManager.createNote(
                title: title,
                content: cleanContent.isEmpty ? "Voice note created automatically" : cleanContent,
                keywords: [keywordObject]
            )

            // Set the recording duration
            if recordingDuration > 0 {
                note.duration = recordingDuration
                print("✅ NOTE CREATED: '\(note.title)' with duration: \(String(format: "%.1f", recordingDuration))s")
            } else {
                print("✅ NOTE CREATED: '\(note.title)' (no duration recorded)")
            }
        }
    }

    private func extractContentAfterKeyword(keyword: String, content: String) -> String {
        let keywordLower = keyword.lowercased()
        let contentLower = content.lowercased()

        if let keywordRange = contentLower.range(of: keywordLower) {
            let afterKeywordIndex = keywordRange.upperBound
            if afterKeywordIndex < content.endIndex {
                return String(content[afterKeywordIndex...]).trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }

        return content
    }

    private func startRecordingAfterKeyword() {
        print("🎯 FIRST keyword detected! Starting smart recording...")
        print("🔒 Keyword detection is now COMPLETELY DISABLED")
        print("📍 Current accumulated text: \(accumulatedText)")
        print("🏷️ Triggered by keyword(s): \(detectedKeywords)")
        print("⚙️ Recording settings:")
        print("   - Auto stop after: \(autoStopDuration) seconds of silence")
        print("   - Maximum duration: \(maxRecordingDuration) seconds")

        // DOUBLE LOCK: Set both flags to prevent any keyword detection
        isRecordingAfterKeyword = true
        keywordDetectionEnabled = false

        // Mark the current position as the keyword detection point
        keywordDetectionPoint = accumulatedText.count
        keywordDetectedText = accumulatedText

        // Record start time for maximum duration check
        recordingStartTime = Date()
        lastSpeechTime = Date()
        lastTextLength = accumulatedText.count

        // Start maximum duration timer (GUARANTEED STOP)
        recordingTimer?.invalidate()
        recordingTimer = Timer.scheduledTimer(withTimeInterval: maxRecordingDuration, repeats: false) { [weak self] _ in
            print("⏰ MAXIMUM RECORDING DURATION (\(self?.maxRecordingDuration ?? 0)s) REACHED - FORCE STOPPING")
            print("🛑 This is a GUARANTEED stop - recording will end now")
            self?.finishRecordingAfterKeyword()
        }

        print("⏰ Maximum duration timer set for \(maxRecordingDuration) seconds - GUARANTEED STOP")

        // Verify timer was created successfully
        if recordingTimer != nil {
            print("✅ Maximum duration timer CONFIRMED active")
        } else {
            print("❌ ERROR: Maximum duration timer failed to start!")
        }

        // Start silence detection timer
        startSilenceDetection()

        print("🎙️ Smart recording started - will stop on silence or max duration")
        print("🚫 NO MORE KEYWORDS will be detected until this session ends")
        print("📋 RECORDING GUARANTEES:")
        print("   ✅ Will stop after \(maxRecordingDuration) seconds (MAXIMUM)")
        print("   ✅ Will stop after \(autoStopDuration) seconds of silence")
    }

    private func startSilenceDetection() {
        silenceTimer?.invalidate()
        silenceTimer = Timer.scheduledTimer(withTimeInterval: autoStopDuration, repeats: false) { [weak self] _ in
            print("🔇 Silence detected for \(self?.autoStopDuration ?? 0) seconds - stopping recording")
            self?.finishRecordingAfterKeyword()
        }
    }

    private func resetSilenceDetection() {
        lastSpeechTime = Date()
        startSilenceDetection()
    }

    private func finishRecordingAfterKeyword() {
        let recordingElapsed = recordingStartTime.map { Date().timeIntervalSince($0) } ?? 0.0
        let silenceElapsed = Date().timeIntervalSince(lastSpeechTime)

        print("⏰ Finishing smart recording after keyword detection")
        print("📊 Recording duration: \(String(format: "%.1f", recordingElapsed)) seconds (max: \(maxRecordingDuration)s)")
        print("📊 Last speech: \(String(format: "%.1f", silenceElapsed)) seconds ago (auto-stop: \(autoStopDuration)s)")

        // Determine stop reason
        if recordingElapsed >= maxRecordingDuration {
            print("🛑 STOP REASON: Maximum recording duration reached (\(maxRecordingDuration)s)")
        } else if silenceElapsed >= autoStopDuration {
            print("🔇 STOP REASON: Silence detected for \(autoStopDuration)s")
        } else {
            print("🤔 STOP REASON: Manual or other trigger")
        }

        print("📊 Final accumulated text: \(accumulatedText)")
        print("🔓 Re-enabling keyword detection for future sessions")
        print("❄️ Starting \(cooldownPeriod)s cooldown period to prevent immediate re-triggering")

        // Set cooldown period to prevent immediate re-triggering
        lastRecordingEndTime = Date()

        // Clean up all timers
        recordingTimer?.invalidate()
        recordingTimer = nil
        silenceTimer?.invalidate()
        silenceTimer = nil

        // SIMPLIFIED: End current session and create note
        endCurrentSession()

        // Reset recording state
        isRecordingAfterKeyword = false
        keywordDetectedText = ""
        keywordDetectionPoint = 0
        lastTextLength = 0

        // Force UI update
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }

        print("✅ Recording session COMPLETED")
        print("🔄 Keyword detection will be re-enabled after \(cooldownPeriod)s cooldown")
        print("⏳ Next recording session will only start when a NEW keyword is detected")
    }

    // Old createNoteFromSession method removed - replaced with createNoteImmediately
    
    // Old processAccumulatedText method removed - replaced with createNoteFromSession
}

// MARK: - SFSpeechRecognizerDelegate
extension BackgroundMonitorService: SFSpeechRecognizerDelegate {
    func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        if !available && isMonitoring {
            stopMonitoring()
        }
    }

    // MARK: - Audio Session Interruption Handling

    @objc private func audioSessionInterrupted(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }

        print("🔇 Audio session interruption: \(type == .began ? "began" : "ended")")

        switch type {
        case .began:
            print("🔇 Audio session interrupted - pausing monitoring")
            if isMonitoring {
                wasMonitoringBeforeBackground = true
                stopMonitoring()
            }

        case .ended:
            print("🔊 Audio session interruption ended")
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) && wasMonitoringBeforeBackground {
                    print("🔄 Resuming monitoring after interruption")
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        self.startMonitoring()
                    }
                }
            }

        @unknown default:
            print("⚠️ Unknown audio session interruption type")
        }
    }
}
