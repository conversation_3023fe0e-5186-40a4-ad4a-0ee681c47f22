//
//  KeywordManager.swift
//  EchoNote
//
//  Created by <PERSON> on 28/6/2025.
//

import Foundation
import SwiftData

/// Unified keyword management system that handles both system predefined and user-defined keywords
@MainActor
class KeywordManager: ObservableObject, KeywordManagerProtocol {

    // MARK: - Shared Instance
    static let shared = KeywordManager()
    
    // MARK: - Properties
    
    private var modelContext: ModelContext
    private var dataManager: DataManager
    
    // Cache for all keywords (system + user)
    @Published var allKeywords: [Keyword] = []
    
    // System predefined keywords data
    private let systemKeywords = [
        ("idea", "#FF3B30", "lightbulb.fill"),
        ("groceries", "#34C759", "cart.fill"),
        ("meeting", "#AF52DE", "person.2.fill"),
        ("task", "#FFCC00", "checkmark.square.fill"),
        ("work", "#007AFF", "briefcase.fill"),
        ("personal", "#FF9500", "person.fill"),
        ("important", "#FF2D92", "exclamationmark.triangle.fill"),
        ("reminder", "#5856D6", "bell.fill"),
        ("note", "#8E8E93", "note.text"),
        ("shopping", "#34C759", "bag.fill")
    ]
    
    // MARK: - Initialization
    
    init(modelContext: ModelContext, dataManager: DataManager) {
        self.modelContext = modelContext
        self.dataManager = dataManager

        Task {
            await initializeKeywords()
        }
    }

    // Default initializer for shared instance
    private init() {
        // This will be configured later when the app starts
        self.modelContext = ModelContext(try! ModelContainer(for: Note.self, Keyword.self, UserPreferences.self))
        self.dataManager = DataManager(modelContext: self.modelContext)

        Task {
            await initializeKeywords()
        }
    }
    
    // MARK: - Initialization Methods
    
    private func initializeKeywords() async {
        print("KeywordManager: Initializing unified keyword system...")
        
        // 1. Ensure system keywords exist in database
        await ensureSystemKeywordsExist()
        
        // 2. Load all keywords from database
        await loadAllKeywords()
        
        print("KeywordManager: Initialization complete. Total keywords: \(allKeywords.count)")
    }
    
    private func ensureSystemKeywordsExist() async {
        print("KeywordManager: Ensuring system keywords exist in database...")
        
        let existingKeywords = dataManager.getAllKeywords()
        let existingTexts = Set(existingKeywords.map { $0.text.lowercased() })
        
        for (text, color, icon) in systemKeywords {
            if !existingTexts.contains(text.lowercased()) {
                print("KeywordManager: Creating system keyword: \(text)")
                _ = dataManager.createKeyword(text: text, color: color, icon: icon)

                // Mark as system keyword (we could add a property for this)
                // For now, we'll identify system keywords by their creation date or other means
            }
        }
    }
    
    private func loadAllKeywords() async {
        print("KeywordManager: Loading all keywords from database...")
        allKeywords = dataManager.getAllKeywords()
        print("KeywordManager: Loaded \(allKeywords.count) keywords: \(allKeywords.map { $0.text })")
    }
    
    // MARK: - Public API
    
    /// Get all enabled keywords for detection
    func getEnabledKeywords() -> [Keyword] {
        return allKeywords.filter { $0.isEnabled }
    }
    
    /// Get keyword by text (case-insensitive)
    func getKeyword(byText text: String) -> Keyword? {
        return allKeywords.first { $0.text.lowercased() == text.lowercased() }
    }
    
    /// Create a new user-defined keyword
    func createUserKeyword(text: String, color: String = "#34C759", icon: String = "lightbulb.fill") -> Keyword {
        print("KeywordManager: Creating user keyword: \(text)")
        let keyword = dataManager.createKeyword(text: text, color: color, icon: icon)
        
        // Update local cache
        allKeywords.append(keyword)
        
        return keyword
    }
    
    /// Update an existing keyword
    func updateKeyword(_ keyword: Keyword, text: String? = nil, color: String? = nil, icon: String? = nil, isEnabled: Bool? = nil) {
        print("KeywordManager: Updating keyword: \(keyword.text)")
        dataManager.updateKeyword(keyword, text: text, color: color, icon: icon, isEnabled: isEnabled)
        
        // Refresh cache
        Task {
            await loadAllKeywords()
        }
    }
    
    /// Delete a keyword (only user-defined keywords can be deleted)
    func deleteKeyword(_ keyword: Keyword) {
        print("KeywordManager: Deleting keyword: \(keyword.text)")
        
        // Check if it's a system keyword
        let isSystemKeyword = systemKeywords.contains { $0.0.lowercased() == keyword.text.lowercased() }
        
        if isSystemKeyword {
            print("KeywordManager: Cannot delete system keyword: \(keyword.text)")
            return
        }
        
        dataManager.deleteKeyword(keyword)
        
        // Update local cache
        allKeywords.removeAll { $0.id == keyword.id }
    }
    
    /// Create note with proper keyword relationships
    func createNoteWithKeywords(title: String, content: String, audioURL: String? = nil, keywordTexts: [String]) -> Note {
        print("KeywordManager: Creating note with keywords: \(keywordTexts)")
        
        // Find keyword objects by text
        var keywords: [Keyword] = []
        for keywordText in keywordTexts {
            if let keyword = getKeyword(byText: keywordText) {
                keywords.append(keyword)
                print("KeywordManager: Found keyword: \(keyword.text)")
            } else {
                print("KeywordManager: Warning - keyword not found: \(keywordText)")
            }
        }
        
        // Create note using unified logic
        return createNoteWithKeywordObjects(title: title, content: content, audioURL: audioURL, keywords: keywords)
    }
    
    /// Create note with keyword objects (unified logic for all keywords)
    func createNoteWithKeywordObjects(title: String, content: String, audioURL: String? = nil, keywords: [Keyword]) -> Note {
        print("KeywordManager: Creating note '\(title)' with \(keywords.count) keyword objects")

        // Create note WITHOUT setting keywords initially to avoid relationship conflicts
        let note = Note(title: title, content: content, audioURL: audioURL, keywords: [])
        modelContext.insert(note)

        print("KeywordManager: Note created, now establishing keyword relationships...")

        // Establish relationships manually to ensure proper bidirectional handling
        for keyword in keywords {
            print("KeywordManager: Processing keyword '\(keyword.text)'")
            print("KeywordManager: Keyword currently has \(keyword.notes.count) notes")

            // Add note to keyword's notes array if not already present
            if !keyword.notes.contains(where: { $0.id == note.id }) {
                keyword.notes.append(note)
                print("KeywordManager: ✅ Added note '\(note.title)' to keyword '\(keyword.text)' notes array")
            } else {
                print("KeywordManager: ⚠️ Note already in keyword '\(keyword.text)' notes array")
            }

            // Add keyword to note's keywords array if not already present
            if !note.keywords.contains(where: { $0.id == keyword.id }) {
                note.keywords.append(keyword)
                print("KeywordManager: ✅ Added keyword '\(keyword.text)' to note '\(note.title)' keywords array")
            } else {
                print("KeywordManager: ⚠️ Keyword already in note '\(note.title)' keywords array")
            }

            // Update usage count
            keyword.incrementUsage()
            print("KeywordManager: Incremented usage for keyword '\(keyword.text)' to \(keyword.usageCount)")
            print("KeywordManager: Keyword '\(keyword.text)' now has \(keyword.notes.count) notes")
        }

        print("KeywordManager: Final note keywords: \(note.keywords.map { $0.text })")

        // Save everything
        do {
            try modelContext.save()
            print("KeywordManager: ✅ Successfully saved note '\(note.title)' with keywords: \(keywords.map { $0.text })")

            // Force refresh by re-fetching keywords to ensure relationships are properly synced
            Task {
                await loadAllKeywords()
            }
            print("KeywordManager: 🔄 Refreshed keyword cache")

            // Verify relationships after save and refresh
            for keyword in keywords {
                let noteCount = keyword.notes.count
                print("KeywordManager: Verification - Keyword '\(keyword.text)' has \(noteCount) notes")

                // Double-check that all notes in the relationship still have this keyword
                for (index, relatedNote) in keyword.notes.enumerated() {
                    let hasKeyword = relatedNote.keywords.contains { $0.id == keyword.id }
                    print("KeywordManager: Note \(index + 1) '\(relatedNote.title)' has keyword '\(keyword.text)': \(hasKeyword)")

                    if !hasKeyword {
                        print("KeywordManager: ⚠️ FIXING: Re-adding keyword '\(keyword.text)' to note '\(relatedNote.title)'")
                        relatedNote.keywords.append(keyword)
                    }
                }
            }

            // Save again if we made any fixes
            try modelContext.save()
            print("KeywordManager: 🔄 Final save completed")

        } catch {
            print("KeywordManager: ❌ Error saving note: \(error)")
        }

        return note
    }
    
    /// Refresh keyword cache
    func refreshKeywords() {
        Task {
            await loadAllKeywords()
        }
    }
    
    /// Check if a keyword is a system keyword
    func isSystemKeyword(_ keyword: Keyword) -> Bool {
        return systemKeywords.contains { $0.0.lowercased() == keyword.text.lowercased() }
    }

    /// Verify and fix all keyword-note relationships
    func verifyAndFixAllRelationships() {
        print("KeywordManager: 🔍 Verifying all keyword-note relationships...")

        let allNotes = getAllNotes()
        var fixCount = 0

        for keyword in allKeywords {
            print("KeywordManager: Checking keyword '\(keyword.text)' with \(keyword.notes.count) notes")

            // Check each note in the keyword's notes array
            for note in keyword.notes {
                let hasKeyword = note.keywords.contains { $0.id == keyword.id }
                if !hasKeyword {
                    print("KeywordManager: 🔧 FIXING: Adding keyword '\(keyword.text)' to note '\(note.title)'")
                    note.keywords.append(keyword)
                    fixCount += 1
                }
            }

            // Check each note to ensure it's in the keyword's notes array
            for note in allNotes {
                let noteHasKeyword = note.keywords.contains { $0.id == keyword.id }
                let keywordHasNote = keyword.notes.contains { $0.id == note.id }

                if noteHasKeyword && !keywordHasNote {
                    print("KeywordManager: 🔧 FIXING: Adding note '\(note.title)' to keyword '\(keyword.text)'")
                    keyword.notes.append(note)
                    fixCount += 1
                }
            }
        }

        if fixCount > 0 {
            do {
                try modelContext.save()
                print("KeywordManager: ✅ Fixed \(fixCount) relationship issues")
            } catch {
                print("KeywordManager: ❌ Error saving relationship fixes: \(error)")
            }
        } else {
            print("KeywordManager: ✅ All relationships are correct")
        }
    }

    private func getAllNotes() -> [Note] {
        let descriptor = FetchDescriptor<Note>()
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            print("KeywordManager: Error fetching notes: \(error)")
            return []
        }
    }

    // MARK: - KeywordManagerProtocol Implementation

    func findOrCreateKeyword(text: String) async throws -> Keyword {
        return try await findOrCreateKeyword(text: text)
    }

    func getAllKeywords() async -> [Keyword] {
        return allKeywords
    }
}
