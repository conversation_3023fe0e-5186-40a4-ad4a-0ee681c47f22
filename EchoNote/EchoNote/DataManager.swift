//
//  DataManager.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import SwiftData
import <PERSON><PERSON>



@Observable
@MainActor
class DataManager {
    private var modelContext: ModelContext
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    // MARK: - Note Operations
    
    func createNote(title: String, content: String, audioURL: String? = nil, keywords: [Keyword] = []) -> Note {
        return PerformanceMonitoringService.shared.measurePerformance(of: {
            // Create note WITHOUT setting keywords initially to avoid relationship conflicts
            let note = Note(title: title, content: content, audioURL: audioURL, keywords: [])
            modelContext.insert(note)

            // Establish relationships manually to ensure proper bidirectional handling
            for keyword in keywords {
                // Add note to keyword's notes array if not already present
                if !keyword.notes.contains(where: { $0.id == note.id }) {
                    keyword.notes.append(note)
                }

                // Add keyword to note's keywords array if not already present
                if !note.keywords.contains(where: { $0.id == keyword.id }) {
                    note.keywords.append(keyword)
                }

                // Update keyword usage counts
                keyword.incrementUsage()
            }

            do {
                try modelContext.save()
            } catch {
                ErrorHandlingService.shared.handle(StorageError.saveFailed(underlying: error), context: "Creating note")
            }

            return note
        }, eventType: .dataSave, metadata: ["operation": "createNote"])
    }
    
    func updateNote(_ note: Note, title: String? = nil, content: String? = nil, audioURL: String? = nil, keywords: [Keyword]? = nil) {
        if let title = title {
            note.title = title
        }
        if let content = content {
            note.content = content
        }
        if let audioURL = audioURL {
            note.audioURL = audioURL
        }
        if let keywords = keywords {
            note.keywords = keywords
        }
        
        note.updateModifiedDate()
        
        do {
            try modelContext.save()
        } catch {
            // Log error silently for debugging
        }
    }

    func updateNoteKeywords(_ note: Note, keywords: [Keyword]) {
        note.keywords = keywords
        note.updateModifiedDate()

        do {
            try modelContext.save()
        } catch {
            // Log error silently for debugging
        }
    }

    func deleteNote(_ note: Note) {
        modelContext.delete(note)
        
        do {
            try modelContext.save()
        } catch {
            // Log error silently for debugging
        }
    }
    
    func toggleNoteFavorite(_ note: Note) {
        note.isFavorite.toggle()
        note.updateModifiedDate()
        
        do {
            try modelContext.save()
        } catch {
            // Handle error silently
        }
    }
    
    // MARK: - Data Loading Operations

    func loadNotes(limit: Int = 100, offset: Int = 0) -> [Note] {
        return PerformanceMonitoringService.shared.measurePerformance(of: {
            let descriptor = FetchDescriptor<Note>(
                sortBy: [SortDescriptor(\.updatedAt, order: .reverse)]
            )

            do {
                let notes = try modelContext.fetch(descriptor)
                return Array(notes.dropFirst(offset).prefix(limit))
            } catch {
                ErrorHandlingService.shared.handle(StorageError.loadFailed(underlying: error), context: "Loading notes")
                return []
            }
        }, eventType: .dataLoad, metadata: ["operation": "loadNotes", "limit": String(limit)])
    }

    func loadRecentNotes(count: Int = 10) -> [Note] {
        return PerformanceMonitoringService.shared.measurePerformance(of: {
            let descriptor = FetchDescriptor<Note>(
                sortBy: [SortDescriptor(\.updatedAt, order: .reverse)]
            )

            do {
                let notes = try modelContext.fetch(descriptor)
                return Array(notes.prefix(count))
            } catch {
                ErrorHandlingService.shared.handle(StorageError.loadFailed(underlying: error), context: "Loading recent notes")
                return []
            }
        }, eventType: .dataLoad, metadata: ["operation": "loadRecentNotes", "count": String(count)])
    }

    func searchNotes(query: String) -> [Note] {
        return PerformanceMonitoringService.shared.measurePerformance(of: {
            let predicate = #Predicate<Note> { note in
                note.title.localizedStandardContains(query) ||
                note.content.localizedStandardContains(query)
            }

            let descriptor = FetchDescriptor<Note>(
                predicate: predicate,
                sortBy: [SortDescriptor(\.updatedAt, order: .reverse)]
            )

            do {
                return try modelContext.fetch(descriptor)
            } catch {
                ErrorHandlingService.shared.handle(StorageError.loadFailed(underlying: error), context: "Searching notes")
                return []
            }
        }, eventType: .dataLoad, metadata: ["operation": "searchNotes", "query": query])
    }

    // MARK: - Keyword Operations
    
    func createKeyword(text: String, color: String = "#34C759", icon: String = "lightbulb.fill") -> Keyword {
        return PerformanceMonitoringService.shared.measurePerformance(of: {
            let keyword = Keyword(text: text, color: color, icon: icon)
            modelContext.insert(keyword)

            do {
                try modelContext.save()
            } catch {
                ErrorHandlingService.shared.handle(StorageError.saveFailed(underlying: error), context: "Creating keyword")
            }

            return keyword
        }, eventType: .dataSave, metadata: ["operation": "createKeyword"])
    }

    func findOrCreateKeyword(text: String) -> Keyword {
        // Try to find existing keyword (case-sensitive for now due to SwiftData limitations)
        let descriptor = FetchDescriptor<Keyword>(
            predicate: #Predicate<Keyword> { keyword in
                keyword.text == text
            }
        )

        do {
            let existingKeywords = try modelContext.fetch(descriptor)
            if let existingKeyword = existingKeywords.first {
                LoggingService.shared.info("Found existing keyword: \(text)", category: .system)
                return existingKeyword
            }
        } catch {
            LoggingService.shared.error("Failed to search for keyword: \(error)", category: .system)
        }

        // If not found with exact match, try manual case-insensitive search
        let allKeywordsDescriptor = FetchDescriptor<Keyword>()
        do {
            let allKeywords = try modelContext.fetch(allKeywordsDescriptor)
            if let existingKeyword = allKeywords.first(where: { $0.text.lowercased() == text.lowercased() }) {
                LoggingService.shared.info("Found existing keyword (case-insensitive): \(text)", category: .system)
                return existingKeyword
            }
        } catch {
            LoggingService.shared.error("Failed to search all keywords: \(error)", category: .system)
        }

        // Create new keyword if not found
        LoggingService.shared.info("Creating new keyword: \(text)", category: .system)
        return createKeyword(text: text)
    }

    func updateKeyword(_ keyword: Keyword, text: String? = nil, color: String? = nil, icon: String? = nil, isEnabled: Bool? = nil) {
        if let text = text {
            keyword.text = text
        }
        if let color = color {
            keyword.color = color
        }
        if let icon = icon {
            keyword.icon = icon
        }
        if let isEnabled = isEnabled {
            keyword.isEnabled = isEnabled
        }
        
        do {
            try modelContext.save()
        } catch {
            // Handle error silently
        }
    }
    
    func deleteKeyword(_ keyword: Keyword) {
        modelContext.delete(keyword)

        do {
            try modelContext.save()
        } catch {
            // Handle error silently
        }
    }

    func getAllKeywords() -> [Keyword] {
        let descriptor = FetchDescriptor<Keyword>(sortBy: [SortDescriptor(\.text)])
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            return []
        }
    }
    
    // MARK: - User Preferences Operations
    
    func getUserPreferences() -> UserPreferences? {
        let descriptor = FetchDescriptor<UserPreferences>()
        do {
            let preferences = try modelContext.fetch(descriptor)
            return preferences.first
        } catch {
            return nil
        }
    }
    
    func createOrUpdateUserPreferences() -> UserPreferences {
        if let existingPreferences = getUserPreferences() {
            return existingPreferences
        } else {
            let preferences = UserPreferences()
            modelContext.insert(preferences)
            
            do {
                try modelContext.save()
            } catch {
                // Handle error silently
            }
            
            return preferences
        }
    }
    
    func updateUserPreferences(_ preferences: UserPreferences) {
        preferences.updateModifiedDate()
        
        do {
            try modelContext.save()
        } catch {
            // Handle error silently
        }
    }

    // MARK: - Batch Operations

    func batchSave() {
        PerformanceMonitoringService.shared.measurePerformance(of: {
            do {
                try modelContext.save()
            } catch {
                ErrorHandlingService.shared.handle(StorageError.saveFailed(underlying: error), context: "Batch save operation")
            }
        }, eventType: .dataSave, metadata: ["operation": "batchSave"])
    }

    func performBatchOperation<T>(_ operation: () throws -> T) -> T? {
        return PerformanceMonitoringService.shared.measurePerformance(of: {
            do {
                let result = try operation()
                try modelContext.save()
                return result
            } catch {
                ErrorHandlingService.shared.handle(StorageError.saveFailed(underlying: error), context: "Batch operation")
                return nil
            }
        }, eventType: .dataSave, metadata: ["operation": "batchOperation"])
    }
}
