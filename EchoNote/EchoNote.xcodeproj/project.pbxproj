// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		C451277D2E0E67BD00955721 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C45127602E0E67BA00955721 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C45127672E0E67BB00955721;
			remoteInfo = EchoNote;
		};
		C45127872E0E67BD00955721 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C45127602E0E67BA00955721 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C45127672E0E67BB00955721;
			remoteInfo = EchoNote;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		C45127682E0E67BB00955721 /* EchoNote.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = EchoNote.app; sourceTree = BUILT_PRODUCTS_DIR; };
		C451277C2E0E67BD00955721 /* EchoNoteTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = EchoNoteTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		C45127862E0E67BD00955721 /* EchoNoteUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = EchoNoteUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		C451278E2E0E67BD00955721 /* Exceptions for "EchoNote" folder in "EchoNote" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = C45127672E0E67BB00955721 /* EchoNote */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		C451276A2E0E67BB00955721 /* EchoNote */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				C451278E2E0E67BD00955721 /* Exceptions for "EchoNote" folder in "EchoNote" target */,
			);
			path = EchoNote;
			sourceTree = "<group>";
		};
		C451277F2E0E67BD00955721 /* EchoNoteTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = EchoNoteTests;
			sourceTree = "<group>";
		};
		C45127892E0E67BD00955721 /* EchoNoteUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = EchoNoteUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		C45127652E0E67BB00955721 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C45127792E0E67BD00955721 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C45127832E0E67BD00955721 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		C451275F2E0E67BA00955721 = {
			isa = PBXGroup;
			children = (
				C451276A2E0E67BB00955721 /* EchoNote */,
				C451277F2E0E67BD00955721 /* EchoNoteTests */,
				C45127892E0E67BD00955721 /* EchoNoteUITests */,
				C45127692E0E67BB00955721 /* Products */,
			);
			sourceTree = "<group>";
		};
		C45127692E0E67BB00955721 /* Products */ = {
			isa = PBXGroup;
			children = (
				C45127682E0E67BB00955721 /* EchoNote.app */,
				C451277C2E0E67BD00955721 /* EchoNoteTests.xctest */,
				C45127862E0E67BD00955721 /* EchoNoteUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		C45127672E0E67BB00955721 /* EchoNote */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C451278F2E0E67BD00955721 /* Build configuration list for PBXNativeTarget "EchoNote" */;
			buildPhases = (
				C45127642E0E67BB00955721 /* Sources */,
				C45127652E0E67BB00955721 /* Frameworks */,
				C45127662E0E67BB00955721 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				C451276A2E0E67BB00955721 /* EchoNote */,
			);
			name = EchoNote;
			packageProductDependencies = (
			);
			productName = EchoNote;
			productReference = C45127682E0E67BB00955721 /* EchoNote.app */;
			productType = "com.apple.product-type.application";
		};
		C451277B2E0E67BD00955721 /* EchoNoteTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C45127942E0E67BD00955721 /* Build configuration list for PBXNativeTarget "EchoNoteTests" */;
			buildPhases = (
				C45127782E0E67BD00955721 /* Sources */,
				C45127792E0E67BD00955721 /* Frameworks */,
				C451277A2E0E67BD00955721 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C451277E2E0E67BD00955721 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				C451277F2E0E67BD00955721 /* EchoNoteTests */,
			);
			name = EchoNoteTests;
			packageProductDependencies = (
			);
			productName = EchoNoteTests;
			productReference = C451277C2E0E67BD00955721 /* EchoNoteTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		C45127852E0E67BD00955721 /* EchoNoteUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C45127972E0E67BD00955721 /* Build configuration list for PBXNativeTarget "EchoNoteUITests" */;
			buildPhases = (
				C45127822E0E67BD00955721 /* Sources */,
				C45127832E0E67BD00955721 /* Frameworks */,
				C45127842E0E67BD00955721 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C45127882E0E67BD00955721 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				C45127892E0E67BD00955721 /* EchoNoteUITests */,
			);
			name = EchoNoteUITests;
			packageProductDependencies = (
			);
			productName = EchoNoteUITests;
			productReference = C45127862E0E67BD00955721 /* EchoNoteUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		C45127602E0E67BA00955721 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					C45127672E0E67BB00955721 = {
						CreatedOnToolsVersion = 16.2;
					};
					C451277B2E0E67BD00955721 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = C45127672E0E67BB00955721;
					};
					C45127852E0E67BD00955721 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = C45127672E0E67BB00955721;
					};
				};
			};
			buildConfigurationList = C45127632E0E67BA00955721 /* Build configuration list for PBXProject "EchoNote" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = C451275F2E0E67BA00955721;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = C45127692E0E67BB00955721 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				C45127672E0E67BB00955721 /* EchoNote */,
				C451277B2E0E67BD00955721 /* EchoNoteTests */,
				C45127852E0E67BD00955721 /* EchoNoteUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		C45127662E0E67BB00955721 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C451277A2E0E67BD00955721 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C45127842E0E67BD00955721 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		C45127642E0E67BB00955721 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C45127782E0E67BD00955721 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C45127822E0E67BD00955721 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		C451277E2E0E67BD00955721 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C45127672E0E67BB00955721 /* EchoNote */;
			targetProxy = C451277D2E0E67BD00955721 /* PBXContainerItemProxy */;
		};
		C45127882E0E67BD00955721 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C45127672E0E67BB00955721 /* EchoNote */;
			targetProxy = C45127872E0E67BD00955721 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		C45127902E0E67BD00955721 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = EchoNote/EchoNote.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"EchoNote/Preview Content\"";
				DEVELOPMENT_TEAM = PGR8SUBXHF;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = EchoNote/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clevorie.EchoNote;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		C45127912E0E67BD00955721 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = EchoNote/EchoNote.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"EchoNote/Preview Content\"";
				DEVELOPMENT_TEAM = PGR8SUBXHF;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = EchoNote/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clevorie.EchoNote;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		C45127922E0E67BD00955721 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		C45127932E0E67BD00955721 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		C45127952E0E67BD00955721 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clevorie.EchoNoteTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/EchoNote.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/EchoNote";
			};
			name = Debug;
		};
		C45127962E0E67BD00955721 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clevorie.EchoNoteTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/EchoNote.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/EchoNote";
			};
			name = Release;
		};
		C45127982E0E67BD00955721 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clevorie.EchoNoteUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = EchoNote;
			};
			name = Debug;
		};
		C45127992E0E67BD00955721 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.clevorie.EchoNoteUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = EchoNote;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C45127632E0E67BA00955721 /* Build configuration list for PBXProject "EchoNote" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C45127922E0E67BD00955721 /* Debug */,
				C45127932E0E67BD00955721 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C451278F2E0E67BD00955721 /* Build configuration list for PBXNativeTarget "EchoNote" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C45127902E0E67BD00955721 /* Debug */,
				C45127912E0E67BD00955721 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C45127942E0E67BD00955721 /* Build configuration list for PBXNativeTarget "EchoNoteTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C45127952E0E67BD00955721 /* Debug */,
				C45127962E0E67BD00955721 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C45127972E0E67BD00955721 /* Build configuration list for PBXNativeTarget "EchoNoteUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C45127982E0E67BD00955721 /* Debug */,
				C45127992E0E67BD00955721 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = C45127602E0E67BA00955721 /* Project object */;
}
