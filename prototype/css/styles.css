/* Main Styles for Echonote App */
:root {
  --primary-bg: #000000;
  --secondary-bg: #121212;
  --card-bg: #1e1e1e;
  --accent-green: #4cd964;
  --accent-pink: #ff6b81;
  --accent-yellow: #ffcc00;
  --text-primary: #ffffff;
  --text-secondary: #a0a0a0;
  --border-radius: 16px;
  --border-radius-lg: 24px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

body {
  background-color: #681a65;
  color: var(--text-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

.container {
  width: 393px; /* iPhone 16 宽度 */
  height: 852px; /* iPhone 16 高度 */
  margin: 0 auto;
  background-color: var(--primary-bg);
  position: relative;
  overflow: hidden;
  border-radius: 55px; /* iPhone 16 圆角 */
  /* 模拟iPhone 16边框 */
  border: 10px solid #1a1a1a;
  /* 添加阴影效果模拟手机立体感 */
  box-shadow: 
    inset 0 0 10px rgba(255, 255, 255, 0.1),
    0 20px 50px rgba(0, 0, 0, 0.5),
    0 0 0 10px #1a1a1a;
  /* iPhone 16 notch simulation */
  padding-top: 35px;
  /* 添加微妙的反光效果 */
  background-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.03) 0%,
    rgba(255, 255, 255, 0) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
}

/* 添加电源按钮 */
.container:before {
  content: "";
  position: absolute;
  right: -14px;
  top: 150px;
  width: 4px;
  height: 80px;
  background-color: #1a1a1a;
  border-radius: 2px;
  z-index: 1001;
}

/* 添加音量按钮 */
.container:after {
  content: "";
  position: absolute;
  left: -14px;
  top: 150px;
  width: 4px;
  height: 120px;
  background-color: #1a1a1a;
  border-radius: 2px;
  z-index: 1001;
}

/* iPhone 16 Dynamic Island */
.dynamic-island {
  position: absolute;
  top: 12px;
  left: 50%;
  transform: translateX(-50%);
  width: 126px;
  height: 37px;
  background-color: #000;
  border-radius: 20px;
  z-index: 1000;
  /* 添加微妙的内阴影效果 */
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.1);
  /* 添加摄像头效果 */
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 添加摄像头效果 */
.dynamic-island:before {
  content: "";
  position: absolute;
  right: 30px;
  width: 12px;
  height: 12px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 50%;
  box-shadow: inset 0 0 2px rgba(255, 255, 255, 0.5);
}

/* 状态栏 */
.status-bar {
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  height: 30px;
  padding: 0 28px; /* 增加左右内边距 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 999;
}

.status-bar-left {
  font-size: 16px; /* 增大字体 */
  font-weight: 600;
}

.status-bar-right {
  display: flex;
  align-items: center;
  gap: 8px; /* 增加图标间距 */
  font-size: 14px;
}

.signal-icon, .wifi-icon, .battery-icon {
  font-size: 16px; /* 增大图标 */
}

.header {
  padding: 16px 20px;
  padding-top: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-back {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.header-back i {
  font-size: 18px;
  color: var(--accent-green);
}

.header h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 16px;
  margin-bottom: 16px;
}

.stats-card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
  background-color: var(--secondary-bg);
  border-radius: var(--border-radius);
  margin-bottom: 16px;
}

.chart-container {
  position: relative;
  width: 100%;
  height: 200px;
}

.donut-chart {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 底部指示条 - iPhone 16特有 */
.bottom-indicator {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 134px;
  height: 5px;
  background-color: #ffffff;
  border-radius: 3px;
  z-index: 1000;
  opacity: 0.3;
}

/* 调整tab-bar位置，为底部指示条腾出空间 */
.tab-bar {
  position: absolute;
  bottom: 20px; /* 增加底部距离 */
  left: 0;
  right: 0;
  height: 80px;
  background-color: var(--secondary-bg);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding-bottom: 25px; /* 增加底部内边距 */
  border-bottom-left-radius: 45px; /* 与手机边框圆角匹配 */
  border-bottom-right-radius: 45px; /* 与手机边框圆角匹配 */
}

/* 添加底部边缘渐变效果 */
.tab-bar:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.2) 100%
  );
  border-bottom-left-radius: 45px;
  border-bottom-right-radius: 45px;
  z-index: -1;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--text-secondary);
  font-size: 10px;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: var(--text-primary);
}

.tab-item.active .tab-icon {
  color: var(--accent-green);
}

.tab-icon {
  font-size: 22px;
  margin-bottom: 4px;
  transition: color 0.3s ease;
}

.mic-button {
  width: 60px;
  height: 60px;
  background-color: var(--accent-green);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px rgba(76, 217, 100, 0.3);
  transform: translateY(-15px);
}

.mic-icon {
  font-size: 24px;
  color: var(--primary-bg);
}

.content {
  padding: 0 16px;
  margin-bottom: 80px;
  height: calc(100% - 60px - 80px);
  overflow-y: auto;
}

.tag-selector {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
  overflow-x: auto;
  padding: 5px 0;
  -webkit-overflow-scrolling: touch;
}

.tag {
  background-color: var(--secondary-bg);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  white-space: nowrap;
}

.tag.active {
  background-color: var(--accent-green);
  color: var(--primary-bg);
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.note-card {
  background-color: var(--secondary-bg);
  border-radius: var(--border-radius);
  padding: 16px;
  position: relative;
}

.note-card h3 {
  font-size: 16px;
  margin-bottom: 8px;
}

.note-card p {
  font-size: 12px;
  color: var(--text-secondary);
}

.note-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 10px;
}

/* Keyword Icon Styles */
.keyword-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.keyword-icon i {
  font-size: 20px;
  color: white;
}

.keyword-card {
  display: flex;
  align-items: center;
  background-color: var(--secondary-bg);
  border-radius: var(--border-radius);
  padding: 15px;
  margin-bottom: 15px;
  position: relative;
  cursor: pointer;
}

.keyword-info {
  flex-grow: 1;
}

.keyword-info h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
}

.keyword-info p {
  margin: 0;
  font-size: 12px;
  color: var(--text-secondary);
}

.keyword-actions {
  display: flex;
  gap: 15px;
}

.edit-button, .delete-button {
  color: var(--text-secondary);
  cursor: pointer;
}

.edit-button:hover, .delete-button:hover {
  color: var(--text-primary);
}

/* Keyword Icon Colors */
.icon-idea {
  background-color: #FF3B30;
}

.icon-groceries {
  background-color: #34C759;
}

.icon-meeting {
  background-color: #AF52DE;
}

.icon-task {
  background-color: #FFCC00;
}

.icon-reminder {
  background-color: #5AC8FA;
}

.icon-book {
  background-color: #FF9500;
}

.icon-movie {
  background-color: #007AFF;
}

.icon-recipe {
  background-color: #FF2D55;
}

/* Section Title */
.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 25px 0 15px 5px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Add Button */
.add-button {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: var(--accent-green);
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 90px;
  right: 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  z-index: 100;
}

.add-button i {
  font-size: 24px;
  color: var(--primary-bg);
}

/* Chevron Icon */
.chevron-icon {
  color: var(--text-secondary);
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
}

.settings-section {
  margin-bottom: 24px;
}

.settings-section h2 {
  font-size: 18px;
  margin-bottom: 12px;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-item-label {
  display: flex;
  align-items: center;
  gap: 12px;
}

.settings-icon {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.slider-container {
  padding: 0 16px;
  margin-top: 8px;
}

.slider {
  width: 100%;
  -webkit-appearance: none;
  height: 4px;
  border-radius: 2px;
  background: var(--secondary-bg);
  outline: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--accent-green);
  cursor: pointer;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.profile-pic {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--accent-green);
}

.profile-info h2 {
  font-size: 20px;
  margin-bottom: 4px;
}

.profile-info p {
  color: var(--text-secondary);
  font-size: 14px;
}

.recording-modal {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.waveform {
  width: 100%;
  height: 100px;
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.wave-bar {
  width: 5px;
  height: 40px;
  background-color: var(--accent-green);
  border-radius: 2px;
  animation: wave 1s infinite ease-in-out;
}

@keyframes wave {
  0%, 100% {
    height: 20px;
  }
  50% {
    height: 60px;
  }
}

.wave-bar:nth-child(2) {
  animation-delay: 0.1s;
}

.wave-bar:nth-child(3) {
  animation-delay: 0.2s;
}

.wave-bar:nth-child(4) {
  animation-delay: 0.3s;
}

.wave-bar:nth-child(5) {
  animation-delay: 0.4s;
}

.recording-status {
  font-size: 24px;
  margin-bottom: 16px;
}

.recording-keyword {
  font-size: 32px;
  font-weight: bold;
  color: var(--accent-green);
  margin-bottom: 40px;
}

.recording-actions {
  display: flex;
  gap: 20px;
}

.recording-button {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
}

.stop-button {
  background-color: var(--accent-pink);
}

.pause-button {
  background-color: var(--accent-yellow);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 400px) {
  .container {
    width: 100%;
    height: 100vh;
    border-radius: 0;
  }
}

.quick-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background-color: var(--secondary-bg);
  border-radius: var(--border-radius);
  width: 80px;
  cursor: pointer;
  transition: transform 0.2s, background-color 0.2s;
}

.quick-action-btn:hover {
  transform: translateY(-2px);
  background-color: rgba(255, 255, 255, 0.1);
}

.quick-action-btn i {
  font-size: 24px;
  margin-bottom: 8px;
  color: var(--accent-green);
}

.quick-action-btn span {
  font-size: 12px;
  color: var(--text-secondary);
} 