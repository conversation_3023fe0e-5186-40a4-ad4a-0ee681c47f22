<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Echonote - Recording</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <style>
    body, html {
      overflow: hidden;
    }
    
    .recording-modal {
      display: flex !important;
    }
    
    .recording-keyword {
      font-size: 36px;
      text-align: center;
    }
    
    .recording-text {
      font-size: 18px;
      color: var(--text-secondary);
      text-align: center;
      margin-bottom: 40px;
      max-width: 80%;
    }
    
    .recording-timer {
      font-size: 24px;
      color: var(--accent-green);
      margin-bottom: 30px;
    }
    
    .detected-keyword {
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0.6;
      }
      100% {
        opacity: 1;
      }
    }
    
    .active-recording .waveform {
      margin-top: -80px;
    }
    
    .active-recording .wave-bar {
      background-color: var(--accent-pink);
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- First State: Listening for Keyword -->
    <div class="recording-modal">
      <div class="waveform">
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
      </div>
      
      <div class="recording-status">Listening for keyword...</div>
      <div class="recording-keyword">Say "Idea", "Groceries", etc.</div>
      
      <div class="recording-actions">
        <div class="recording-button pause-button">
          <i class="fas fa-pause"></i>
        </div>
        
        <div class="recording-button stop-button">
          <i class="fas fa-stop"></i>
        </div>
      </div>
    </div>
  </div>
  
  <script src="js/main.js"></script>
  <script>
    // For demonstration purposes, toggle between recording states
    document.addEventListener('DOMContentLoaded', () => {
      const recordingModal = document.querySelector('.recording-modal');
      const recordingStatus = document.querySelector('.recording-status');
      const recordingKeyword = document.querySelector('.recording-keyword');
      
      // Simulate keyword detection after 3 seconds
      setTimeout(() => {
        recordingStatus.textContent = 'Keyword Detected!';
        recordingKeyword.innerHTML = '<span class="detected-keyword">Idea</span>';
        recordingKeyword.classList.add('detected-keyword');
        
        // Create and add timer element
        const timer = document.createElement('div');
        timer.className = 'recording-timer';
        timer.textContent = '0:00';
        recordingKeyword.after(timer);
        
        // Create and add recording text
        const recordingText = document.createElement('div');
        recordingText.className = 'recording-text';
        recordingText.textContent = 'Recording your idea...';
        timer.after(recordingText);
        
        recordingModal.classList.add('active-recording');
        
        // Update timer
        let seconds = 0;
        const timerInterval = setInterval(() => {
          seconds++;
          const mins = Math.floor(seconds / 60);
          const secs = seconds % 60;
          timer.textContent = `${mins}:${secs < 10 ? '0' + secs : secs}`;
        }, 1000);
        
        // Simulate text transcription
        const phrases = [
          "I have an idea for a new app...",
          "It would help people track their daily habits...",
          "Users could set goals and get reminders...",
          "The app would show progress over time with charts..."
        ];
        
        let phraseIndex = 0;
        const textInterval = setInterval(() => {
          if (phraseIndex < phrases.length) {
            recordingText.textContent = phrases[phraseIndex];
            phraseIndex++;
          } else {
            clearInterval(textInterval);
          }
        }, 3000);
      }, 3000);
    });
  </script>
</body>
</html> 