<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Echonote - Note Detail</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <style>
    .note-detail-container {
      padding: 0 20px;
    }
    
    .note-header {
      margin-bottom: 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 15px;
    }
    
    .note-title {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 10px;
      color: var(--text-primary);
    }
    
    .note-meta {
      display: flex;
      justify-content: space-between;
      color: var(--text-secondary);
      font-size: 14px;
    }
    
    .note-tag {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 15px;
      background-color: var(--accent-green);
      color: var(--primary-bg);
      font-size: 12px;
      font-weight: 500;
    }
    
    .note-content {
      line-height: 1.6;
      color: var(--text-primary);
      margin-bottom: 30px;
    }
    
    .note-actions {
      display: flex;
      justify-content: space-around;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
    }
    
    .action-icon {
      font-size: 20px;
      margin-bottom: 8px;
    }
    
    .action-label {
      font-size: 12px;
      color: var(--text-secondary);
    }
    
    .edit-icon {
      color: var(--accent-green);
    }
    
    .share-icon {
      color: var(--accent-yellow);
    }
    
    .delete-icon {
      color: var(--accent-pink);
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- iPhone 16 Dynamic Island -->
    <div class="dynamic-island"></div>
    
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal signal-icon"></i>
        <i class="fas fa-wifi wifi-icon"></i>
        <i class="fas fa-battery-full battery-icon"></i>
      </div>
    </div>
    
    <div class="header">
      <div class="header-back" onclick="window.location.href='notes.html'">
        <i class="fas fa-chevron-left"></i>
      </div>
      <h1>Note</h1>
      <div class="header-actions">
        <i class="fas fa-search" onclick="window.location.href='search.html'"></i>
      </div>
    </div>
    
    <div class="content">
      <div class="note-detail-container">
        <div class="note-header">
          <h2 class="note-title">App Concept</h2>
          <div class="note-meta">
            <div class="note-tag">Idea</div>
            <div class="note-date">Today, 3:45 PM</div>
          </div>
        </div>
        
        <div class="note-content">
          <p>Create a mobile app for tracking daily water intake with reminders and achievements.</p>
          <p>The app would feature a clean, minimal interface with daily goals, weekly stats, and customizable reminders. Users could track their progress over time and earn achievements for consistency.</p>
          <p>Key features:</p>
          <ul>
            <li>Daily water intake tracking</li>
            <li>Customizable goals</li>
            <li>Smart reminders</li>
            <li>Achievement system</li>
            <li>Progress visualization</li>
          </ul>
        </div>
        
        <div class="note-actions">
          <div class="action-item" onclick="window.location.href='edit-note.html'">
            <i class="fas fa-edit action-icon edit-icon"></i>
            <span class="action-label">Edit</span>
          </div>
          
          <div class="action-item">
            <i class="fas fa-share-alt action-icon share-icon"></i>
            <span class="action-label">Share</span>
          </div>
          
          <div class="action-item">
            <i class="fas fa-trash action-icon delete-icon"></i>
            <span class="action-label">Delete</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Tab Bar -->
    <div class="tab-bar">
      <div class="tab-item" data-page="home.html">
        <i class="fas fa-home tab-icon"></i>
        <span>Home</span>
      </div>
      
      <div class="tab-item" data-page="keywords.html">
        <i class="fas fa-tags tab-icon"></i>
        <span>Keywords</span>
      </div>
      
      <div class="mic-button">
        <i class="fas fa-microphone mic-icon"></i>
      </div>
      
      <div class="tab-item active" data-page="notes.html">
        <i class="fas fa-sticky-note tab-icon"></i>
        <span>Notes</span>
      </div>
      
      <div class="tab-item" data-page="profile.html">
        <i class="fas fa-user tab-icon"></i>
        <span>Profile</span>
      </div>
    </div>
  </div>
  
  <script src="js/main.js"></script>
</body>
</html> 