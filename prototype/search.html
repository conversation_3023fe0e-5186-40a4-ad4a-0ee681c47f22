<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Echonote - Search</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <style>
    .search-container {
      padding: 0 20px;
    }
    
    .search-box {
      position: relative;
      margin-bottom: 20px;
    }
    
    .search-input {
      width: 100%;
      background-color: var(--secondary-bg);
      border: none;
      border-radius: var(--border-radius);
      color: var(--text-primary);
      font-size: 16px;
      padding: 15px 45px 15px 15px;
    }
    
    .search-icon {
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--text-secondary);
    }
    
    .search-filters {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
      overflow-x: auto;
      padding-bottom: 10px;
    }
    
    .search-filter {
      padding: 8px 15px;
      border-radius: 20px;
      background-color: var(--secondary-bg);
      font-size: 14px;
      white-space: nowrap;
      cursor: pointer;
    }
    
    .search-filter.active {
      background-color: var(--accent-green);
      color: var(--primary-bg);
    }
    
    .search-results {
      margin-top: 20px;
    }
    
    .search-result-card {
      background-color: var(--secondary-bg);
      border-radius: var(--border-radius);
      padding: 15px;
      margin-bottom: 15px;
      cursor: pointer;
    }
    
    .search-result-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    
    .search-result-title {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
    }
    
    .search-result-tag {
      display: inline-block;
      padding: 2px 8px;
      border-radius: 10px;
      font-size: 10px;
    }
    
    .search-result-content {
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 10px;
    }
    
    .search-result-highlight {
      background-color: rgba(255, 204, 0, 0.3);
      padding: 0 2px;
    }
    
    .search-result-meta {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: var(--text-secondary);
    }
    
    .recent-searches {
      margin-top: 30px;
    }
    
    .recent-searches h3 {
      font-size: 16px;
      color: var(--text-secondary);
      margin-bottom: 15px;
    }
    
    .recent-search-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .recent-search-text {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .recent-search-text i {
      color: var(--text-secondary);
    }
    
    .clear-search {
      color: var(--accent-pink);
      font-size: 12px;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- iPhone 16 Dynamic Island -->
    <div class="dynamic-island"></div>
    
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal signal-icon"></i>
        <i class="fas fa-wifi wifi-icon"></i>
        <i class="fas fa-battery-full battery-icon"></i>
      </div>
    </div>
    
    <div class="header">
      <div class="header-back" onclick="window.location.href='notes.html'">
        <i class="fas fa-chevron-left"></i>
      </div>
      <h1>Search</h1>
      <div class="header-actions">
        <i class="fas fa-search"></i>
      </div>
    </div>
    
    <div class="content">
      <div class="search-container">
        <div class="search-box">
          <input type="text" class="search-input" placeholder="Search notes..." value="app">
          <i class="fas fa-search search-icon"></i>
        </div>
        
        <div class="search-filters">
          <div class="search-filter active">All</div>
          <div class="search-filter">Idea</div>
          <div class="search-filter">Meeting</div>
          <div class="search-filter">Task</div>
          <div class="search-filter">Groceries</div>
          <div class="search-filter">This Week</div>
          <div class="search-filter">This Month</div>
        </div>
        
        <div class="search-results">
          <div class="search-result-card" onclick="window.location.href='note-detail.html'">
            <div class="search-result-header">
              <h3 class="search-result-title">App Concept</h3>
              <div class="search-result-tag tag-idea">Idea</div>
            </div>
            <div class="search-result-content">
              Create a mobile <span class="search-result-highlight">app</span> for tracking daily water intake with reminders and achievements...
            </div>
            <div class="search-result-meta">
              <div>Today, 10:23 AM</div>
              <div>3 mentions</div>
            </div>
          </div>
          
          <div class="search-result-card" onclick="window.location.href='note-detail.html'">
            <div class="search-result-header">
              <h3 class="search-result-title">Marketing Strategy</h3>
              <div class="search-result-tag tag-meeting">Meeting</div>
            </div>
            <div class="search-result-content">
              Discuss mobile <span class="search-result-highlight">app</span> marketing strategy for Q3, prepare slides for presentation...
            </div>
            <div class="search-result-meta">
              <div>Yesterday, 2:30 PM</div>
              <div>1 mention</div>
            </div>
          </div>
        </div>
        
        <div class="recent-searches">
          <h3>Recent Searches</h3>
          
          <div class="recent-search-item">
            <div class="recent-search-text">
              <i class="fas fa-history"></i>
              <span>meeting notes</span>
            </div>
            <div class="clear-search">Remove</div>
          </div>
          
          <div class="recent-search-item">
            <div class="recent-search-text">
              <i class="fas fa-history"></i>
              <span>grocery list</span>
            </div>
            <div class="clear-search">Remove</div>
          </div>
          
          <div class="recent-search-item">
            <div class="recent-search-text">
              <i class="fas fa-history"></i>
              <span>book ideas</span>
            </div>
            <div class="clear-search">Remove</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Tab Bar -->
    <div class="tab-bar">
      <div class="tab-item" data-page="home.html">
        <i class="fas fa-home tab-icon"></i>
        <span>Home</span>
      </div>
      
      <div class="tab-item" data-page="keywords.html">
        <i class="fas fa-tags tab-icon"></i>
        <span>Keywords</span>
      </div>
      
      <div class="mic-button">
        <i class="fas fa-microphone mic-icon"></i>
      </div>
      
      <div class="tab-item active" data-page="notes.html">
        <i class="fas fa-sticky-note tab-icon"></i>
        <span>Notes</span>
      </div>
      
      <div class="tab-item" data-page="profile.html">
        <i class="fas fa-user tab-icon"></i>
        <span>Profile</span>
      </div>
    </div>
  </div>
  
  <script src="js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const searchFilters = document.querySelectorAll('.search-filter');
      
      searchFilters.forEach(filter => {
        filter.addEventListener('click', () => {
          // Remove active class from all filters
          searchFilters.forEach(f => f.classList.remove('active'));
          
          // Add active class to clicked filter
          filter.classList.add('active');
        });
      });
      
      const clearSearchButtons = document.querySelectorAll('.clear-search');
      
      clearSearchButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          e.stopPropagation();
          const searchItem = button.closest('.recent-search-item');
          searchItem.style.opacity = '0';
          setTimeout(() => {
            searchItem.style.display = 'none';
          }, 300);
        });
      });
    });
  </script>
</body>
</html> 