<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Echonote App Prototype</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      background-color: #681a65;
      margin: 0;
      padding: 20px;
      min-height: 100vh;
    }
    
    .prototype-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 40px;
      margin-bottom: 40px;
      padding: 20px;
    }
    
    .device-container {
      position: relative;
      width: 393px;
      height: 852px;
      margin: 0 auto;
      /* 模拟iPhone 16边框 */
      border: 10px solid #1a1a1a;
      border-radius: 55px;
      overflow: hidden;
      box-shadow: 
        inset 0 0 10px rgba(255, 255, 255, 0.1),
        0 20px 50px rgba(0, 0, 0, 0.5),
        0 0 0 10px #1a1a1a;
      background-color: #000;
    }
    
    .device-frame {
      width: 100%;
      height: 100%;
      border: none;
      display: block;
    }
    
    /* 添加电源按钮 */
    .device-container:before {
      content: "";
      position: absolute;
      right: -14px;
      top: 150px;
      width: 4px;
      height: 80px;
      background-color: #1a1a1a;
      border-radius: 2px;
      z-index: 1001;
    }
    
    /* 添加音量按钮 */
    .device-container:after {
      content: "";
      position: absolute;
      left: -14px;
      top: 150px;
      width: 4px;
      height: 120px;
      background-color: #1a1a1a;
      border-radius: 2px;
      z-index: 1001;
    }
    
    /* 添加微妙的反光效果 */
    .device-glare {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.03) 0%,
        rgba(255, 255, 255, 0) 50%,
        rgba(0, 0, 0, 0.1) 100%
      );
      pointer-events: none;
      border-radius: 45px;
      z-index: 1000;
    }
    
    .device-notch {
      position: absolute;
      top: 12px;
      left: 50%;
      transform: translateX(-50%);
      width: 126px;
      height: 37px;
      background-color: #000;
      border-radius: 20px;
      z-index: 1000;
      box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.1);
    }
    
    /* 添加摄像头效果 */
    .device-notch:before {
      content: "";
      position: absolute;
      right: 30px;
      width: 12px;
      height: 12px;
      background-color: rgba(0, 0, 0, 0.8);
      border-radius: 50%;
      box-shadow: inset 0 0 2px rgba(255, 255, 255, 0.5);
      top: 50%;
      transform: translateY(-50%);
    }
    
    /* 底部指示条 */
    .device-home-indicator {
      position: absolute;
      bottom: 8px;
      left: 50%;
      transform: translateX(-50%);
      width: 134px;
      height: 5px;
      background-color: #ffffff;
      border-radius: 3px;
      z-index: 1000;
      opacity: 0.3;
    }
    
    .description {
      max-width: 800px;
      margin: 0 auto 40px;
      line-height: 1.6;
      color: #ccc;
      text-align: center;
      padding: 0 20px;
    }
    
    h1 {
      text-align: center;
      margin: 20px 0;
      color: #fff;
    }
    
    a {
      color: #4cd964;
      text-decoration: none;
    }
  </style>
</head>
<body>
  <h1>Echonote App Prototype</h1>
  
  <div class="description">
    <p>This prototype demonstrates the UI/UX design for the Echonote app, which allows users to capture and organize spoken thoughts instantly using speech recognition. <a href="ux-analysis.html">View UX Analysis</a></p>
  </div>
  
  <div class="prototype-grid">
    <!-- Home Screen -->
    <div class="device-container">
      <!-- 动态岛 -->
      <div class="device-notch"></div>
      
      <!-- 设备框架 -->
      <iframe src="home.html" class="device-frame"></iframe>
      
      <!-- 底部指示条 -->
      <div class="device-home-indicator"></div>
      
      <!-- 屏幕反光效果 -->
      <div class="device-glare"></div>
    </div>
    
    <!-- Keywords Screen -->
    <div class="device-container">
      <!-- 动态岛 -->
      <div class="device-notch"></div>
      
      <!-- 设备框架 -->
      <iframe src="keywords.html" class="device-frame"></iframe>
      
      <!-- 底部指示条 -->
      <div class="device-home-indicator"></div>
      
      <!-- 屏幕反光效果 -->
      <div class="device-glare"></div>
    </div>
    
    <!-- Notes Screen -->
    <div class="device-container">
      <!-- 动态岛 -->
      <div class="device-notch"></div>
      
      <!-- 设备框架 -->
      <iframe src="notes.html" class="device-frame"></iframe>
      
      <!-- 底部指示条 -->
      <div class="device-home-indicator"></div>
      
      <!-- 屏幕反光效果 -->
      <div class="device-glare"></div>
    </div>
    
    <!-- Profile Screen -->
    <div class="device-container">
      <!-- 动态岛 -->
      <div class="device-notch"></div>
      
      <!-- 设备框架 -->
      <iframe src="profile.html" class="device-frame"></iframe>
      
      <!-- 底部指示条 -->
      <div class="device-home-indicator"></div>
      
      <!-- 屏幕反光效果 -->
      <div class="device-glare"></div>
    </div>
    
    <!-- Note Detail -->
    <div class="device-container">
      <!-- 动态岛 -->
      <div class="device-notch"></div>
      
      <!-- 设备框架 -->
      <iframe src="note-detail.html" class="device-frame"></iframe>
      
      <!-- 底部指示条 -->
      <div class="device-home-indicator"></div>
      
      <!-- 屏幕反光效果 -->
      <div class="device-glare"></div>
    </div>
    
    <!-- Edit Note -->
    <div class="device-container">
      <!-- 动态岛 -->
      <div class="device-notch"></div>
      
      <!-- 设备框架 -->
      <iframe src="edit-note.html" class="device-frame"></iframe>
      
      <!-- 底部指示条 -->
      <div class="device-home-indicator"></div>
      
      <!-- 屏幕反光效果 -->
      <div class="device-glare"></div>
    </div>
    
    <!-- Edit Keyword -->
    <div class="device-container">
      <!-- 动态岛 -->
      <div class="device-notch"></div>
      
      <!-- 设备框架 -->
      <iframe src="edit-keyword.html" class="device-frame"></iframe>
      
      <!-- 底部指示条 -->
      <div class="device-home-indicator"></div>
      
      <!-- 屏幕反光效果 -->
      <div class="device-glare"></div>
    </div>
    
    <!-- Search -->
    <div class="device-container">
      <!-- 动态岛 -->
      <div class="device-notch"></div>
      
      <!-- 设备框架 -->
      <iframe src="search.html" class="device-frame"></iframe>
      
      <!-- 底部指示条 -->
      <div class="device-home-indicator"></div>
      
      <!-- 屏幕反光效果 -->
      <div class="device-glare"></div>
    </div>
    
    <!-- Recording Screen -->
    <div class="device-container">
      <!-- 动态岛 -->
      <div class="device-notch"></div>
      
      <!-- 设备框架 -->
      <iframe src="recording.html" class="device-frame"></iframe>
      
      <!-- 底部指示条 -->
      <div class="device-home-indicator"></div>
      
      <!-- 屏幕反光效果 -->
      <div class="device-glare"></div>
    </div>
  </div>
</body>
</html> 