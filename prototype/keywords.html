<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Echonote - Keywords</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <style>
    .content {
      padding: 0 20px;
      padding-bottom: 80px; /* 为浮动按钮留出空间 */
    }
    
    .header {
      padding: 16px 20px;
      padding-top: 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      margin-bottom: 10px;
    }
    
    .header-title {
      display: flex;
      align-items: center;
    }
    
    .header-title h1 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    
    .header-actions i {
      font-size: 18px;
      transition: all 0.2s ease;
    }
    
    .header-actions i:hover {
      transform: scale(1.1);
      opacity: 0.9;
    }
    
    .header-actions i.fa-plus {
      color: #FF9500;
      font-size: 24px;
    }
    
    .header-actions i.fa-plus:active {
      transform: scale(0.95);
    }
    
    /* 添加view-switcher样式 */
    .view-switcher {
      display: flex;
      background-color: rgba(60, 60, 60, 0.7);
      border-radius: 6px;
      padding: 3px;
      margin: 0 auto;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }
    
    .view-option {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
    }
    
    .view-option i {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- iPhone 16 Dynamic Island -->
    <div class="dynamic-island"></div>
    
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal signal-icon"></i>
        <i class="fas fa-wifi wifi-icon"></i>
        <i class="fas fa-battery-full battery-icon"></i>
      </div>
    </div>
    
    <div class="header">
      <div class="header-title">
      </div>
      
      <!-- 添加空的view-switcher，保持结构一致 -->
      <div class="view-switcher" style="visibility: hidden;">
        <div class="view-option">
          <i class="fas fa-th-large"></i>
        </div>
      </div>
      
      <div class="header-actions">
        <i class="fas fa-search" onclick="window.location.href='search.html'" style="margin-right: 15px;"></i>
        <i class="fas fa-plus" onclick="window.location.href='edit-keyword.html'"></i>
      </div>
    </div>
    
    <div class="content">
      <div class="section-title">ACTIVE KEYWORDS</div>
      
      <!-- Keywords List -->
      <div class="keyword-card" data-id="1" onclick="window.location.href='edit-keyword.html'">
        <div class="keyword-icon icon-idea">
          <i class="fas fa-lightbulb"></i>
        </div>
        <div class="keyword-info">
          <h3>Idea</h3>
          <p>12 notes</p>
        </div>
        <i class="fas fa-chevron-right chevron-icon"></i>
      </div>
      
      <div class="keyword-card" data-id="2" onclick="window.location.href='edit-keyword.html'">
        <div class="keyword-icon icon-groceries">
          <i class="fas fa-shopping-basket"></i>
        </div>
        <div class="keyword-info">
          <h3>Groceries</h3>
          <p>3 notes</p>
        </div>
        <i class="fas fa-chevron-right chevron-icon"></i>
      </div>
      
      <div class="keyword-card" data-id="3" onclick="window.location.href='edit-keyword.html'">
        <div class="keyword-icon icon-task">
          <i class="fas fa-tasks"></i>
        </div>
        <div class="keyword-info">
          <h3>Tasks</h3>
          <p>8 notes</p>
        </div>
        <i class="fas fa-chevron-right chevron-icon"></i>
      </div>
      
      <div class="keyword-card" data-id="4" onclick="window.location.href='edit-keyword.html'">
        <div class="keyword-icon icon-meeting">
          <i class="fas fa-users"></i>
        </div>
        <div class="keyword-info">
          <h3>Meetings</h3>
          <p>5 notes</p>
        </div>
        <i class="fas fa-chevron-right chevron-icon"></i>
      </div>
      
      <div class="section-title">INACTIVE KEYWORDS</div>
      
      <div class="keyword-card" data-id="5" onclick="window.location.href='edit-keyword.html'">
        <div class="keyword-icon icon-reminder">
          <i class="fas fa-bell"></i>
        </div>
        <div class="keyword-info">
          <h3>Reminder</h3>
          <p>0 notes</p>
        </div>
        <i class="fas fa-chevron-right chevron-icon"></i>
      </div>
      
      <div class="keyword-card" data-id="6" onclick="window.location.href='edit-keyword.html'">
        <div class="keyword-icon icon-book">
          <i class="fas fa-book"></i>
        </div>
        <div class="keyword-info">
          <h3>Book</h3>
          <p>0 notes</p>
        </div>
        <i class="fas fa-chevron-right chevron-icon"></i>
      </div>
      
      <div class="keyword-card" data-id="7" onclick="window.location.href='edit-keyword.html'">
        <div class="keyword-icon icon-movie">
          <i class="fas fa-film"></i>
        </div>
        <div class="keyword-info">
          <h3>Movie</h3>
          <p>0 notes</p>
        </div>
        <i class="fas fa-chevron-right chevron-icon"></i>
      </div>
      
      <div class="keyword-card" data-id="8" onclick="window.location.href='edit-keyword.html'">
        <div class="keyword-icon icon-recipe">
          <i class="fas fa-utensils"></i>
        </div>
        <div class="keyword-info">
          <h3>Recipe</h3>
          <p>0 notes</p>
        </div>
        <i class="fas fa-chevron-right chevron-icon"></i>
      </div>
    </div>
    
    <!-- Tab Bar -->
    <div class="tab-bar">
      <div class="tab-item" data-page="home.html">
        <i class="fas fa-home tab-icon"></i>
        <span>Home</span>
      </div>
      
      <div class="tab-item active" data-page="keywords.html">
        <i class="fas fa-tags tab-icon"></i>
        <span>Keywords</span>
      </div>
      
      <!-- 添加中间的麦克风按钮 -->
      <div class="tab-item" data-page="recording.html">
        <div class="mic-button">
          <i class="fas fa-microphone mic-icon"></i>
        </div>
      </div>
      
      <div class="tab-item" data-page="notes.html">
        <i class="fas fa-sticky-note tab-icon"></i>
        <span>Notes</span>
      </div>
      
      <div class="tab-item" data-page="profile.html">
        <i class="fas fa-user tab-icon"></i>
        <span>Profile</span>
      </div>
    </div>
    
    <!-- 底部指示条 - iPhone 16特有 -->
    <div class="bottom-indicator"></div>
    
    <!-- Recording Modal (Hidden by default) -->
    <div class="recording-modal" style="display: none;">
      <div class="waveform">
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
      </div>
      
      <div class="recording-status">Listening for keyword...</div>
      <div class="recording-keyword">Say "Idea", "Groceries", etc.</div>
      
      <div class="recording-actions">
        <div class="recording-button pause-button">
          <i class="fas fa-pause"></i>
        </div>
        
        <div class="recording-button stop-button">
          <i class="fas fa-stop"></i>
        </div>
      </div>
    </div>
  </div>
  
  <script src="js/main.js"></script>
  <script>
    // 移除浮动添加按钮相关的JavaScript代码
  </script>
</body>
</html> 