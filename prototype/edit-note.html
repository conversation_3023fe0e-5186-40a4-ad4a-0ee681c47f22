<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Echonote - Edit Note</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <style>
    .edit-note-container {
      padding: 0 20px;
    }
    
    .note-title-input {
      width: 100%;
      background-color: transparent;
      border: none;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      color: var(--text-primary);
      font-size: 20px;
      font-weight: 600;
      padding: 10px 0;
      margin-bottom: 20px;
    }
    
    .note-content-input {
      width: 100%;
      height: 300px;
      background-color: var(--secondary-bg);
      border: none;
      border-radius: var(--border-radius);
      color: var(--text-primary);
      font-size: 16px;
      padding: 15px;
      resize: none;
      margin-bottom: 20px;
    }
    
    .note-tag-selector {
      margin-bottom: 20px;
    }
    
    .note-tag-selector label {
      display: block;
      margin-bottom: 10px;
      color: var(--text-secondary);
    }
    
    .tag-options {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    
    .tag-option {
      padding: 8px 15px;
      border-radius: 20px;
      font-size: 14px;
      background-color: var(--secondary-bg);
      cursor: pointer;
    }
    
    .tag-option.active {
      background-color: var(--accent-green);
      color: var(--primary-bg);
    }
    
    .note-actions {
      display: flex;
      justify-content: space-between;
      margin-top: 30px;
      padding: 0 20px;
      position: absolute;
      top: 0;
      right: 0;
      height: 60px;
      align-items: center;
    }
    
    .action-button {
      padding: 8px 15px;
      border-radius: var(--border-radius);
      font-size: 14px;
      font-weight: 600;
      border: none;
      cursor: pointer;
      margin-left: 10px;
    }
    
    .cancel-button {
      background-color: var(--secondary-bg);
      color: var(--text-primary);
    }
    
    .save-button {
      background-color: var(--accent-green);
      color: var(--primary-bg);
    }
    
    .header {
      position: relative;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- iPhone 16 Dynamic Island -->
    <div class="dynamic-island"></div>
    
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal signal-icon"></i>
        <i class="fas fa-wifi wifi-icon"></i>
        <i class="fas fa-battery-full battery-icon"></i>
      </div>
    </div>
    
    <div class="header">
      <h1>Edit Note</h1>
      <div class="note-actions">
        <i class="fas fa-search" onclick="window.location.href='search.html'" style="margin-right: 15px;"></i>
        <button class="action-button cancel-button" onclick="window.location.href='notes.html'">Cancel</button>
        <button class="action-button save-button" onclick="window.location.href='notes.html'">Save</button>
      </div>
    </div>
    
    <div class="content">
      <div class="edit-note-container">
        <input type="text" class="note-title-input" placeholder="Note Title" value="App Concept">
        
        <textarea class="note-content-input" placeholder="Write your note here...">Create a mobile app for tracking daily water intake with reminders and achievements. The app would feature a clean, minimal interface with daily goals, weekly stats, and customizable reminders. Users could track their progress over time and earn achievements for consistency.</textarea>
        
        <div class="note-tag-selector">
          <label>Select Keyword:</label>
          <div class="tag-options">
            <div class="tag-option active">Idea</div>
            <div class="tag-option">Groceries</div>
            <div class="tag-option">Meeting</div>
            <div class="tag-option">Task</div>
            <div class="tag-option">Work</div>
            <div class="tag-option">Personal</div>
            <div class="tag-option">Project</div>
            <div class="tag-option" id="add-keyword"><i class="fas fa-plus"></i></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Tab Bar -->
    <div class="tab-bar">
      <div class="tab-item" data-page="home.html">
        <i class="fas fa-home tab-icon"></i>
        <span>Home</span>
      </div>
      
      <div class="tab-item" data-page="keywords.html">
        <i class="fas fa-tags tab-icon"></i>
        <span>Keywords</span>
      </div>
      
      <div class="mic-button">
        <i class="fas fa-microphone mic-icon"></i>
      </div>
      
      <div class="tab-item active" data-page="notes.html">
        <i class="fas fa-sticky-note tab-icon"></i>
        <span>Notes</span>
      </div>
      
      <div class="tab-item" data-page="profile.html">
        <i class="fas fa-user tab-icon"></i>
        <span>Profile</span>
      </div>
    </div>
  </div>
  
  <script src="js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const tagOptions = document.querySelectorAll('.tag-option');
      
      tagOptions.forEach(tag => {
        tag.addEventListener('click', () => {
          // Remove active class from all tags
          tagOptions.forEach(t => t.classList.remove('active'));
          
          // Add active class to clicked tag
          if (tag.id !== 'add-keyword') {
            tag.classList.add('active');
          } else {
            // Navigate to edit-keyword page
            window.location.href = 'edit-keyword.html';
          }
        });
      });
    });
  </script>
</body>
</html> 