<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Echonote - Profile</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <style>
    .toggle-switch {
      width: 50px;
      height: 26px;
      background-color: #333;
      border-radius: 13px;
      position: relative;
      cursor: pointer;
      transition: background-color 0.3s;
      user-select: none;
      -webkit-user-select: none;
      z-index: 2;
    }
    
    .toggle-switch::before {
      content: '';
      position: absolute;
      width: 22px;
      height: 22px;
      border-radius: 50%;
      background-color: white;
      top: 2px;
      left: 2px;
      transition: transform 0.3s;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    }
    
    .toggle-switch.active {
      background-color: var(--accent-green);
    }
    
    .toggle-switch.active::before {
      transform: translateX(24px);
    }
    
    /* 添加按下效果 */
    .toggle-switch:active::before {
      width: 28px;
    }
    
    .toggle-switch.active:active::before {
      transform: translateX(18px);
      width: 28px;
    }
    
    /* 添加触摸效果支持 */
    @media (hover: none) {
      .toggle-switch {
        -webkit-tap-highlight-color: transparent;
      }
    }
    
    .slider-value {
      color: var(--accent-green);
      font-weight: bold;
    }
    
    .premium-badge {
      background-color: var(--accent-yellow);
      color: var(--primary-bg);
      padding: 2px 8px;
      border-radius: 10px;
      font-size: 12px;
      font-weight: bold;
    }
    
    .section-divider {
      height: 1px;
      background-color: rgba(255, 255, 255, 0.1);
      margin: 16px 0;
    }
    
    .settings-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
    }
    
    .icon-moon { background-color: #5856D6; }
    .icon-haptic { background-color: #FF2D55; }
    .icon-globe { background-color: #5AC8FA; }
    .icon-volume { background-color: #FF9500; }
    .icon-stopwatch { background-color: #4CD964; }
    .icon-crown { background-color: #FFCC00; }
    .icon-card { background-color: #34C759; }
    .icon-ticket { background-color: #AF52DE; }
    .icon-user-plus { background-color: #FF3B30; }
    .icon-info { background-color: #007AFF; }
    .icon-shield { background-color: #5856D6; }
    .icon-file { background-color: #FF9500; }
    .icon-signout { background-color: #FF3B30; }
    
    .language-select {
      background-color: #333;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 6px 10px;
      font-size: 14px;
      appearance: none;
      -webkit-appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='white' d='M6 8.825L1.175 4 2.05 3.125 6 7.075 9.95 3.125 10.825 4 6 8.825z'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 8px center;
      padding-right: 24px;
      cursor: pointer;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translate(-50%, 20px); }
      to { opacity: 1; transform: translate(-50%, 0); }
    }
    
    .fade-in {
      animation: fadeIn 0.3s ease-out forwards;
    }
    
    .toast {
      transition: opacity 0.3s ease;
    }
    
    .header {
      padding: 16px 20px;
      padding-top: 25px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    
    /* 增强开关的可点击区域 */
    .settings-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      margin-bottom: 8px;
      background-color: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .settings-item:active {
      background-color: rgba(255, 255, 255, 0.1);
    }
    
    .settings-item-label {
      display: flex;
      align-items: center;
      flex: 1;
    }
    
    /* 语言设置项样式 */
    .language-setting {
      cursor: default !important;
    }
    
    .language-select {
      cursor: pointer;
    }
    
    /* Siri Shortcut按钮样式 */
    .siri-button {
      display: flex;
      align-items: center;
      background-color: rgba(90, 200, 250, 0.1);
      border-radius: 16px;
      padding: 6px 12px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .siri-button:hover, .siri-button:active {
      background-color: rgba(90, 200, 250, 0.2);
    }
    
    /* 添加Siri快捷方式模态框样式 */
    .siri-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }
    
    .siri-modal.active {
      opacity: 1;
      visibility: visible;
    }
    
    .siri-modal-content {
      background-color: var(--primary-bg);
      border-radius: 16px;
      width: 85%;
      max-width: 340px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
      transform: translateY(20px);
      transition: transform 0.3s ease;
    }
    
    .siri-modal.active .siri-modal-content {
      transform: translateY(0);
    }
    
    .siri-logo {
      width: 60px;
      height: 60px;
      margin: 0 auto 15px;
      background: linear-gradient(135deg, #5AC8FA, #007AFF);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .siri-logo i {
      font-size: 30px;
      color: white;
    }
    
    .siri-modal h3 {
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 18px;
      color: var(--text-primary);
    }
    
    .siri-modal p {
      margin-bottom: 20px;
      font-size: 14px;
      color: var(--text-secondary);
      line-height: 1.5;
    }
    
    .siri-modal-buttons {
      display: flex;
      justify-content: space-between;
    }
    
    .siri-modal-button {
      flex: 1;
      padding: 10px;
      border-radius: 10px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      text-align: center;
      transition: background-color 0.2s;
    }
    
    .siri-modal-button.cancel {
      background-color: rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
      margin-right: 10px;
    }
    
    .siri-modal-button.install {
      background-color: #007AFF;
      color: white;
    }
    
    .siri-modal-button:active {
      opacity: 0.8;
    }
    
    .profile-pic {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      border: 3px solid var(--accent-color);
      object-fit: cover;
    }
    
    /* 头像上传相关样式 */
    .profile-pic-container {
      position: relative;
      width: 80px;
      height: 80px;
      cursor: pointer;
    }
    
    .profile-pic-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .profile-pic-container:hover .profile-pic-overlay {
      opacity: 1;
    }
    
    .profile-pic-overlay i {
      color: white;
      font-size: 24px;
    }
    
    .profile-header {
      display: flex;
      align-items: center;
      padding: 20px;
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- iPhone 16 Dynamic Island -->
    <div class="dynamic-island"></div>
    
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal signal-icon"></i>
        <i class="fas fa-wifi wifi-icon"></i>
        <i class="fas fa-battery-full battery-icon"></i>
      </div>
    </div>
    
    <div class="header">
      <div class="header-actions">
        <i class="fas fa-search" onclick="window.location.href='search.html'" style="margin-right: 15px;"></i>
      </div>
    </div>
    
    <div class="content">
      <!-- Profile Header -->
      <div class="profile-header">
        <div class="profile-pic-container">
          <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Profile Picture" class="profile-pic" id="profile-pic">
          <input type="file" id="profile-pic-upload" accept="image/*" style="display: none;">
          <div class="profile-pic-overlay">
            <i class="fas fa-camera"></i>
          </div>
        </div>
        <div class="profile-info">
          <h2>Alex Johnson</h2>
          <p><EMAIL></p>
          <span class="premium-badge">Premium</span>
        </div>
      </div>
      
      <!-- App Preferences -->
      <div class="settings-section">
        <h2>App Preferences</h2>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon icon-moon">
              <i class="fas fa-moon" style="color: white;"></i>
            </div>
            <span>Dark Mode</span>
          </div>
          <div class="toggle-switch active"></div>
        </div>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon icon-haptic">
              <i class="fas fa-hand-sparkles" style="color: white;"></i>
            </div>
            <span>Haptic Feedback</span>
          </div>
          <div class="toggle-switch active"></div>
        </div>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon" style="background-color: #FF2D55;">
              <i class="fas fa-microphone" style="color: white;"></i>
            </div>
            <span>Microphone Animations</span>
          </div>
          <div class="toggle-switch active" id="mic-animations-toggle"></div>
        </div>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon" style="background-color: #007AFF;">
              <i class="fas fa-chart-pie" style="color: white;"></i>
            </div>
            <span>Show Notes Overview</span>
          </div>
          <div class="toggle-switch active" id="show-overview-toggle"></div>
        </div>
        
        <div class="settings-item language-setting">
          <div class="settings-item-label">
            <div class="settings-icon icon-globe">
              <i class="fas fa-globe" style="color: white;"></i>
            </div>
            <span>Language</span>
          </div>
          <select class="language-select" id="language-select">
            <option value="en">English</option>
            <option value="es">Español</option>
            <option value="fr">Français</option>
            <option value="de">Deutsch</option>
            <option value="zh">中文</option>
            <option value="ja">日本語</option>
            <option value="ko">한국어</option>
          </select>
        </div>
      </div>
      
      <!-- Recording Options -->
      <div class="settings-section">
        <h2>Recording Options</h2>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon icon-volume">
              <i class="fas fa-volume-mute" style="color: white;"></i>
            </div>
            <span>Auto Stop After Silence</span>
          </div>
          <span class="slider-value">3s</span>
        </div>
        
        <div class="slider-container">
          <input type="range" min="1" max="10" value="3" class="slider">
        </div>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon icon-stopwatch">
              <i class="fas fa-stopwatch" style="color: white;"></i>
            </div>
            <span>Maximum Recording Duration</span>
          </div>
          <span class="slider-value">60s</span>
        </div>
        
        <div class="slider-container">
          <input type="range" min="30" max="300" step="30" value="60" class="slider">
        </div>
        
        <div class="settings-item" id="siri-shortcut-button">
          <div class="settings-item-label">
            <div class="settings-icon" style="background-color: #5AC8FA;">
              <i class="fab fa-apple" style="color: white;"></i>
            </div>
            <span>Add to Siri</span>
          </div>
          <div class="siri-button">
            <i class="fas fa-plus-circle" style="color: #5AC8FA; margin-right: 5px;"></i>
            <span style="color: #5AC8FA;">Shortcut</span>
          </div>
        </div>
      </div>
      
      <!-- Subscription & Account -->
      <div class="settings-section">
        <h2>Subscription & Account</h2>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon icon-crown">
              <i class="fas fa-crown" style="color: white;"></i>
            </div>
            <span>Premium Subscription</span>
          </div>
          <span>Active</span>
        </div>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon icon-card">
              <i class="fas fa-credit-card" style="color: white;"></i>
            </div>
            <span>Manage Subscription</span>
          </div>
          <i class="fas fa-chevron-right"></i>
        </div>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon icon-ticket">
              <i class="fas fa-ticket-alt" style="color: white;"></i>
            </div>
            <span>Redeem Promo Code</span>
          </div>
          <i class="fas fa-chevron-right"></i>
        </div>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon icon-user-plus">
              <i class="fas fa-user-plus" style="color: white;"></i>
            </div>
            <span>Invite Friends</span>
          </div>
          <i class="fas fa-chevron-right"></i>
        </div>
      </div>
      
      <!-- About -->
      <div class="settings-section">
        <h2>About</h2>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon icon-info">
              <i class="fas fa-info-circle" style="color: white;"></i>
            </div>
            <span>About Echonote</span>
          </div>
          <i class="fas fa-chevron-right"></i>
        </div>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon icon-shield">
              <i class="fas fa-shield-alt" style="color: white;"></i>
            </div>
            <span>Privacy Policy</span>
          </div>
          <i class="fas fa-chevron-right"></i>
        </div>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon icon-file">
              <i class="fas fa-file-contract" style="color: white;"></i>
            </div>
            <span>Terms of Service</span>
          </div>
          <i class="fas fa-chevron-right"></i>
        </div>
        
        <div class="settings-item">
          <div class="settings-item-label">
            <div class="settings-icon icon-signout">
              <i class="fas fa-sign-out-alt" style="color: white;"></i>
            </div>
            <span>Sign Out</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Tab Bar -->
    <div class="tab-bar">
      <div class="tab-item" data-page="home.html">
        <i class="fas fa-home tab-icon"></i>
        <span>Home</span>
      </div>
      
      <div class="tab-item" data-page="keywords.html">
        <i class="fas fa-tags tab-icon"></i>
        <span>Keywords</span>
      </div>
      
      <div class="tab-item" data-page="recording.html">
        <div class="mic-button">
          <i class="fas fa-microphone mic-icon"></i>
        </div>
      </div>
      
      <div class="tab-item" data-page="notes.html">
        <i class="fas fa-sticky-note tab-icon"></i>
        <span>Notes</span>
      </div>
      
      <div class="tab-item active" data-page="profile.html">
        <i class="fas fa-user tab-icon"></i>
        <span>Profile</span>
      </div>
    </div>
    
    <!-- 底部指示条 - iPhone 16特有 -->
    <div class="bottom-indicator"></div>
    
    <!-- Siri Shortcut Modal -->
    <div class="siri-modal" id="siri-modal">
      <div class="siri-modal-content">
        <div class="siri-logo">
          <i class="fab fa-apple"></i>
        </div>
        <h3>Add to Siri</h3>
        <p>Add EchoNote to Siri to quickly record notes even when your phone is locked. Just say "Hey Siri, record a note with EchoNote".</p>
        <div class="siri-modal-buttons">
          <div class="siri-modal-button cancel" id="siri-cancel">Cancel</div>
          <div class="siri-modal-button install" id="siri-install">Add to Siri</div>
        </div>
      </div>
    </div>
    
    <!-- Recording Modal (Hidden by default) -->
    <div class="recording-modal" style="display: none;">
      <div class="waveform">
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
      </div>
      
      <div class="recording-status">Listening for keyword...</div>
      <div class="recording-keyword">Say "Idea", "Groceries", etc.</div>
      
      <div class="recording-actions">
        <div class="recording-button pause-button">
          <i class="fas fa-pause"></i>
        </div>
        
        <div class="recording-button stop-button">
          <i class="fas fa-stop"></i>
        </div>
      </div>
    </div>
  </div>
  
  <script src="js/main.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 头像上传功能
      const profilePic = document.getElementById('profile-pic');
      const profilePicContainer = document.querySelector('.profile-pic-container');
      const profilePicUpload = document.getElementById('profile-pic-upload');
      
      // 点击头像打开文件选择器
      profilePicContainer.addEventListener('click', function() {
        profilePicUpload.click();
      });
      
      // 处理文件选择
      profilePicUpload.addEventListener('change', function(event) {
        const file = event.target.files[0];
        if (file && file.type.match('image.*')) {
          const reader = new FileReader();
          
          reader.onload = function(e) {
            profilePic.src = e.target.result;
            showToast('Profile picture updated!');
          };
          
          reader.readAsDataURL(file);
        }
      });
      
      // 显示提示消息
      function showToast(message) {
        const toast = document.createElement('div');
        toast.className = 'toast fade-in';
        toast.style.position = 'fixed';
        toast.style.bottom = '20px';
        toast.style.left = '50%';
        toast.style.transform = 'translateX(-50%)';
        toast.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        toast.style.color = 'white';
        toast.style.padding = '10px 20px';
        toast.style.borderRadius = '20px';
        toast.style.zIndex = '1000';
        toast.innerText = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
          toast.style.opacity = '0';
          setTimeout(() => {
            document.body.removeChild(toast);
          }, 300);
        }, 2000);
      }
      
      // 其他现有的JavaScript代码
      // Toggle switches
      const toggleSwitches = document.querySelectorAll('.toggle-switch');
      toggleSwitches.forEach(toggle => {
        toggle.addEventListener('click', function() {
          this.classList.toggle('active');
          
          // 特殊处理麦克风动画开关
          if (this.id === 'mic-animations-toggle') {
            // 保存设置到本地存储
            localStorage.setItem('micAnimationsEnabled', this.classList.contains('active'));
          }
          
          // 特殊处理Notes Overview开关
          if (this.id === 'show-overview-toggle') {
            // 保存设置到本地存储
            localStorage.setItem('showNotesOverview', this.classList.contains('active'));
          }
        });
      });
      
      // 初始化开关状态
      const micAnimationsToggle = document.getElementById('mic-animations-toggle');
      const showOverviewToggle = document.getElementById('show-overview-toggle');
      
      // 从本地存储加载设置
      if (localStorage.getItem('micAnimationsEnabled') === 'false') {
        micAnimationsToggle.classList.remove('active');
      }
      
      if (localStorage.getItem('showNotesOverview') === 'false') {
        showOverviewToggle.classList.remove('active');
      }
      
      // Sliders
      const sliders = document.querySelectorAll('.slider');
      sliders.forEach(slider => {
        const sliderValue = slider.parentElement.previousElementSibling.querySelector('.slider-value');
        
        slider.addEventListener('input', function() {
          const value = this.value;
          const unit = sliderValue.textContent.slice(-1);
          sliderValue.textContent = value + unit;
        });
      });
      
      // Siri Shortcut
      const siriButton = document.getElementById('siri-shortcut-button');
      const siriModal = document.createElement('div');
      siriModal.className = 'siri-modal';
      siriModal.innerHTML = `
        <div class="siri-modal-content">
          <div class="siri-logo">
            <i class="fab fa-apple"></i>
          </div>
          <h3>Add to Siri</h3>
          <p>Create a Siri shortcut to record notes even when your phone is locked.</p>
          <div class="siri-modal-buttons">
            <div class="siri-modal-button cancel">Cancel</div>
            <div class="siri-modal-button install">Add to Siri</div>
          </div>
        </div>
      `;
      document.body.appendChild(siriModal);
      
      siriButton.addEventListener('click', function() {
        siriModal.classList.add('active');
      });
      
      const cancelButton = siriModal.querySelector('.cancel');
      cancelButton.addEventListener('click', function() {
        siriModal.classList.remove('active');
      });
      
      const installButton = siriModal.querySelector('.install');
      installButton.addEventListener('click', function() {
        siriModal.classList.remove('active');
        showToast('Shortcut added to Siri!');
      });
    });
  </script>
</body>
</html> 