// Main JavaScript for Echonote App Prototype

document.addEventListener('DOMContentLoaded', () => {
  // Handle tab navigation
  const tabItems = document.querySelectorAll('.tab-item');
  
  tabItems.forEach(item => {
    item.addEventListener('click', () => {
      // Get the target page from data attribute
      const targetPage = item.getAttribute('data-page');
      if (targetPage) {
        // Navigate to the target page
        window.parent.location.href = targetPage;
      }
    });
  });

  // Handle recording modal
  const micButton = document.querySelector('.mic-button');
  const recordingModal = document.querySelector('.recording-modal');
  const stopButton = document.querySelector('.stop-button');
  const pauseButton = document.querySelector('.pause-button');

  if (micButton) {
    micButton.addEventListener('click', () => {
      if (recordingModal) {
        recordingModal.style.display = 'flex';
        recordingModal.classList.add('fade-in');
        
        // Simulate recording animation
        animateWaveform();
        
        // Simulate keyword detection after 3 seconds
        setTimeout(() => {
          const recordingStatus = document.querySelector('.recording-status');
          const recordingKeyword = document.querySelector('.recording-keyword');
          
          if (recordingStatus && recordingKeyword) {
            recordingStatus.textContent = 'Keyword detected: "Idea"';
            recordingKeyword.textContent = 'Recording note...';
          }
          
          // Simulate note completion after another 5 seconds
          setTimeout(() => {
            if (recordingModal) {
              recordingModal.style.display = 'none';
              
              // Show success message
              showToast('Note saved successfully!');
              
              // Redirect to notes page after recording (if not already there)
              if (!window.location.href.includes('notes.html')) {
                setTimeout(() => {
                  window.location.href = 'notes.html';
                }, 1000);
              }
            }
          }, 5000);
        }, 3000);
      }
    });
  }

  if (stopButton) {
    stopButton.addEventListener('click', () => {
      if (recordingModal) {
        recordingModal.style.display = 'none';
      }
    });
  }

  if (pauseButton) {
    pauseButton.addEventListener('click', () => {
      // Toggle pause state
      pauseButton.classList.toggle('paused');
    });
  }

  // Handle tag selection
  const tags = document.querySelectorAll('.tag');
  
  tags.forEach(tag => {
    tag.addEventListener('click', () => {
      // Remove active class from all tags
      tags.forEach(t => t.classList.remove('active'));
      
      // Add active class to clicked tag
      tag.classList.add('active');
    });
  });

  // Simulate chart animations
  const chartElements = document.querySelectorAll('.donut-chart');
  
  chartElements.forEach(chart => {
    // Add animation class
    chart.classList.add('fade-in');
  });

  // Handle keyword actions
  const editButtons = document.querySelectorAll('.edit-button');
  const deleteButtons = document.querySelectorAll('.delete-button');
  
  editButtons.forEach(button => {
    button.addEventListener('click', (e) => {
      e.stopPropagation();
      window.location.href = 'edit-keyword.html';
    });
  });
  
  deleteButtons.forEach(button => {
    button.addEventListener('click', (e) => {
      e.stopPropagation();
      if (confirm('Are you sure you want to delete this keyword?')) {
        const keywordCard = button.closest('.keyword-card');
        if (keywordCard) {
          keywordCard.style.opacity = '0';
          setTimeout(() => {
            keywordCard.style.display = 'none';
          }, 300);
        }
      }
    });
  });

  // Handle add keyword button
  const addButton = document.querySelector('.add-button');
  
  if (addButton) {
    addButton.addEventListener('click', () => {
      window.location.href = 'edit-keyword.html';
    });
  }

  // Handle settings toggles
  const toggleSwitches = document.querySelectorAll('.toggle-switch');
  
  toggleSwitches.forEach(toggle => {
    toggle.addEventListener('click', () => {
      toggle.classList.toggle('active');
      
      // Show feedback when a setting is changed
      showToast('Setting updated');
    });
  });

  // Handle sliders
  const sliders = document.querySelectorAll('.slider');
  const sliderValues = document.querySelectorAll('.slider-value');
  
  sliders.forEach((slider, index) => {
    slider.addEventListener('input', () => {
      if (sliderValues[index]) {
        sliderValues[index].textContent = slider.value;
      }
    });
    
    slider.addEventListener('change', () => {
      showToast('Setting updated');
    });
  });
  
  // Quick action buttons in home screen
  const quickActionBtns = document.querySelectorAll('.quick-action-btn');
  
  if (quickActionBtns.length > 0) {
    quickActionBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const action = btn.querySelector('span').textContent;
        
        switch (action) {
          case 'New Note':
            window.location.href = 'edit-note.html';
            break;
          case 'Search':
            window.location.href = 'search.html';
            break;
          case 'Favorites':
            window.location.href = 'notes.html';
            // Would set a filter for favorites
            break;
        }
      });
    });
  }
});

// Helper function to animate waveform
function animateWaveform() {
  const waveBars = document.querySelectorAll('.wave-bar');
  
  if (waveBars.length > 0) {
    setInterval(() => {
      waveBars.forEach(bar => {
        const randomHeight = Math.floor(Math.random() * 50) + 10;
        bar.style.height = `${randomHeight}px`;
      });
    }, 100);
  }
}

// Helper function to show toast messages
function showToast(message) {
  // Create toast element if it doesn't exist
  let toast = document.querySelector('.toast-message');
  
  if (!toast) {
    toast = document.createElement('div');
    toast.className = 'toast-message';
    document.body.appendChild(toast);
    
    // Add toast styles
    toast.style.position = 'fixed';
    toast.style.bottom = '80px';
    toast.style.left = '50%';
    toast.style.transform = 'translateX(-50%)';
    toast.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    toast.style.color = 'white';
    toast.style.padding = '10px 20px';
    toast.style.borderRadius = '20px';
    toast.style.fontSize = '14px';
    toast.style.zIndex = '9999';
    toast.style.opacity = '0';
    toast.style.transition = 'opacity 0.3s ease-in-out';
  }
  
  // Set message and show toast
  toast.textContent = message;
  toast.style.opacity = '1';
  
  // Hide toast after 2 seconds
  setTimeout(() => {
    toast.style.opacity = '0';
  }, 2000);
} 