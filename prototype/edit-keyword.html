<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Echonote - Edit Keyword</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <style>
    .edit-keyword-container {
      padding: 0 20px;
    }
    
    .keyword-input-group {
      margin-bottom: 30px;
    }
    
    .keyword-input-label {
      display: block;
      color: var(--text-secondary);
      font-size: 14px;
      margin-bottom: 10px;
    }
    
    .keyword-input {
      width: 100%;
      background-color: var(--secondary-bg);
      border: none;
      border-radius: var(--border-radius);
      color: var(--text-primary);
      font-size: 16px;
      padding: 15px;
      margin-bottom: 5px;
    }
    
    .keyword-input-help {
      font-size: 12px;
      color: var(--text-secondary);
      margin-top: 5px;
    }
    
    .keyword-preview {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-bottom: 30px;
      background-color: var(--secondary-bg);
      padding: 15px;
      border-radius: var(--border-radius);
    }
    
    .preview-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      background-color: #FF3B30;
    }
    
    .preview-icon i {
      font-size: 24px;
      color: white;
    }
    
    .preview-text {
      flex-grow: 1;
    }
    
    .preview-text h3 {
      margin: 0 0 5px 0;
      font-size: 18px;
    }
    
    .preview-text p {
      margin: 0;
      font-size: 14px;
      color: var(--text-secondary);
    }
    
    .color-options {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-top: 15px;
    }
    
    .color-option {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      cursor: pointer;
    }
    
    .color-option.active {
      border: 2px solid white;
      box-shadow: 0 0 0 2px var(--accent-green);
    }
    
    .icon-options {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-top: 15px;
      max-height: 220px;
      overflow-y: auto;
      padding-right: 5px;
    }
    
    .icon-options::-webkit-scrollbar {
      width: 4px;
    }
    
    .icon-options::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 10px;
    }
    
    .icon-options::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 10px;
    }
    
    .icon-option {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background-color: var(--secondary-bg);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
    
    .icon-option.active {
      border: 2px solid white;
      box-shadow: 0 0 0 2px var(--accent-green);
    }
    
    .icon-option i {
      font-size: 20px;
      color: var(--text-primary);
    }
    
    .red {
      background-color: #FF3B30;
    }
    
    .green {
      background-color: #34C759;
    }
    
    .yellow {
      background-color: #FFCC00;
    }
    
    .blue {
      background-color: #007AFF;
    }
    
    .purple {
      background-color: #AF52DE;
    }
    
    .orange {
      background-color: #FF9500;
    }
    
    .teal {
      background-color: #5AC8FA;
    }
    
    .pink {
      background-color: #FF2D55;
    }
    
    .keyword-actions {
      display: flex;
      justify-content: space-between;
      margin-top: 40px;
      margin-bottom: 20px;
    }
    
    .action-button {
      padding: 12px 20px;
      border-radius: var(--border-radius);
      font-size: 16px;
      font-weight: 600;
      border: none;
      cursor: pointer;
    }
    
    .cancel-button {
      background-color: var(--secondary-bg);
      color: var(--text-primary);
    }
    
    .save-button {
      background-color: var(--accent-green);
      color: var(--primary-bg);
    }
    
    .delete-keyword {
      text-align: center;
      margin-top: 30px;
      margin-bottom: 30px;
    }
    
    .delete-button {
      color: #FF3B30;
      background: none;
      border: none;
      font-size: 16px;
      cursor: pointer;
      padding: 10px 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- iPhone 16 Dynamic Island -->
    <div class="dynamic-island"></div>
    
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal signal-icon"></i>
        <i class="fas fa-wifi wifi-icon"></i>
        <i class="fas fa-battery-full battery-icon"></i>
      </div>
    </div>
    
    <div class="header">
      <div class="back-button" onclick="window.location.href='keywords.html'">
        <i class="fas fa-chevron-left"></i>
      </div>
      <h1>Edit Keyword</h1>
      <div class="header-actions">
        <i class="fas fa-search" onclick="window.location.href='search.html'" style="margin-right: 15px;"></i>
      </div>
    </div>
    
    <div class="content">
      <div class="edit-keyword-container">
        <div class="keyword-preview">
          <div class="preview-icon" id="preview-icon">
            <i class="fas fa-lightbulb" id="preview-icon-symbol"></i>
          </div>
          <div class="preview-text">
            <h3 id="preview-title">Idea</h3>
            <p>Keyword Preview</p>
          </div>
        </div>
        
        <div class="keyword-input-group">
          <label class="keyword-input-label">Keyword Name</label>
          <input type="text" class="keyword-input" id="keyword-name" value="Idea" placeholder="Enter keyword">
          <p class="keyword-input-help">This is the word that will trigger note creation when spoken</p>
        </div>
        
        <div class="keyword-input-group">
          <label class="keyword-input-label">Icon</label>
          <div class="icon-options">
            <div class="icon-option active" data-icon="fa-lightbulb">
              <i class="fas fa-lightbulb"></i>
            </div>
            <div class="icon-option" data-icon="fa-shopping-basket">
              <i class="fas fa-shopping-basket"></i>
            </div>
            <div class="icon-option" data-icon="fa-tasks">
              <i class="fas fa-tasks"></i>
            </div>
            <div class="icon-option" data-icon="fa-users">
              <i class="fas fa-users"></i>
            </div>
            <div class="icon-option" data-icon="fa-bell">
              <i class="fas fa-bell"></i>
            </div>
            <div class="icon-option" data-icon="fa-book">
              <i class="fas fa-book"></i>
            </div>
            <div class="icon-option" data-icon="fa-film">
              <i class="fas fa-film"></i>
            </div>
            <div class="icon-option" data-icon="fa-utensils">
              <i class="fas fa-utensils"></i>
            </div>
            <div class="icon-option" data-icon="fa-calendar">
              <i class="fas fa-calendar"></i>
            </div>
            <div class="icon-option" data-icon="fa-car">
              <i class="fas fa-car"></i>
            </div>
            <div class="icon-option" data-icon="fa-plane">
              <i class="fas fa-plane"></i>
            </div>
            <div class="icon-option" data-icon="fa-music">
              <i class="fas fa-music"></i>
            </div>
            <div class="icon-option" data-icon="fa-graduation-cap">
              <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="icon-option" data-icon="fa-briefcase">
              <i class="fas fa-briefcase"></i>
            </div>
            <div class="icon-option" data-icon="fa-heart">
              <i class="fas fa-heart"></i>
            </div>
            <div class="icon-option" data-icon="fa-gift">
              <i class="fas fa-gift"></i>
            </div>
            <div class="icon-option" data-icon="fa-coffee">
              <i class="fas fa-coffee"></i>
            </div>
            <div class="icon-option" data-icon="fa-code">
              <i class="fas fa-code"></i>
            </div>
            <div class="icon-option" data-icon="fa-paw">
              <i class="fas fa-paw"></i>
            </div>
            <div class="icon-option" data-icon="fa-running">
              <i class="fas fa-running"></i>
            </div>
            <div class="icon-option" data-icon="fa-dumbbell">
              <i class="fas fa-dumbbell"></i>
            </div>
            <div class="icon-option" data-icon="fa-baby">
              <i class="fas fa-baby"></i>
            </div>
            <div class="icon-option" data-icon="fa-home">
              <i class="fas fa-home"></i>
            </div>
            <div class="icon-option" data-icon="fa-gamepad">
              <i class="fas fa-gamepad"></i>
            </div>
            <div class="icon-option" data-icon="fa-paint-brush">
              <i class="fas fa-paint-brush"></i>
            </div>
            <div class="icon-option" data-icon="fa-camera">
              <i class="fas fa-camera"></i>
            </div>
            <div class="icon-option" data-icon="fa-laptop">
              <i class="fas fa-laptop"></i>
            </div>
            <div class="icon-option" data-icon="fa-glasses">
              <i class="fas fa-glasses"></i>
            </div>
            <div class="icon-option" data-icon="fa-stethoscope">
              <i class="fas fa-stethoscope"></i>
            </div>
            <div class="icon-option" data-icon="fa-suitcase">
              <i class="fas fa-suitcase"></i>
            </div>
            <div class="icon-option" data-icon="fa-money-bill">
              <i class="fas fa-money-bill"></i>
            </div>
            <div class="icon-option" data-icon="fa-chart-line">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="icon-option" data-icon="fa-seedling">
              <i class="fas fa-seedling"></i>
            </div>
            <div class="icon-option" data-icon="fa-birthday-cake">
              <i class="fas fa-birthday-cake"></i>
            </div>
            <div class="icon-option" data-icon="fa-map-marker-alt">
              <i class="fas fa-map-marker-alt"></i>
            </div>
            <div class="icon-option" data-icon="fa-ticket-alt">
              <i class="fas fa-ticket-alt"></i>
            </div>
            <div class="icon-option" data-icon="fa-newspaper">
              <i class="fas fa-newspaper"></i>
            </div>
            <div class="icon-option" data-icon="fa-phone">
              <i class="fas fa-phone"></i>
            </div>
            <div class="icon-option" data-icon="fa-envelope">
              <i class="fas fa-envelope"></i>
            </div>
            <div class="icon-option" data-icon="fa-comment">
              <i class="fas fa-comment"></i>
            </div>
            <div class="icon-option" data-icon="fa-sun">
              <i class="fas fa-sun"></i>
            </div>
            <div class="icon-option" data-icon="fa-moon">
              <i class="fas fa-moon"></i>
            </div>
            <div class="icon-option" data-icon="fa-cloud">
              <i class="fas fa-cloud"></i>
            </div>
            <div class="icon-option" data-icon="fa-snowflake">
              <i class="fas fa-snowflake"></i>
            </div>
            <div class="icon-option" data-icon="fa-fire">
              <i class="fas fa-fire"></i>
            </div>
            <div class="icon-option" data-icon="fa-leaf">
              <i class="fas fa-leaf"></i>
            </div>
            <div class="icon-option" data-icon="fa-apple-alt">
              <i class="fas fa-apple-alt"></i>
            </div>
            <div class="icon-option" data-icon="fa-wine-glass">
              <i class="fas fa-wine-glass"></i>
            </div>
            <div class="icon-option" data-icon="fa-pizza-slice">
              <i class="fas fa-pizza-slice"></i>
            </div>
            <div class="icon-option" data-icon="fa-hamburger">
              <i class="fas fa-hamburger"></i>
            </div>
            <div class="icon-option" data-icon="fa-ice-cream">
              <i class="fas fa-ice-cream"></i>
            </div>
          </div>
        </div>
        
        <div class="keyword-input-group">
          <label class="keyword-input-label">Color</label>
          <div class="color-options">
            <div class="color-option red active" data-color="#FF3B30"></div>
            <div class="color-option green" data-color="#34C759"></div>
            <div class="color-option yellow" data-color="#FFCC00"></div>
            <div class="color-option blue" data-color="#007AFF"></div>
            <div class="color-option purple" data-color="#AF52DE"></div>
            <div class="color-option orange" data-color="#FF9500"></div>
            <div class="color-option teal" data-color="#5AC8FA"></div>
            <div class="color-option pink" data-color="#FF2D55"></div>
          </div>
        </div>
        
        <div class="keyword-input-group">
          <label class="keyword-input-label">Auto-Stop Recording After (seconds)</label>
          <input type="number" class="keyword-input" value="3" min="1" max="10">
          <p class="keyword-input-help">Recording will automatically stop after this many seconds of silence</p>
        </div>
        
        <div class="keyword-actions">
          <button class="action-button cancel-button" onclick="window.location.href='keywords.html'">Cancel</button>
          <button class="action-button save-button" onclick="showToast('Keyword saved successfully'); setTimeout(() => window.location.href='keywords.html', 1000);">Save</button>
        </div>
        
        <div class="delete-keyword">
          <button class="delete-button" onclick="showToast('Keyword deleted'); setTimeout(() => window.location.href='keywords.html', 1000);">Delete Keyword</button>
        </div>
      </div>
    </div>
    
    <!-- Tab Bar -->
    <div class="tab-bar">
      <div class="tab-item" data-page="home.html">
        <i class="fas fa-home tab-icon"></i>
        <span>Home</span>
      </div>
      
      <div class="tab-item active" data-page="keywords.html">
        <i class="fas fa-tags tab-icon"></i>
        <span>Keywords</span>
      </div>
      
      <div class="mic-button">
        <i class="fas fa-microphone mic-icon"></i>
      </div>
      
      <div class="tab-item" data-page="notes.html">
        <i class="fas fa-sticky-note tab-icon"></i>
        <span>Notes</span>
      </div>
      
      <div class="tab-item" data-page="profile.html">
        <i class="fas fa-user tab-icon"></i>
        <span>Profile</span>
      </div>
    </div>
  </div>
  
  <script src="js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const colorOptions = document.querySelectorAll('.color-option');
      const iconOptions = document.querySelectorAll('.icon-option');
      const previewIcon = document.getElementById('preview-icon');
      const previewIconSymbol = document.getElementById('preview-icon-symbol');
      const previewTitle = document.getElementById('preview-title');
      const keywordNameInput = document.getElementById('keyword-name');
      
      // Update preview when keyword name changes
      keywordNameInput.addEventListener('input', () => {
        previewTitle.textContent = keywordNameInput.value || 'Keyword';
      });
      
      // Handle color selection
      colorOptions.forEach(color => {
        color.addEventListener('click', () => {
          // Remove active class from all colors
          colorOptions.forEach(c => c.classList.remove('active'));
          
          // Add active class to clicked color
          color.classList.add('active');
          
          // Update preview icon color
          previewIcon.style.backgroundColor = color.dataset.color;
        });
      });
      
      // Handle icon selection
      iconOptions.forEach(icon => {
        icon.addEventListener('click', () => {
          // Remove active class from all icons
          iconOptions.forEach(i => i.classList.remove('active'));
          
          // Add active class to clicked icon
          icon.classList.add('active');
          
          // Update preview icon
          previewIconSymbol.className = `fas ${icon.dataset.icon}`;
        });
      });
    });
  </script>
</body>
</html> 