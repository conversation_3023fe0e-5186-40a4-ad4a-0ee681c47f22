<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Echonote - Notes</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <style>
    .note-card {
      position: relative;
    }
    
    .note-actions {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      gap: 8px;
    }
    
    .note-tag {
      display: inline-block;
      padding: 2px 8px;
      border-radius: 10px;
      font-size: 10px;
      margin-bottom: 8px;
    }
    
    .tag-idea {
      background-color: rgba(255, 59, 48, 0.2);
      color: #FF3B30;
    }
    
    .tag-groceries {
      background-color: rgba(52, 199, 89, 0.2);
      color: #34C759;
    }
    
    .tag-meeting {
      background-color: rgba(175, 82, 222, 0.2);
      color: #AF52DE;
    }
    
    .tag-task {
      background-color: rgba(255, 204, 0, 0.2);
      color: #FFCC00;
    }
    
    .timestamp {
      font-size: 10px;
      color: var(--text-secondary);
      margin-top: 8px;
    }
    
    .favorite-icon {
      color: #ffcc00;
    }
    
    .edit-icon, .delete-icon {
      color: var(--text-secondary);
    }
    
    .tab-selector {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 0 5px;
      position: sticky;
      top: 0;
      z-index: 10;
      background-color: rgba(0, 0, 0, 0.95);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      padding: 15px 5px;
      margin-top: -15px;
      margin-left: -16px;
      margin-right: -16px;
      padding-left: 16px;
      padding-right: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      transition: box-shadow 0.3s ease, background-color 0.3s ease;
      width: calc(100% + 32px); /* Ensure it spans the full width */
      left: 0; /* Anchor to the left edge */
    }
    
    .tab-selector.scrolled {
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);
      background-color: rgba(0, 0, 0, 0.98);
    }
    
    .tabs {
      display: flex;
      gap: 15px;
      overflow-x: auto;
      padding-bottom: 5px;
      flex-grow: 1;
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
    }
    
    .tabs::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
    
    .tab {
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 600;
      white-space: nowrap;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .tab.active {
      color: white;
    }
    
    .tab-all {
      background-color: rgba(90, 200, 250, 0.2);
      color: #5ac8fa;
    }
    
    .tab-all.active {
      background-color: #5ac8fa;
    }
    
    .tab-recent {
      background-color: rgba(255, 149, 0, 0.2);
      color: #FF9500;
    }
    
    .tab-recent.active {
      background-color: #FF9500;
    }
    
    .tab-favorites {
      background-color: rgba(255, 204, 0, 0.2);
      color: #FFCC00;
    }
    
    .tab-favorites.active {
      background-color: #FFCC00;
    }
    
    .filter-button {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background-color: var(--secondary-bg);
      cursor: pointer;
    }
    
    .filter-menu {
      position: fixed;
      top: auto;
      right: 20px;
      background-color: var(--secondary-bg);
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      z-index: 1000;
      width: 180px;
      padding: 10px 0;
      display: none;
    }
    
    .filter-menu.show {
      display: block;
      animation: fadeIn 0.2s ease-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .filter-menu-title {
      font-size: 14px;
      font-weight: 600;
      padding: 5px 15px;
      color: var(--text-secondary);
      margin-bottom: 5px;
    }
    
    .filter-item {
      display: flex;
      align-items: center;
      padding: 10px 15px;
      cursor: pointer;
    }
    
    .filter-item:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }
    
    .filter-icon {
      width: 30px;
      height: 30px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
    }
    
    .filter-icon i {
      color: white;
      font-size: 14px;
    }
    
    .filter-text {
      font-size: 14px;
      color: var(--text-primary);
    }
    
    .icon-idea {
      background-color: #FF3B30;
    }
    
    .icon-groceries {
      background-color: #34C759;
    }
    
    .icon-meeting {
      background-color: #AF52DE;
    }
    
    .icon-task {
      background-color: #FFCC00;
    }
    
    /* View Switcher Styles */
    .view-switcher {
      display: flex;
      background-color: rgba(60, 60, 60, 0.7);
      border-radius: 6px;
      padding: 3px;
      margin: 0 auto;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }
    
    .view-option {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
    }
    
    .view-option.active {
      background-color: rgba(255, 255, 255, 0.15);
    }
    
    .view-option i {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);
      transition: all 0.2s ease;
    }
    
    /* Color highlights for active view options */
    .view-option[data-view="grid"].active i {
      color: #5AC8FA; /* Blue */
    }
    
    .view-option[data-view="list"].active i {
      color: #FF9500; /* Orange */
    }
    
    .view-option[data-view="columns"].active i {
      color: #4CD964; /* Green */
    }
    
    .view-option[data-view="gallery"].active i {
      color: #FF3B30; /* Red */
    }
    
    .view-option:hover:not(.active) {
      background-color: rgba(80, 80, 80, 0.7);
    }
    
    .view-option:active {
      transform: scale(0.95);
    }
    
    .view-tooltip {
      display: none;
    }
    
    .view-transition {
      transition: all 0.3s ease-out;
    }
    
    .view-grid .note-card,
    .view-list .note-card,
    .view-columns .note-card,
    .view-gallery .note-card {
      transition: transform 0.3s ease, opacity 0.3s ease;
    }
    
    .header {
      padding: 16px 20px;
      padding-top: 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .header-title {
      display: none;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    
    .content {
      padding: 0 16px;
      margin-bottom: 80px;
      height: calc(100% - 60px - 80px);
      overflow-y: auto;
      scroll-padding-top: 70px; /* Add padding to account for the sticky header */
    }
    
    /* Add some extra padding to the first element after the sticky header */
    .grid-container {
      padding-top: 10px;
    }
    
    /* Remove the tooltip styles */
    .view-tooltip {
      display: none;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes fadeOut {
      from { opacity: 1; transform: translateY(0); }
      to { opacity: 0; transform: translateY(10px); }
    }
    
    .fade-in {
      animation: fadeIn 0.3s ease-out forwards;
    }
    
    .fade-out {
      animation: fadeOut 0.3s ease-out forwards;
    }
    
    .toast {
      transition: opacity 0.3s ease;
    }
    
    /* View Transitions */
    .view-transition {
      transition: all 0.3s ease-out;
    }
    
    .header-actions i {
      font-size: 18px;
      transition: all 0.2s ease;
    }
    
    .header-actions i:hover {
      transform: scale(1.1);
      opacity: 0.9;
    }
    
    .header-actions i.fa-plus {
      color: #FF9500;
      font-size: 24px;
    }
    
    .header-actions i.fa-plus:active {
      transform: scale(0.95);
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- iPhone 16 Dynamic Island -->
    <div class="dynamic-island"></div>
    
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal signal-icon"></i>
        <i class="fas fa-wifi wifi-icon"></i>
        <i class="fas fa-battery-full battery-icon"></i>
      </div>
    </div>
    
    <div class="header">
      <div class="header-title">
      </div>
      
      <!-- View Switcher -->
      <div class="view-switcher">
        <div class="view-option active" data-view="grid">
          <i class="fas fa-th-large"></i>
        </div>
        <div class="view-option" data-view="list">
          <i class="fas fa-list"></i>
        </div>
        <div class="view-option" data-view="columns">
          <i class="fas fa-columns"></i>
        </div>
        <div class="view-option" data-view="gallery">
          <i class="fas fa-th"></i>
        </div>
      </div>
      
      <div class="header-actions">
        <i class="fas fa-search" onclick="window.location.href='search.html'" style="margin-right: 15px;"></i>
        <i class="fas fa-plus" onclick="window.location.href='edit-note.html'"></i>
      </div>
    </div>
    
    <div class="content">
      <!-- Tab Selector -->
      <div class="tab-selector" id="tabSelector">
        <div class="tabs">
          <div class="tab tab-all active" data-filter="all">All Notes</div>
          <div class="tab tab-recent" data-filter="recent">Recent</div>
          <div class="tab tab-favorites" data-filter="favorites">Favorites</div>
        </div>
        <div class="filter-button" id="filterButton">
          <i class="fas fa-ellipsis-v"></i>
        </div>
      </div>
      
      <!-- Filter Menu (Hidden by default) -->
      <div class="filter-menu" id="filterMenu">
        <div class="filter-menu-title">FILTER BY KEYWORD</div>
        <div class="filter-item" data-filter="idea">
          <div class="filter-icon icon-idea">
            <i class="fas fa-lightbulb"></i>
          </div>
          <div class="filter-text">Idea</div>
        </div>
        <div class="filter-item" data-filter="groceries">
          <div class="filter-icon icon-groceries">
            <i class="fas fa-shopping-basket"></i>
          </div>
          <div class="filter-text">Groceries</div>
        </div>
        <div class="filter-item" data-filter="meeting">
          <div class="filter-icon icon-meeting">
            <i class="fas fa-users"></i>
          </div>
          <div class="filter-text">Meeting</div>
        </div>
        <div class="filter-item" data-filter="task">
          <div class="filter-icon icon-task">
            <i class="fas fa-tasks"></i>
          </div>
          <div class="filter-text">Task</div>
        </div>
      </div>
      
      <!-- Notes Grid -->
      <div class="grid-container">
        <div class="note-card" data-type="idea" data-favorite="true" onclick="window.location.href='note-detail.html'">
          <div class="note-actions">
            <i class="fas fa-star favorite-icon"></i>
            <i class="fas fa-edit edit-icon" onclick="event.stopPropagation(); window.location.href='edit-note.html'"></i>
            <i class="fas fa-trash-alt delete-icon" onclick="event.stopPropagation(); confirm('Are you sure you want to delete this note?')"></i>
          </div>
          <div class="note-tag tag-idea">Idea</div>
          <h3>App Concept</h3>
          <p>Create a mobile app for tracking daily water intake with reminders and achievements...</p>
          <p class="timestamp">Today, 10:23 AM</p>
        </div>
        
        <div class="note-card" data-type="groceries" data-favorite="false" onclick="window.location.href='note-detail.html'">
          <div class="note-actions">
            <i class="far fa-star" onclick="event.stopPropagation(); this.classList.toggle('fas'); this.classList.toggle('far'); this.classList.toggle('favorite-icon'); this.closest('.note-card').dataset.favorite = this.classList.contains('fas') ? 'true' : 'false'"></i>
            <i class="fas fa-edit edit-icon" onclick="event.stopPropagation(); window.location.href='edit-note.html'"></i>
            <i class="fas fa-trash-alt delete-icon" onclick="event.stopPropagation(); confirm('Are you sure you want to delete this note?')"></i>
          </div>
          <div class="note-tag tag-groceries">Groceries</div>
          <h3>Weekly Shopping</h3>
          <p>Milk, eggs, bread, apples, chicken breast, pasta, tomatoes, onions...</p>
          <p class="timestamp">Yesterday, 4:15 PM</p>
        </div>
        
        <div class="note-card" data-type="meeting" data-favorite="true" onclick="window.location.href='note-detail.html'">
          <div class="note-actions">
            <i class="fas fa-star favorite-icon" onclick="event.stopPropagation(); this.classList.toggle('far'); this.classList.toggle('fas'); this.classList.toggle('favorite-icon'); this.closest('.note-card').dataset.favorite = this.classList.contains('fas') ? 'true' : 'false'"></i>
            <i class="fas fa-edit edit-icon" onclick="event.stopPropagation(); window.location.href='edit-note.html'"></i>
            <i class="fas fa-trash-alt delete-icon" onclick="event.stopPropagation(); confirm('Are you sure you want to delete this note?')"></i>
          </div>
          <div class="note-tag tag-meeting">Meeting</div>
          <h3>Marketing Strategy</h3>
          <p>Discuss Q3 marketing strategy with the team, prepare slides for presentation...</p>
          <p class="timestamp">Yesterday, 2:30 PM</p>
        </div>
        
        <div class="note-card" data-type="task" data-favorite="false" onclick="window.location.href='note-detail.html'">
          <div class="note-actions">
            <i class="far fa-star" onclick="event.stopPropagation(); this.classList.toggle('fas'); this.classList.toggle('far'); this.classList.toggle('favorite-icon'); this.closest('.note-card').dataset.favorite = this.classList.contains('fas') ? 'true' : 'false'"></i>
            <i class="fas fa-edit edit-icon" onclick="event.stopPropagation(); window.location.href='edit-note.html'"></i>
            <i class="fas fa-trash-alt delete-icon" onclick="event.stopPropagation(); confirm('Are you sure you want to delete this note?')"></i>
          </div>
          <div class="note-tag tag-task">Task</div>
          <h3>Home Repairs</h3>
          <p>Call the plumber to fix the kitchen sink, schedule appointment for next week...</p>
          <p class="timestamp">Jun 12, 9:45 AM</p>
        </div>
        
        <div class="note-card" data-type="idea" data-favorite="false" onclick="window.location.href='note-detail.html'">
          <div class="note-actions">
            <i class="far fa-star" onclick="event.stopPropagation(); this.classList.toggle('fas'); this.classList.toggle('far'); this.classList.toggle('favorite-icon'); this.closest('.note-card').dataset.favorite = this.classList.contains('fas') ? 'true' : 'false'"></i>
            <i class="fas fa-edit edit-icon" onclick="event.stopPropagation(); window.location.href='edit-note.html'"></i>
            <i class="fas fa-trash-alt delete-icon" onclick="event.stopPropagation(); confirm('Are you sure you want to delete this note?')"></i>
          </div>
          <div class="note-tag tag-idea">Idea</div>
          <h3>Book Concept</h3>
          <p>A sci-fi novel about a world where dreams can be recorded and shared like social media...</p>
          <p class="timestamp">Jun 10, 11:20 PM</p>
        </div>
        
        <div class="note-card" data-type="meeting" data-favorite="false" onclick="window.location.href='note-detail.html'">
          <div class="note-actions">
            <i class="far fa-star" onclick="event.stopPropagation(); this.classList.toggle('fas'); this.classList.toggle('far'); this.classList.toggle('favorite-icon'); this.closest('.note-card').dataset.favorite = this.classList.contains('fas') ? 'true' : 'false'"></i>
            <i class="fas fa-edit edit-icon" onclick="event.stopPropagation(); window.location.href='edit-note.html'"></i>
            <i class="fas fa-trash-alt delete-icon" onclick="event.stopPropagation(); confirm('Are you sure you want to delete this note?')"></i>
          </div>
          <div class="note-tag tag-meeting">Meeting</div>
          <h3>Client Call</h3>
          <p>Discuss project timeline and deliverables with the client, address concerns about budget...</p>
          <p class="timestamp">Jun 9, 3:15 PM</p>
        </div>
      </div>
    </div>
    
    <!-- Tab Bar -->
    <div class="tab-bar">
      <div class="tab-item" data-page="home.html">
        <i class="fas fa-home tab-icon"></i>
        <span>Home</span>
      </div>
      
      <div class="tab-item" data-page="keywords.html">
        <i class="fas fa-tags tab-icon"></i>
        <span>Keywords</span>
      </div>
      
      <div class="tab-item" data-page="recording.html">
        <div class="mic-button">
          <i class="fas fa-microphone mic-icon"></i>
        </div>
      </div>
      
      <div class="tab-item active" data-page="notes.html">
        <i class="fas fa-sticky-note tab-icon"></i>
        <span>Notes</span>
      </div>
      
      <div class="tab-item" data-page="profile.html">
        <i class="fas fa-user tab-icon"></i>
        <span>Profile</span>
      </div>
    </div>
    
    <!-- Recording Modal (Hidden by default) -->
    <div class="recording-modal" style="display: none;">
      <div class="waveform">
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
      </div>
      
      <div class="recording-status">Listening for keyword...</div>
      <div class="recording-keyword">Say "Idea", "Groceries", etc.</div>
      
      <div class="recording-actions">
        <div class="recording-button pause-button">
          <i class="fas fa-pause"></i>
        </div>
        
        <div class="recording-button stop-button">
          <i class="fas fa-stop"></i>
        </div>
      </div>
    </div>
    
    <!-- 底部指示条 - iPhone 16特有 -->
    <div class="bottom-indicator"></div>
  </div>
  
  <script src="js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // Filter button functionality
      const filterButton = document.getElementById('filterButton');
      const filterMenu = document.getElementById('filterMenu');
      const tabSelector = document.getElementById('tabSelector');
      const content = document.querySelector('.content');
      
      // Add scroll event listener to handle sticky tab selector effects
      content.addEventListener('scroll', () => {
        if (content.scrollTop > 10) {
          tabSelector.classList.add('scrolled');
        } else {
          tabSelector.classList.remove('scrolled');
        }
        
        // Adjust filter menu position based on scroll
        if (filterMenu.classList.contains('show')) {
          filterMenu.style.top = (tabSelector.offsetHeight + tabSelector.offsetTop) + 'px';
        }
      });
      
      filterButton.addEventListener('click', (event) => {
        event.stopPropagation();
        filterMenu.classList.toggle('show');
        if (filterMenu.classList.contains('show')) {
          // Position the filter menu below the tab selector
          const tabSelectorRect = tabSelector.getBoundingClientRect();
          filterMenu.style.top = (tabSelectorRect.bottom + 5) + 'px';
        }
      });
      
      // Close filter menu when clicking outside
      document.addEventListener('click', (event) => {
        if (!filterButton.contains(event.target) && !filterMenu.contains(event.target)) {
          filterMenu.classList.remove('show');
        }
      });
      
      // Tab filtering
      const tabs = document.querySelectorAll('.tab');
      const filterItems = document.querySelectorAll('.filter-item');
      const noteCards = document.querySelectorAll('.note-card');
      
      // Function to filter notes
      function filterNotes(filterType) {
        noteCards.forEach(card => {
          if (filterType === 'all') {
            card.style.display = 'block';
          } else if (filterType === 'favorites') {
            card.style.display = card.dataset.favorite === 'true' ? 'block' : 'none';
          } else if (filterType === 'recent') {
            // For demo purposes, we'll show all notes for "Recent"
            card.style.display = 'block';
          } else {
            // Filter by keyword type
            card.style.display = card.dataset.type === filterType ? 'block' : 'none';
          }
        });
      }
      
      // Tab click event
      tabs.forEach(tab => {
        tab.addEventListener('click', () => {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove('active'));
          
          // Add active class to clicked tab
          tab.classList.add('active');
          
          // Filter notes based on tab
          filterNotes(tab.dataset.filter);
          
          // Close filter menu
          filterMenu.classList.remove('show');
        });
      });
      
      // Filter item click event
      filterItems.forEach(item => {
        item.addEventListener('click', () => {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove('active'));
          
          // Filter notes based on selected keyword
          filterNotes(item.dataset.filter);
          
          // Close filter menu
          filterMenu.classList.remove('show');
          
          // Show toast message
          showToast(`Filtered by: ${item.querySelector('.filter-text').textContent}`);
        });
      });
      
      // View switcher functionality
      const viewOptions = document.querySelectorAll('.view-option');
      const gridContainer = document.querySelector('.grid-container');
      
      viewOptions.forEach(option => {
        option.addEventListener('click', () => {
          // Remove active class from all options
          viewOptions.forEach(opt => opt.classList.remove('active'));
          
          // Add active class to clicked option
          option.classList.add('active');
          
          // Apply the selected view
          const viewType = option.dataset.view;
          applyView(viewType);
        });
      });
      
      function applyView(viewType) {
        // Add transition class to container
        gridContainer.classList.add('view-transition');
        
        // Ensure the tab selector stays fixed width regardless of view mode
        document.getElementById('tabSelector').style.width = 'calc(100% + 32px)';
        document.getElementById('tabSelector').style.left = '0';
        
        // Fade out current notes
        const noteCards = document.querySelectorAll('.note-card');
        noteCards.forEach(card => {
          card.style.opacity = '0';
          card.style.transform = 'scale(0.95)';
        });
        
        // Update the view switcher with appropriate colors
        document.querySelectorAll('.view-option').forEach(option => {
          if (option.dataset.view === viewType) {
            option.classList.add('active');
            
            // Add color highlight based on view type
            switch(viewType) {
              case 'grid':
                option.querySelector('i').style.textShadow = '0 0 5px rgba(90, 200, 250, 0.7)';
                break;
              case 'list':
                option.querySelector('i').style.textShadow = '0 0 5px rgba(255, 149, 0, 0.7)';
                break;
              case 'columns':
                option.querySelector('i').style.textShadow = '0 0 5px rgba(76, 217, 100, 0.7)';
                break;
              case 'gallery':
                option.querySelector('i').style.textShadow = '0 0 5px rgba(255, 59, 48, 0.7)';
                break;
            }
          } else {
            option.classList.remove('active');
            option.querySelector('i').style.textShadow = 'none';
          }
        });
        
        // Wait for fade out to complete
        setTimeout(() => {
          // Remove all view-related classes
          gridContainer.classList.remove('view-grid', 'view-list', 'view-columns', 'view-gallery');
          
          // Add the appropriate class based on the view type
          gridContainer.classList.add(`view-${viewType}`);
          
          // Apply specific styling based on view type
          switch(viewType) {
            case 'grid':
              gridContainer.style.display = 'grid';
              gridContainer.style.gridTemplateColumns = 'repeat(2, 1fr)';
              gridContainer.style.gridAutoFlow = 'row';
              gridContainer.style.overflowX = 'hidden'; // Prevent horizontal scrolling
              break;
            case 'list':
              gridContainer.style.display = 'flex';
              gridContainer.style.flexDirection = 'column';
              gridContainer.style.overflowX = 'hidden'; // Prevent horizontal scrolling
              break;
            case 'columns':
              gridContainer.style.display = 'grid';
              gridContainer.style.gridTemplateColumns = 'repeat(2, 1fr)';
              gridContainer.style.gridAutoFlow = 'column';
              gridContainer.style.overflowX = 'auto'; // Allow horizontal scrolling for columns
              gridContainer.style.width = '100%'; // Ensure it doesn't expand beyond container
              gridContainer.style.paddingBottom = '20px'; // Add space for scrollbar
              break;
            case 'gallery':
              gridContainer.style.display = 'grid';
              gridContainer.style.gridTemplateColumns = 'repeat(3, 1fr)';
              gridContainer.style.gridAutoFlow = 'row';
              gridContainer.style.overflowX = 'hidden'; // Prevent horizontal scrolling
              break;
          }
          
          // Adjust note card styles based on view
          noteCards.forEach(card => {
            if (viewType === 'list') {
              card.style.width = '100%';
              card.style.margin = '0 0 10px 0';
            } else {
              card.style.width = '';
              card.style.margin = '';
            }
            
            // Fade in notes with slight delay
            setTimeout(() => {
              card.style.opacity = '1';
              card.style.transform = 'scale(1)';
            }, 50);
          });
        }, 200);
        
        // Save the view preference to localStorage
        localStorage.setItem('preferredView', viewType);
      }
      
      // Initialize with saved view or default to grid
      const savedView = localStorage.getItem('preferredView') || 'grid';
      
      // Set the active class on the saved view option
      document.querySelector(`.view-option[data-view="${savedView}"]`).classList.add('active');
      document.querySelectorAll('.view-option').forEach(opt => {
        if (opt.dataset.view !== savedView) {
          opt.classList.remove('active');
        }
      });
      
      // Apply the saved view
      applyView(savedView);
      
      // Toast notification function
      function showToast(message) {
        const toast = document.createElement('div');
        toast.className = 'toast fade-in';
        toast.textContent = message;
        toast.style.position = 'fixed';
        toast.style.bottom = '100px';
        toast.style.left = '50%';
        toast.style.transform = 'translateX(-50%)';
        toast.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        toast.style.color = 'white';
        toast.style.padding = '12px 20px';
        toast.style.borderRadius = '8px';
        toast.style.zIndex = '1000';
        document.body.appendChild(toast);
        
        setTimeout(() => {
          toast.style.opacity = '0';
          setTimeout(() => {
            document.body.removeChild(toast);
          }, 300);
        }, 2000);
      }
    });
  </script>
</body>
</html> 