<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Echonote - Home</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    .note-card {
      position: relative;
    }
    
    .note-actions {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      gap: 8px;
    }
    
    .note-tag {
      display: inline-block;
      padding: 2px 8px;
      border-radius: 10px;
      font-size: 10px;
      margin-bottom: 8px;
    }
    
    .tag-idea {
      background-color: rgba(255, 59, 48, 0.2);
      color: #FF3B30;
    }
    
    .tag-groceries {
      background-color: rgba(52, 199, 89, 0.2);
      color: #34C759;
    }
    
    .tag-meeting {
      background-color: rgba(175, 82, 222, 0.2);
      color: #AF52DE;
    }
    
    .tag-task {
      background-color: rgba(255, 204, 0, 0.2);
      color: #FFCC00;
    }
    
    .timestamp {
      font-size: 10px;
      color: var(--text-secondary);
      margin-top: 8px;
    }
    
    .favorite-icon {
      color: #ffcc00;
    }
    
    .edit-icon, .delete-icon {
      color: var(--text-secondary);
    }

    .chart-options {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin: 0;
      position: relative;
      left: 0;
      transform: none;
      height: 100%;
      align-items: center;
      min-width: 150px; /* 确保即使隐藏也会保留空间 */
      transition: visibility 0.3s, opacity 0.3s; /* 添加过渡效果 */
    }

    .chart-option {
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background-color: var(--secondary-bg);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .chart-option.active {
      background-color: var(--accent-green);
      color: var(--primary-bg);
    }

    .chart-option i {
      font-size: 14px;
    }

    .chart-tooltip {
      position: absolute;
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 5px 10px;
      border-radius: 5px;
      font-size: 12px;
      bottom: -30px;
      left: 50%;
      transform: translateX(-50%);
      white-space: nowrap;
      opacity: 0;
      transition: opacity 0.3s;
      pointer-events: none;
      z-index: 100;
    }

    .chart-option:hover .chart-tooltip {
      opacity: 1;
    }

    .header {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      padding-top: 25px;
    }
    
    /* 添加标题栏图标样式，确保位置固定 */
    .header-actions {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      min-width: 70px; /* 确保图标区域有固定宽度 */
    }
    
    .center-mic-container {
      position: relative;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      padding: 0; /* 移除内边距 */
      height: auto;
      min-height: auto;
      isolation: isolate;
      overflow: visible;
    }
    
    .center-mic-button {
      width: 200px; /* 增加尺寸 */
      height: 200px; /* 增加尺寸 */
      background: linear-gradient(145deg, #4cd964, #34c759);
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 10px 30px rgba(76, 217, 100, 0.5);
      cursor: pointer;
      position: relative;
      z-index: 2;
      /* 添加呼吸动画效果 */
      animation: breathing 4s ease-in-out infinite;
    }
    
    /* 添加呼吸动画 */
    @keyframes breathing {
      0%, 100% {
        transform: scale(1);
        box-shadow: 0 10px 30px rgba(76, 217, 100, 0.5);
      }
      50% {
        transform: scale(1.05);
        box-shadow: 0 15px 40px rgba(76, 217, 100, 0.7);
      }
    }
    
    /* 移除bounce动画 */
    @keyframes bounce {
      0%, 100% {
        transform: none;
      }
      50% {
        transform: none;
      }
    }
    
    /* 移除pulse动画 */
    @keyframes pulse {
      0%, 70%, 100% {
        box-shadow: 0 10px 30px rgba(76, 217, 100, 0.5);
      }
    }
    
    /* 隐藏所有ripple元素 */
    .ripple {
      display: none;
    }
    
    .center-mic-icon {
      font-size: 100px; /* 增加图标大小 */
      color: #000000;
      /* 移除动画 */
      animation: none;
      text-shadow: none;
    }
    
    /* 移除colorShift动画 */
    @keyframes colorShift {
      0%, 25%, 50%, 75%, 100% {
        text-shadow: none;
      }
    }
    
    .mic-label {
      text-align: center;
      font-size: 18px;
      color: var(--text-primary);
      margin-top: 20px;
      font-weight: 500;
      /* 移除动画 */
      animation: none;
      opacity: 1;
    }
    
    /* 移除fadeInOut动画 */
    @keyframes fadeInOut {
      0%, 50%, 100% {
        opacity: 1;
      }
    }
    
    .tab-bar {
      justify-content: space-evenly;
    }

    .stats-card {
      background-color: var(--secondary-bg);
      border-radius: 16px;
      padding: 16px;
      margin-bottom: 20px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .stats-card h2 {
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 18px;
    }
    
    .chart-container {
      width: 100%;
      height: 220px;
      position: relative;
      margin: 0 auto;
    }
    
    /* 为右侧图例提供足够的空间 */
    .chart-container.with-right-legend {
      height: 220px;
      padding-right: 10px;
    }
    
    /* 优化图表在小屏幕上的显示 */
    @media (max-width: 375px) {
      .chart-container.with-right-legend {
        height: 240px;
      }
    }

    /* 完全分离内容区域 */
    .content {
      display: flex;
      flex-direction: column;
      height: calc(100vh - 150px); /* 减去头部和底部的高度 */
      overflow: hidden; /* 防止内容溢出 */
      position: relative; /* 添加相对定位 */
    }
    
    .stats-section {
      position: relative;
      z-index: 1;
      flex: 0 0 auto; /* 不允许伸缩 */
      /* 创建独立的渲染层 */
      transform: translate3d(0, 0, 0);
      backface-visibility: hidden;
      perspective: 1000px;
      /* 固定高度 */
      height: 300px;
      overflow: hidden;
      margin-bottom: 20px;
    }
    
    .mic-section {
      position: absolute;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      bottom: 100px; /* 调整位置，使其更靠近Tab bar */
      left: 0;
      height: calc(100% - 400px); /* 占据从Notes Overview到Tab bar之间的大部分空间 */
      min-height: 300px; /* 确保最小高度 */
    }
    
    /* 固定图表容器尺寸 */
    .chart-container {
      width: 100%;
      height: 220px;
      position: relative;
      margin: 0 auto;
      /* 创建独立的渲染层 */
      transform: translate3d(0, 0, 0);
      backface-visibility: hidden;
      perspective: 1000px;
    }

    .chart-placeholder {
      width: 150px; /* 与chart-options的min-width相同 */
      height: 30px;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- iPhone 16 Dynamic Island -->
    <div class="dynamic-island"></div>
    
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal signal-icon"></i>
        <i class="fas fa-wifi wifi-icon"></i>
        <i class="fas fa-battery-full battery-icon"></i>
      </div>
    </div>
    
    <div class="header">
      <div class="chart-options">
        <div class="chart-option active" data-chart-type="doughnut">
          <i class="fas fa-chart-pie"></i>
          <div class="chart-tooltip">Pie Chart</div>
        </div>
        <div class="chart-option" data-chart-type="bar">
          <i class="fas fa-chart-bar"></i>
          <div class="chart-tooltip">Bar Chart</div>
        </div>
        <div class="chart-option" data-chart-type="line">
          <i class="fas fa-chart-line"></i>
          <div class="chart-tooltip">Line Chart</div>
        </div>
        <div class="chart-option" data-chart-type="radar">
          <i class="fas fa-spider"></i>
          <div class="chart-tooltip">Radar Chart</div>
        </div>
        <!-- 添加一个空的占位元素，确保即使其他元素隐藏，布局也不会变化 -->
        <div class="chart-placeholder" style="display: none;"></div>
      </div>
      <div class="header-actions">
        <i class="fas fa-search" onclick="window.location.href='search.html'" style="margin-right: 15px;"></i>
        <i class="fas fa-bell"></i>
      </div>
    </div>
    
    <div class="content">
      <!-- Overview Cards -->
      <div class="stats-section">
        <div class="stats-card">
          <h2>Notes Overview</h2>
          <p>Total Notes: <strong>24</strong></p>
          <div class="chart-container">
            <canvas id="notesChart"></canvas>
          </div>
        </div>
      </div>
      
      <!-- Center Microphone Button -->
      <div class="mic-section">
        <div class="center-mic-container">
          <!-- 移除所有ripple元素 -->
          <div class="center-mic-button">
            <i class="fas fa-microphone center-mic-icon"></i>
          </div>
          <div class="mic-label">Tap to record a new note</div>
        </div>
      </div>
    </div>
    
    <!-- Tab Bar -->
    <div class="tab-bar">
      <div class="tab-item active" data-page="home.html">
        <i class="fas fa-home tab-icon"></i>
        <span>Home</span>
      </div>
      
      <div class="tab-item" data-page="keywords.html">
        <i class="fas fa-tags tab-icon"></i>
        <span>Keywords</span>
      </div>
      
      <div class="tab-item" data-page="notes.html">
        <i class="fas fa-sticky-note tab-icon"></i>
        <span>Notes</span>
      </div>
      
      <div class="tab-item" data-page="profile.html">
        <i class="fas fa-user tab-icon"></i>
        <span>Profile</span>
      </div>
    </div>
    
    <!-- 底部指示条 - iPhone 16特有 -->
    <div class="bottom-indicator"></div>
    
    <!-- Recording Modal (Hidden by default) -->
    <div class="recording-modal" style="display: none;">
      <div class="waveform">
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
        <div class="wave-bar"></div>
      </div>
      
      <div class="recording-status">Listening for keyword...</div>
      <div class="recording-keyword">Say "Idea", "Groceries", etc.</div>
      
      <div class="recording-actions">
        <div class="recording-button pause-button">
          <i class="fas fa-pause"></i>
        </div>
        
        <div class="recording-button stop-button">
          <i class="fas fa-stop"></i>
        </div>
      </div>
    </div>
  </div>
  
  <script src="js/main.js"></script>
  <script>
    // Initialize charts
    document.addEventListener('DOMContentLoaded', () => {
      // Notes data by keyword
      const keywordsData = {
        labels: ['Ideas', 'Groceries', 'Meetings', 'Tasks'],
        values: [8, 5, 6, 5],
        colors: [
          '#FF3B30', // Red for Ideas
          '#34C759', // Green for Groceries
          '#AF52DE', // Purple for Meetings
          '#FFCC00'  // Yellow for Tasks
        ]
      };
      
      // Chart instance reference
      let currentChart;
      
      // 检查麦克风动画设置
      function checkMicAnimationSettings() {
        const micAnimationsEnabled = localStorage.getItem('micAnimationsEnabled');
        const micButton = document.querySelector('.center-mic-button');
        const ripples = document.querySelectorAll('.ripple');
        const micIcon = document.querySelector('.center-mic-icon');
        
        // 无论设置如何，都确保呼吸动画效果启用
        if (micButton) {
          micButton.style.animation = 'breathing 4s ease-in-out infinite';
        }
        
        // 隐藏波纹效果
        ripples.forEach(ripple => {
          ripple.style.display = 'none';
        });
        
        // 移除图标的动画
        if (micIcon) {
          micIcon.style.animation = 'none';
        }
      }
      
      // 检查是否显示Notes Overview
      function checkNotesOverviewVisibility() {
        // 如果localStorage中没有值，设置默认值为true
        if (localStorage.getItem('showNotesOverview') === null) {
          localStorage.setItem('showNotesOverview', 'true');
        }
        
        const showNotesOverview = localStorage.getItem('showNotesOverview');
        const statsSection = document.querySelector('.stats-section');
        const micSection = document.querySelector('.mic-section');
        const chartOptions = document.querySelector('.chart-options');
        const chartPlaceholder = document.querySelector('.chart-placeholder');
        const header = document.querySelector('.header');
        
        // 如果设置为false，则隐藏Notes Overview并调整麦克风部分
        if (showNotesOverview === 'false' && statsSection) {
          statsSection.style.display = 'none';
          
          // 隐藏图表选项，但保持header布局
          if (chartOptions) {
            chartOptions.style.visibility = 'hidden'; // 使用visibility而不是display
            chartOptions.style.opacity = '0';
          }
          
          // 显示占位元素
          if (chartPlaceholder) {
            chartPlaceholder.style.display = 'block';
          }
          
          // 确保header保持两端对齐
          if (header) {
            header.style.justifyContent = 'space-between';
          }
        } else if (statsSection) {
          statsSection.style.display = 'block';
          
          // 显示图表选项
          if (chartOptions) {
            chartOptions.style.visibility = 'visible';
            chartOptions.style.opacity = '1';
          }
          
          // 隐藏占位元素
          if (chartPlaceholder) {
            chartPlaceholder.style.display = 'none';
          }
        }
        
        // 无论是否显示Notes Overview，都确保麦克风部分位于tab bar和Notes Overview之间的中间
        if (micSection) {
          micSection.style.position = 'absolute';
          micSection.style.width = '100%';
          micSection.style.bottom = '100px'; // 距离底部tab bar约100px
          micSection.style.left = '0';
          micSection.style.transform = 'none';
          micSection.style.justifyContent = 'center';
          micSection.style.zIndex = '2';
        }
      }
      
      // 页面加载时检查设置
      checkMicAnimationSettings();
      checkNotesOverviewVisibility();
      
      // 监听storage变化，实时更新动画状态和Notes Overview显示状态
      window.addEventListener('storage', function(e) {
        if (e.key === 'micAnimationsEnabled') {
          checkMicAnimationSettings();
        } else if (e.key === 'showNotesOverview') {
          checkNotesOverviewVisibility();
        }
      });
      
      // Function to create or update chart
      function updateChart(type) {
        const notesCtx = document.getElementById('notesChart').getContext('2d');
        const chartContainer = document.querySelector('.chart-container');
        
        // 根据图表类型添加或移除类名
        if (type === 'doughnut') {
          chartContainer.classList.add('with-right-legend');
        } else {
          chartContainer.classList.remove('with-right-legend');
        }
        
        // Destroy existing chart if it exists
        if (currentChart) {
          currentChart.destroy();
        }
        
        // Chart configuration based on type
        let chartConfig = {
          type: type,
          data: {
            labels: keywordsData.labels,
            datasets: [{
              data: keywordsData.values,
              backgroundColor: keywordsData.colors,
              borderWidth: 0
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            // 添加动画配置，使其更平滑且不受外部影响
            animation: {
              duration: 800,
              easing: 'easeOutQuart'
            },
            // 禁用悬停时的动画，减少重绘
            hover: {
              animationDuration: 0
            },
            plugins: {
              legend: {
                position: type === 'doughnut' ? 'right' : 'bottom',
                align: 'start',
                labels: {
                  color: '#ffffff',
                  boxWidth: 15,
                  padding: 10,
                  font: {
                    size: 12,
                    weight: 'normal'
                  },
                  generateLabels: function(chart) {
                    // 只为环形图和饼图自定义图例标签
                    if (type === 'doughnut' || type === 'pie') {
                      const data = chart.data;
                      return data.labels.map((label, i) => {
                        const value = data.datasets[0].data[i];
                        const backgroundColor = data.datasets[0].backgroundColor[i];
                        
                        return {
                          text: `${label}: ${value}`,
                          fillStyle: backgroundColor,
                          strokeStyle: backgroundColor,
                          lineWidth: 0,
                          hidden: false,
                          index: i
                        };
                      });
                    } else {
                      // 对其他图表类型使用默认图例
                      return Chart.defaults.plugins.legend.labels.generateLabels(chart);
                    }
                  }
                },
                title: {
                  display: type === 'doughnut',
                  text: 'Keywords',
                  color: '#ffffff',
                  font: {
                    size: 14,
                    weight: 'bold'
                  }
                }
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    const label = context.label || '';
                    const value = context.raw || 0;
                    return `${label}: ${value}`;
                  }
                }
              }
            }
          }
        };
        
        // Specific options based on chart type
        if (type === 'doughnut') {
          chartConfig.options.cutout = '70%';
          // 为右侧图例提供更多空间
          chartConfig.options.layout = {
            padding: {
              left: 0,
              right: 20,
              top: 0,
              bottom: 0
            }
          };
          // 调整图表位置，向左偏移
          chartConfig.options.plugins.legend.position = 'right';
          chartConfig.options.plugins.legend.align = 'start';
        } 
        else if (type === 'bar') {
          chartConfig.data.datasets[0].label = 'Number of Notes';
          chartConfig.options.scales = {
            y: {
              beginAtZero: true,
              ticks: { color: '#a0a0a0' },
              grid: { color: 'rgba(255, 255, 255, 0.1)' }
            },
            x: {
              ticks: { color: '#a0a0a0' },
              grid: { display: false }
            }
          };
        }
        else if (type === 'line') {
          chartConfig.data.datasets[0].label = 'Number of Notes';
          chartConfig.data.datasets[0].tension = 0.3;
          chartConfig.data.datasets[0].fill = true;
          
          // Use app's accent colors for line chart
          chartConfig.data.datasets[0].backgroundColor = 'rgba(52, 199, 89, 0.2)'; // Green with transparency
          chartConfig.data.datasets[0].borderColor = '#34C759'; // Solid green for line
          chartConfig.data.datasets[0].pointBackgroundColor = '#34C759';
          chartConfig.data.datasets[0].pointBorderColor = '#ffffff';
          
          chartConfig.options.scales = {
            y: {
              beginAtZero: true,
              ticks: { color: '#a0a0a0' },
              grid: { color: 'rgba(255, 255, 255, 0.1)' }
            },
            x: {
              ticks: { color: '#a0a0a0' },
              grid: { display: false }
            }
          };
        }
        else if (type === 'radar') {
          chartConfig.data.datasets[0].label = 'Number of Notes';
          
          // Use app's accent colors for radar chart
          chartConfig.data.datasets[0].backgroundColor = 'rgba(175, 82, 222, 0.3)'; // Purple with transparency
          chartConfig.data.datasets[0].borderColor = '#AF52DE'; // Solid purple for outline
          chartConfig.data.datasets[0].pointBackgroundColor = '#AF52DE';
          chartConfig.data.datasets[0].pointBorderColor = '#ffffff';
          
          chartConfig.options.scales = {
            r: {
              beginAtZero: true,
              ticks: { color: '#a0a0a0', backdropColor: 'transparent' },
              grid: { color: 'rgba(255, 255, 255, 0.1)' },
              pointLabels: { color: '#ffffff' }
            }
          };
        }
        
        // Create new chart
        currentChart = new Chart(notesCtx, chartConfig);
      }
      
      // Initialize with doughnut chart
      updateChart('doughnut');
      
      // Chart option buttons
      const chartOptions = document.querySelectorAll('.chart-option');
      chartOptions.forEach(option => {
        option.addEventListener('click', function() {
          // Remove active class from all options
          chartOptions.forEach(opt => opt.classList.remove('active'));
          
          // Add active class to clicked option
          this.classList.add('active');
          
          // Update chart based on selected type
          const chartType = this.dataset.chartType;
          updateChart(chartType);
        });
      });
      
      // Center microphone button
      const recordingModal = document.querySelector('.recording-modal');
      
      if (micButton && recordingModal) {
        micButton.addEventListener('click', () => {
          recordingModal.style.display = 'flex';
          recordingModal.classList.add('fade-in');
          
          // Simulate recording animation
          animateWaveform();
          
          // Simulate keyword detection after 3 seconds
          setTimeout(() => {
            const recordingStatus = document.querySelector('.recording-status');
            const recordingKeyword = document.querySelector('.recording-keyword');
            
            if (recordingStatus && recordingKeyword) {
              recordingStatus.textContent = 'Keyword detected: "Idea"';
              recordingKeyword.textContent = 'Recording note...';
            }
            
            // Simulate note completion after another 5 seconds
            setTimeout(() => {
              if (recordingModal) {
                recordingModal.style.display = 'none';
                
                // Show success message
                showToast('Note saved successfully!');
                
                // Redirect to notes page after recording (if not already there)
                if (!window.location.href.includes('notes.html')) {
                  setTimeout(() => {
                    window.location.href = 'notes.html';
                  }, 1000);
                }
              }
            }, 5000);
          }, 3000);
        });
      }
      
      // Add hover effect to mic button
      const micButton = document.querySelector('.center-mic-button');
      if (micButton) {
        micButton.addEventListener('mouseenter', () => {
          // 保存当前动画
          const currentAnimation = micButton.style.animation;
          // 添加悬停效果
          micButton.style.transform = 'scale(1.05)';
        });
        
        micButton.addEventListener('mouseleave', () => {
          // 移除悬停效果
          micButton.style.transform = '';
        });
      }
    });
    
    // Helper function for toast messages (copied from main.js)
    function showToast(message) {
      // Create toast element if it doesn't exist
      let toast = document.querySelector('.toast-message');
      
      if (!toast) {
        toast = document.createElement('div');
        toast.className = 'toast-message';
        document.body.appendChild(toast);
        
        // Add toast styles
        toast.style.position = 'fixed';
        toast.style.bottom = '80px';
        toast.style.left = '50%';
        toast.style.transform = 'translateX(-50%)';
        toast.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        toast.style.color = 'white';
        toast.style.padding = '10px 20px';
        toast.style.borderRadius = '20px';
        toast.style.fontSize = '14px';
        toast.style.zIndex = '9999';
        toast.style.opacity = '0';
        toast.style.transition = 'opacity 0.3s ease-in-out';
      }
      
      // Set message and show toast
      toast.textContent = message;
      toast.style.opacity = '1';
      
      // Hide toast after 2 seconds
      setTimeout(() => {
        toast.style.opacity = '0';
      }, 2000);
    }
    
    // Helper function to animate waveform
    function animateWaveform() {
      const waveBars = document.querySelectorAll('.wave-bar');
      
      if (waveBars.length > 0) {
        setInterval(() => {
          waveBars.forEach(bar => {
            const randomHeight = Math.floor(Math.random() * 50) + 10;
            bar.style.height = `${randomHeight}px`;
          });
        }, 100);
      }
    }
  </script>
</body>
</html> 