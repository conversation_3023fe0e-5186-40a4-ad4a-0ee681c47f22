# EchoNote - iOS Voice Note Taking App

## Overview
EchoNote is an iOS app designed to capture and organize spoken thoughts using speech recognition technology. It allows users to record voice notes on-the-go, automatically transcribes them, and intelligently identifies keywords to categorize and organize the content. The app addresses the need for efficient note-taking when typing is inconvenient or impossible, such as while driving, exercising, or when inspiration strikes unexpectedly.

## Target Platforms
- iOS 16.0+
- iPhone (primary)
- iPad (secondary)

## Key Features

### Speech Recognition and Note Capture
- Real-time speech-to-text conversion
- Automatic detection of user-defined keywords in speech
- Waveform visualization during recording
- Option to edit transcribed text after recording
- Support for multiple recording modes (continuous, tap-to-record)

### Note Management
- List view of all notes with sorting options (date, keywords, favorites)
- Detailed note view with playback controls
- Ability to favorite, edit, and delete notes
- Automatic categorization based on detected keywords
- Manual tagging and categorization options
- Search functionality across all notes

### Keyword Management
- User-defined keyword library
- Ability to add, edit, and delete keywords
- Keyword analytics showing usage frequency
- Automatic suggestions for potential keywords based on usage patterns

### User Profile and Settings
- Customizable recording settings (sensitivity, pause detection)
- Theme options (light/dark mode)
- Data backup and sync options
- Privacy settings for speech recognition data

### Home Dashboard
- Summary statistics of notes and keyword usage
- Quick access to recent notes
- Suggested actions based on usage patterns
- Recording status and history

## Technical Requirements

### Architecture
- MVVM architecture pattern
- SwiftUI for UI components
- SwiftData for local data persistence
- AVFoundation for audio recording
- Speech framework for speech recognition

### Data Models
- Note: id, title, content, audio_url, created_date, modified_date, is_favorite, duration
- Keyword: id, text, created_date, usage_count
- UserPreferences: recording_mode, theme, privacy_settings

### Services
- SpeechRecognitionService: Handles real-time speech-to-text conversion
- AudioRecordingService: Manages audio recording and playback
- DataManager: CRUD operations for notes and keywords
- KeywordDetectionService: Analyzes text for keyword matches

### UI Components
- MainTabView: Main navigation with tabs for Home, Notes, Keywords, Profile
- RecordingView: Audio recording interface with waveform visualization
- NotesView: List and grid views of notes with filtering options
- KeywordsView: Management interface for keywords
- NoteDetailView: Detailed view of individual notes with playback controls
- ProfileView: User settings and preferences

## Non-Functional Requirements
- Performance: Speech recognition should work with minimal latency
- Usability: Intuitive interface following iOS design guidelines
- Privacy: All speech processing should happen on-device when possible

## Future Enhancements (v2.0)
- Cloud sync across devices
- Sharing notes with other users
- Advanced analytics and insights
- Integration with calendar and reminders
- Custom categories beyond keywords
- AI-powered note summarization

## Target Audience
- Busy professionals who need to capture ideas and tasks throughout the day
- Creative individuals who want to record inspiration when it strikes
- Students who need to take quick notes during lectures or study sessions
- Parents/caregivers who need hands-free note-taking while managing other tasks

## Core Features

### 1. Voice Note Recording with Keyword Detection
- Users can initiate recording by tapping the central microphone button
- App listens for predefined trigger keywords (e.g., "Idea", "Task", "Groceries", "Meeting")
- When a keyword is detected, the app begins recording the subsequent speech
- Recording continues until the user manually stops or silence is detected
- Notes are automatically categorized based on the detected keyword
- Visual feedback shows the detected keyword and recording status
- Audio waveform visualization provides feedback during recording
- Siri shortcuts integration to record while screen is locked

### 2. Note Management
- View all notes in a grid or list layout with filtering options
- Notes display the detected keyword, timestamp, and preview of content
- Mark notes as favorites for quick access
- Edit note content and associated keywords
- Delete unwanted notes
- Search notes by content or keywords

### 3. Keyword Management
- Create, edit, and delete trigger keywords
- View statistics on keyword usage
- Set custom colors for different keywords
- Enable/disable specific keywords

### 4. Home Dashboard

- Show usage statistics (notes created per day/week, most used keywords)
- Striking Microphone icon in the centre for easy access 


### 5. User Profile and Settings
- Customize app appearance (light/dark mode)
- Adjust voice recognition sensitivity
- Set default recording timeout
- Configure auto-stop settings (silence duration)
- Manage notification preferences

## Technical Requirements

### iOS App Development
- Develop using Swift and SwiftUI for modern iOS interface
- Target iOS 16.0 and above
- Support for iPhone devices (iPad support in future versions)
- Implement MVVM architecture for clean separation of concerns

### Data Storage
- Use SwiftData for local data persistence
- Store notes with the following attributes:
  - Unique identifier
  - Content (text)
  - Associated keyword(s)
  - Creation timestamp
  - Last modified timestamp
  - Favorite status
  - Audio recording reference (optional for future)

### Voice Recognition
- Implement real-time speech recognition using Apple's Speech framework
- Process audio input to detect predefined keywords
- Convert spoken words to text with high accuracy
- Handle background noise and different accents
- Provide visual feedback during keyword detection and recording

### User Interface
- Follow iOS Human Interface Guidelines
- Support dark mode and light mode
- Implement responsive layouts for different iPhone sizes
- Create smooth animations for state transitions

## User Experience

### Navigation Structure
- Tab bar navigation with four main sections:
  1. Home - Dashboard with statistics and recent notes
  2. Keywords - Management of trigger keywords
  3. Notes - Browsing and searching all recorded notes
  4. Profile - User settings and preferences
- Central microphone button for immediate access to recording from any screen

# UI Requirements
- **Home Screen:**
  - Notes Overview shows the total notes, notes are taken this week and today, visually presented. (e.g., "Idea", "Groceries").
  - Microphone button in the center of the bottom tab bar to start notes taking, click it will pop us the recording page.
- **Keywords Screen:**
  - UI to manage trigger keywords(Add/Edit/Remove).
- **Notes Screen:**
  - List of entries for the selected keywords in grid, three tags are "All Notes", "Recent" and "Favorites", each note with timestamp and edit/delete options.
- **Profile Screen:**
  - Manage app preferences (e.g., Dark Mode, Haptic Feedback, Microphone Animation on/off, Show Notes Overview on/off, Language).
  - Recording Options: Auto Stop (Automatically stop recording after silence for certain seconds - use drag bar), Maximum Recording Duration ( use drag bar), Add to Siri Shortcut button.
  - Subscription & Account ( Edit profile with uploading your own photo, View subscription status, Manage subscription, Redeem promo code, Referral & invite friends).

# Technical Implementation Suggestions
- **Speech Recognition:**
  - Use Apple's Speech framework (SFSpeechRecognizer) for real-time speech-to-text.
  - Implement Siri Shortcut trigger notes taking or push-to-talk listening mode.
  - Parse recognized text for the presence of keywords at the start of a phrase.
  - On keyword detection, extract the keyword and the following content, then save to the corresponding note keywords.
- **Keyword Management:**
  - Store keywords in local storage (UserDefaults or Core Data).
  - Provide UI for users to manage keywords.
- **Note Storage:**
  - Use SwiftData for persistent, local storage of notes and keywords.
  - Each note entry should include: keyword, content, timestamp, and edit/delete status.
- **SwiftUI Integration:**
  - Use SwiftUI for all UI components, leveraging MVVM architecture.
  - Bindings for real-time updates to note lists and categories.


# Non-functional Requirements
- **Performance:**
  - Fast, real-time speech recognition and note creation.
- **Privacy:**
  - All speech processing and note storage should be local by default.
  - Clearly communicate privacy policy to users.
- **Reliability:**
  - Handle interruptions (phone calls, Siri, etc.) gracefully.
- **Battery Efficiency:**
  - Minimize battery usage, especially if continuous listening is enabled.

### Key User Flows
1. **Voice Note Capture**:
   - User taps microphone button
   - App listens for keywords
   - Keyword is detected and highlighted
   - Recording begins with visual feedback
   - User stops recording
   - Note is saved and categorized

2. **Note Browsing**:
   - User navigates to Notes tab
   - Notes are displayed in grid/list view
   - User can filter by keyword or search
   - Tapping a note opens detailed view

3. **Keyword Management**:
   - User navigates to Keywords tab
   - List of keywords with usage statistics is displayed
   - User can add, edit, or delete keywords

4. **Settings Adjustment**:
   - User navigates to Profile tab
   - Settings options are displayed in categories
   - User can adjust preferences and app behavior

# User Stories
- As a user, I want to say "Idea..." and have my thought recorded in an "Idea" note automatically.
- As a user, I want to say "Groceries..." and have my shopping list items saved in a "Groceries" note.
- As a user, I want to customize which keywords trigger new notes.
- As a user, I want to review, edit, and delete my notes later.
- As a user, I want each entry to be timestamped for context.
- As a user, I want to search my notes by keyword or content.



## Performance Requirements
- App should launch in under 2 seconds
- Voice recognition should begin within 1 second of tapping the microphone
- Keyword detection should occur within 2 seconds of speaking
- Note saving should complete in under 1 second
- UI should remain responsive during all operations


## Success Metrics
- User retention rate after 30 days
- Average number of notes created per user per week
- Time spent using the app
- Number of keywords created per user
- App store rating and reviews

## Development Milestones
1. Initial Setup and Core Architecture (Week 1)
2. Data Models and Storage Implementation (Week 2)
3. Voice Recognition and Keyword Detection (Week 3)
4. UI Implementation - Main Screens (Week 4)
5. UI Implementation - Note Management (Week 5)
6. Testing and Bug Fixes (Week 6)
7. Final Polish and App Store Submission (Week 7)



# Appendix
- Apple Speech framework documentation: https://developer.apple.com/documentation/speech
- SwiftUI documentation: https://developer.apple.com/documentation/swiftui
- Example apps: Apple Voice Memos, Otter.ai (for inspiration)



